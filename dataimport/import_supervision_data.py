#!/usr/bin/env python3
"""
Scrip<PERSON> to import supervision data from Excel file into quest.supervision_data table.
Matches faculty using case-insensitive first and last name comparison with uw.faculty table.
"""

import os
import sys
import argparse
import logging
import pandas as pd
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from psycopg2 import sql

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('import_supervision_data')

# Database connection string
DB_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Import supervision data from Excel file')
    parser.add_argument('--file', type=str, default='raw_data/QUEST/SupervisionData_20250428.xlsx',
                        help='Path to the Excel file')
    parser.add_argument('--create-schema', action='store_true',
                        help='Create the quest schema if it does not exist')
    parser.add_argument('--create-table', action='store_true',
                        help='Create the supervision_data table if it does not exist')
    parser.add_argument('--truncate', action='store_true',
                        help='Truncate the table before importing data')
    return parser.parse_args()

def connect_to_db():
    """Connect to the database"""
    try:
        conn = psycopg2.connect(DB_URL)
        conn.autocommit = True
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)

def create_schema(conn):
    """Create the quest schema if it does not exist"""
    try:
        cur = conn.cursor()
        cur.execute("CREATE SCHEMA IF NOT EXISTS quest")
        cur.close()
        logger.info("Schema 'quest' created or already exists")
    except Exception as e:
        logger.error(f"Failed to create schema: {e}")
        sys.exit(1)

def create_table(conn):
    """Create the supervision_data table if it does not exist"""
    try:
        cur = conn.cursor()

        # Check if table exists
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'quest'
                AND table_name = 'supervision_data'
            )
        """)
        table_exists = cur.fetchone()[0]

        if not table_exists:
            # Create the table if it doesn't exist
            cur.execute("""
                CREATE TABLE quest.supervision_data (
                    supervision_id SERIAL PRIMARY KEY,
                    term INTEGER,
                    faculty TEXT,
                    department TEXT,
                    student_id INTEGER,
                    student_name TEXT,
                    academic_plan TEXT,
                    supervisor_last_name TEXT,
                    supervisor_first_name TEXT,
                    citizenship TEXT,
                    email_address TEXT,
                    faculty_id INTEGER,
                    import_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    FOREIGN KEY (faculty_id) REFERENCES uw.faculty(faculty_id)
                )
            """)
            logger.info("Table 'quest.supervision_data' created")
        else:
            # Check if student_id column is INTEGER
            cur.execute("""
                SELECT data_type
                FROM information_schema.columns
                WHERE table_schema = 'quest'
                AND table_name = 'supervision_data'
                AND column_name = 'student_id'
            """)
            data_type = cur.fetchone()[0]

            if data_type.lower() != 'integer':
                logger.info("Altering student_id column from TEXT to INTEGER")
                try:
                    # Alter the column type
                    cur.execute("""
                        ALTER TABLE quest.supervision_data
                        ALTER COLUMN student_id TYPE INTEGER USING (student_id::INTEGER)
                    """)
                    logger.info("Successfully altered student_id column to INTEGER")
                except Exception as e:
                    logger.error(f"Failed to alter student_id column: {e}")
                    logger.info("Continuing with existing column type")
            else:
                logger.info("student_id column is already INTEGER")

        # Create indexes for better performance
        cur.execute("CREATE INDEX IF NOT EXISTS idx_supervision_data_faculty_id ON quest.supervision_data(faculty_id)")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_supervision_data_term ON quest.supervision_data(term)")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_supervision_data_student_id ON quest.supervision_data(student_id)")

        cur.close()
        logger.info("Table 'quest.supervision_data' setup completed")
    except Exception as e:
        logger.error(f"Failed to create/update table: {e}")
        sys.exit(1)

def truncate_table(conn):
    """Truncate the supervision_data table"""
    try:
        cur = conn.cursor()
        cur.execute("TRUNCATE TABLE quest.supervision_data RESTART IDENTITY")
        cur.close()
        logger.info("Table 'quest.supervision_data' truncated")
    except Exception as e:
        logger.error(f"Failed to truncate table: {e}")
        sys.exit(1)

def get_faculty_mapping(conn):
    """Get a mapping of faculty names to faculty_id"""
    try:
        cur = conn.cursor()
        cur.execute("""
            SELECT faculty_id, first_name, last_name
            FROM uw.faculty
            WHERE is_deleted = FALSE
        """)

        faculty_map = {}
        for row in cur.fetchall():
            faculty_id, first_name, last_name = row
            # Create a key with lowercase first and last name for case-insensitive matching
            key = (first_name.lower(), last_name.lower())
            faculty_map[key] = faculty_id

        cur.close()
        logger.info(f"Loaded {len(faculty_map)} faculty members for matching")
        return faculty_map
    except Exception as e:
        logger.error(f"Failed to get faculty mapping: {e}")
        sys.exit(1)

def import_data(conn, file_path, faculty_map):
    """Import data from Excel file into supervision_data table"""
    try:
        # Read the Excel file
        logger.info(f"Reading Excel file: {file_path}")
        df = pd.read_excel(file_path)

        # Check if the file has the expected columns
        expected_columns = [
            'Term', 'Faculty', 'Department', 'Student ID', 'Student Name',
            'Academic Plan', 'Supervisor Last Name', 'Supervisor First Name',
            'Citizenship', 'Eamil Address'
        ]

        # Check if all expected columns are present
        missing_columns = [col for col in expected_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing columns in Excel file: {missing_columns}")
            sys.exit(1)

        # Rename the email column to fix typo in the original data
        df = df.rename(columns={'Eamil Address': 'Email Address'})

        # Create a cursor for database operations
        cur = conn.cursor()

        # Process each row
        matched_count = 0
        unmatched_count = 0
        total_rows = len(df)

        for index, row in df.iterrows():
            # Get faculty information for matching
            supervisor_first_name = str(row['Supervisor First Name']).strip()
            supervisor_last_name = str(row['Supervisor Last Name']).strip()

            # Create a key for faculty matching (lowercase for case-insensitive matching)
            faculty_key = (supervisor_first_name.lower(), supervisor_last_name.lower())

            # Try to find a matching faculty_id
            faculty_id = faculty_map.get(faculty_key)

            if faculty_id:
                matched_count += 1
            else:
                unmatched_count += 1
                logger.warning(f"No matching faculty found for {supervisor_first_name} {supervisor_last_name}")

            # Insert the row into the database
            try:
                # Convert term to integer if possible, otherwise use NULL
                term = None
                try:
                    if pd.notna(row['Term']):
                        term = int(row['Term'])
                except (ValueError, TypeError):
                    logger.warning(f"Invalid term value: {row['Term']} for student {row['Student ID']}")

                # Convert student ID to integer if possible, otherwise use NULL
                student_id = None
                try:
                    if pd.notna(row['Student ID']):
                        student_id = int(row['Student ID'])
                except (ValueError, TypeError):
                    logger.warning(f"Invalid student ID value: {row['Student ID']} - cannot convert to integer")

                cur.execute("""
                    INSERT INTO quest.supervision_data (
                        term, faculty, department, student_id, student_name,
                        academic_plan, supervisor_last_name, supervisor_first_name,
                        citizenship, email_address, faculty_id
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    term,
                    str(row['Faculty']) if pd.notna(row['Faculty']) else None,
                    str(row['Department']) if pd.notna(row['Department']) else None,
                    student_id,
                    str(row['Student Name']) if pd.notna(row['Student Name']) else None,
                    str(row['Academic Plan']) if pd.notna(row['Academic Plan']) else None,
                    supervisor_last_name if pd.notna(supervisor_last_name) else None,
                    supervisor_first_name if pd.notna(supervisor_first_name) else None,
                    str(row['Citizenship']) if pd.notna(row['Citizenship']) else None,
                    str(row['Email Address']) if pd.notna(row['Email Address']) else None,
                    faculty_id
                ))
            except Exception as e:
                logger.error(f"Error inserting row {index}: {e}")
                logger.error(f"Row data: {row.to_dict()}")
                # Continue with the next row instead of exiting
                continue

            # Log progress for every 100 rows
            if (index + 1) % 100 == 0 or index + 1 == total_rows:
                logger.info(f"Processed {index + 1}/{total_rows} rows")

        # Close the cursor
        cur.close()

        # Log summary
        logger.info(f"Import completed: {total_rows} total rows")
        logger.info(f"Faculty matching: {matched_count} matched, {unmatched_count} unmatched")

    except Exception as e:
        logger.error(f"Failed to import data: {e}")
        sys.exit(1)

def main():
    """Main function"""
    args = parse_args()

    # Check if the Excel file exists
    if not os.path.isfile(args.file):
        logger.error(f"Excel file not found: {args.file}")
        sys.exit(1)

    # Connect to the database
    conn = connect_to_db()

    # Create schema if requested
    if args.create_schema:
        create_schema(conn)

    # Create table if requested
    if args.create_table:
        create_table(conn)

    # Truncate table if requested
    if args.truncate:
        truncate_table(conn)

    # Get faculty mapping
    faculty_map = get_faculty_mapping(conn)

    # Import data
    import_data(conn, args.file, faculty_map)

    # Close the database connection
    conn.close()

    logger.info("Import process completed successfully")

if __name__ == "__main__":
    main()
