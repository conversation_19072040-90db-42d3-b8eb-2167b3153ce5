import os
import pandas as pd
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
from datetime import datetime
import re

# Load environment variables
load_dotenv()

# Database connection
DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"
engine = create_engine(DATABASE_URL)

def create_course_section_id(row):
    """Create unique course section ID"""
    return f"{row['Term Code']}{row['Subject']}{row['Course Number']}-{row['Section']}"

def normalize_name(name):
    """Normalize name for comparison"""
    if pd.isna(name):
        return ""
    # Convert to lowercase and remove special characters
    name = str(name).lower()
    name = re.sub(r'[^a-z\s]', '', name)
    # Remove extra whitespace
    name = ' '.join(name.split())
    return name

def find_matching_instructor(instructor_name, instructor_df):
    """Find matching instructor from the CSV file"""
    normalized_name = normalize_name(instructor_name)
    
    # Try exact match first
    match = instructor_df[instructor_df['normalized_name'] == normalized_name]
    if not match.empty:
        return match.iloc[0]
    
    # Try partial match
    for _, row in instructor_df.iterrows():
        if normalized_name in row['normalized_name'] or row['normalized_name'] in normalized_name:
            return row
    
    return None

def import_course_data():
    # Create UW schema if it doesn't exist
    with engine.connect() as connection:
        connection.execute(text("CREATE SCHEMA IF NOT EXISTS uw;"))
        connection.commit()

    # Read Excel file
    excel_file = 'QUEST_course_info.xlsx'
    df = pd.read_excel(excel_file)
    
    # Read instructor CSV file
    instructor_csv = '20250407_Q910_Instructors_teaching_courses_at_campus_location_L_between_terms_S_and_T.csv'
    instructor_df = pd.read_csv(instructor_csv)
    
    # Normalize names in instructor CSV
    instructor_df['normalized_name'] = instructor_df['instructor'].apply(normalize_name)
    
    # Apply filters
    df = df[
        (df['Section'] < 100) &  # Section (L) < 100
        (df['Units'] > 0) &      # Units (Q) > 0
        (df['Enrolment Total'] > 0) &  # Enrolment Total (V) > 0
        (df['Class Meeting Number'] > 0)  # Class Meeting Number (AH) > 0
    ]
    
    # Create course_section_id
    df['course_section_id'] = df.apply(create_course_section_id, axis=1)
    
    # Remove duplicates based on course_section_id
    df = df.drop_duplicates(subset=['course_section_id'], keep='first')

    # Prepare course_info data
    course_info_columns = [
        'course_section_id', 'Term Code', 'Term', 'Session', 'Faculty', 'Department',
        'Course Career', 'Course ID', 'Course Offering Nbr', 'Class Number',
        'Subject', 'Course Number', 'Section', 'Class Component', 'Course Title',
        'Course Topic ID', 'Course Topic Title', 'Units', 'Class Association',
        'Grading Basis', 'Graded Component', 'Enrolment Capacity', 'Enrolment Total',
        'Campus', 'Campus Description', 'Location', 'Location Description',
        'Class Status', 'Class Type', 'Class Note', 'Auto Enrol Section 1',
        'Auto Enrol Section 2', 'Combined Section ID', 'Combined Section Descr',
        'Class Meeting Number', 'Meeting Pattern', 'Start Time', 'End Time',
        'Building', 'Room', 'Room Capacity', 'Room Owner', 'Start Date', 'End Date'
    ]
    
    # Rename columns to match database
    column_mapping = {
        'Term Code': 'term_code',
        'Term': 'term',
        'Session': 'session',
        'Faculty': 'faculty',
        'Department': 'department',
        'Course Career': 'course_career',
        'Course ID': 'course_id',
        'Course Offering Nbr': 'course_offering_nbr',
        'Class Number': 'class_number',
        'Subject': 'subject',
        'Course Number': 'course_number',
        'Section': 'section',
        'Class Component': 'class_component',
        'Course Title': 'course_title',
        'Course Topic ID': 'course_topic_id',
        'Course Topic Title': 'course_topic_title',
        'Units': 'units',
        'Class Association': 'class_association',
        'Grading Basis': 'grading_basis',
        'Graded Component': 'graded_component',
        'Enrolment Capacity': 'enrolment_capacity',
        'Enrolment Total': 'enrolment_total',
        'Campus': 'campus',
        'Campus Description': 'campus_description',
        'Location': 'location',
        'Location Description': 'location_description',
        'Class Status': 'class_status',
        'Class Type': 'class_type',
        'Class Note': 'class_note',
        'Auto Enrol Section 1': 'auto_enrol_section_1',
        'Auto Enrol Section 2': 'auto_enrol_section_2',
        'Combined Section ID': 'combined_section_id',
        'Combined Section Descr': 'combined_section_descr',
        'Class Meeting Number': 'class_meeting_number',
        'Meeting Pattern': 'meeting_pattern',
        'Start Time': 'start_time',
        'End Time': 'end_time',
        'Building': 'building',
        'Room': 'room',
        'Room Capacity': 'room_capacity',
        'Room Owner': 'room_owner',
        'Start Date': 'start_date',
        'End Date': 'end_date'
    }
    
    course_info_df = df[course_info_columns].rename(columns=column_mapping)
    
    # Create instructor records with matching
    instructor_records = []
    unmatched_instructors = set()
    
    for _, row in df.iterrows():
        course_section_id = row['course_section_id']
        # Check each instructor column
        for col in ['Instructor Name', 'Instructor Name.1', 'Instructor Name.2',
                   'Unnamed: 46', 'Unnamed: 47', 'Unnamed: 48', 'Unnamed: 49']:
            if pd.notna(row[col]) and row[col].strip():
                instructor_name = row[col].strip()
                match = find_matching_instructor(instructor_name, instructor_df)
                
                if match is not None:
                    instructor_records.append({
                        'course_section_id': course_section_id,
                        'instructor_name': instructor_name,
                        'instructor_id': match['instructor_id'],
                        'userid': match['userid'],
                        'email': match['email']
                    })
                else:
                    unmatched_instructors.add(instructor_name)
                    instructor_records.append({
                        'course_section_id': course_section_id,
                        'instructor_name': instructor_name,
                        'instructor_id': None,
                        'userid': None,
                        'email': None
                    })
    
    if unmatched_instructors:
        print("\nUnmatched instructors:")
        for name in sorted(unmatched_instructors):
            print(f"- {name}")
    
    instructor_df = pd.DataFrame(instructor_records)
    
    # Import data to database
    try:
        # Import course_info
        course_info_df.to_sql('course_info', engine, if_exists='append', index=False, schema='uw')
        print(f"\nSuccessfully imported {len(course_info_df)} course records")
        
        # Import instructors
        instructor_df.to_sql('course_instructor', engine, if_exists='append', index=False, schema='uw')
        print(f"Successfully imported {len(instructor_df)} instructor records")
        print(f"Matched {len(instructor_df) - len(unmatched_instructors)} instructors")
        print(f"Unmatched {len(unmatched_instructors)} instructors")
        
    except Exception as e:
        print(f"Error importing data: {str(e)}")

if __name__ == "__main__":
    import_course_data() 