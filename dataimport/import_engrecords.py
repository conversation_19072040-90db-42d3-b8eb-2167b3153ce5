import os
import pandas as pd
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine
import glob

# Database connection string
DB_URL = "postgresql://neondb_owner:npg_W27oUDZy<PERSON><PERSON><EMAIL>/ingest?sslmode=require"

def create_schema():
    """Create the engrecords schema"""
    conn = psycopg2.connect(DB_URL)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cur = conn.cursor()
    cur.execute("CREATE SCHEMA IF NOT EXISTS engrecords")
    cur.close()
    conn.close()

def get_csv_files():
    """Get all CSV files from the data_import/EngRecords directory"""
    return glob.glob('data_import/EngRecords/*.csv')

def create_and_import_tables():
    """Create tables and import data from CSV files"""
    engine = create_engine(DB_URL)
    
    for csv_file in get_csv_files():
        # Get table name from file name
        table_name = os.path.splitext(os.path.basename(csv_file))[0]
        schema_table = f'engrecords.{table_name}'
        
        print(f"Processing {table_name}...")
        
        try:
            # Read CSV file
            df = pd.read_csv(csv_file)
            
            # Convert date columns to datetime
            for col in df.columns:
                if 'date' in col.lower() or 'dt' in col.lower():
                    df[col] = pd.to_datetime(df[col], errors='coerce')
            
            # Create table and import data
            df.to_sql(
                name=table_name,
                schema='engrecords',
                con=engine,
                if_exists='replace',
                index=False
            )
            
            print(f"Successfully imported {table_name}")
            
        except Exception as e:
            print(f"Error processing {table_name}: {str(e)}")

def main():
    print("Creating schema...")
    create_schema()
    
    print("Creating tables and importing data...")
    create_and_import_tables()
    
    print("Import completed!")

if __name__ == "__main__":
    main() 