#!/bin/bash

# This script runs the Google Scholar API script to download citation information
# for faculty members in the uw.faculty table with job_family='Regular Faculty'

# Load environment variables
source .env

# Check if SERPAPI_KEY is set
if [ -z "$SERPAPI_KEY" ]; then
    echo "Error: SERPAPI_KEY environment variable is not set"
    echo "Please add SERPAPI_KEY=your_serpapi_api_key to your .env file"
    exit 1
fi

# Check if database connection string is set
if [ -z "$POSTGRES_URL_new" ]; then
    echo "Error: Database connection string is not set"
    echo "Please add POSTGRES_URL_new to your .env file"
    exit 1
fi

# Create results directory
mkdir -p dataimport/google_scholar_results

# Run the script
echo "Starting Google Scholar API script..."
python dataimport/google_scholar_api.py "$@"

# Check exit status
if [ $? -eq 0 ]; then
    echo "Google Scholar API script completed successfully"
else
    echo "Google Scholar API script failed"
    exit 1
fi
