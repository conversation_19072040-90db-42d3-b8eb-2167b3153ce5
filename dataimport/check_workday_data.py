#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the structure and data in the workday.position_details table.
"""

import psycopg2
import os

# Database connection string
DB_URL = os.environ.get(
    "POSTGRES_URL_ingest",
    "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"
)

def main():
    """Main function"""
    # Connect to the database
    conn = psycopg2.connect(DB_URL)
    cur = conn.cursor()
    
    # Check table structure
    print("Table Structure:")
    cur.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'workday' 
        AND table_name = 'position_details' 
        ORDER BY ordinal_position
    """)
    for row in cur.fetchall():
        print(f"  {row[0]}: {row[1]}")
    
    # Check row count
    cur.execute("SELECT COUNT(*) FROM workday.position_details")
    count = cur.fetchone()[0]
    print(f"\nTotal rows: {count}")
    
    # Check sample data
    print("\nSample data:")
    cur.execute("""
        SELECT position_name, reference_id, worker, business_title, job_family, level_04
        FROM workday.position_details
        LIMIT 5
    """)
    for row in cur.fetchall():
        print(f"  Position: {row[0]}")
        print(f"  Reference ID: {row[1]}")
        print(f"  Worker: {row[2]}")
        print(f"  Business Title: {row[3]}")
        print(f"  Job Family: {row[4]}")
        print(f"  Level 04: {row[5]}")
        print()
    
    # Check job family distribution
    print("Job Family Distribution:")
    cur.execute("""
        SELECT job_family, COUNT(*) as count
        FROM workday.position_details
        GROUP BY job_family
        ORDER BY count DESC
    """)
    for row in cur.fetchall():
        print(f"  {row[0]}: {row[1]}")
    
    # Close the database connection
    cur.close()
    conn.close()

if __name__ == "__main__":
    main()
