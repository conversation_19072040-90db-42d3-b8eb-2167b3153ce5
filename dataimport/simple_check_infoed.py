#!/usr/bin/env python3
"""
Simple script to check the infoed.research_funding table.
"""

import psycopg2

# Database connection string
DB_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"

def main():
    """Main function"""
    # Connect to the database
    conn = psycopg2.connect(DB_URL)
    cur = conn.cursor()
    
    # Check row count
    cur.execute("SELECT COUNT(*) FROM infoed.research_funding")
    count = cur.fetchone()[0]
    print(f"Total rows in infoed.research_funding: {count}")
    
    # Check match statistics
    cur.execute("""
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN faculty_id IS NOT NULL THEN 1 END) as matched,
            COUNT(CASE WHEN faculty_id IS NULL THEN 1 END) as unmatched
        FROM infoed.research_funding
    """)
    total, matched, unmatched = cur.fetchone()
    print(f"Match Statistics:")
    print(f"  Total: {total}")
    print(f"  Matched: {matched} ({matched/total*100:.2f}%)")
    print(f"  Unmatched: {unmatched} ({unmatched/total*100:.2f}%)")
    
    # Show some sample matched researchers
    print("\nSample Matched Researchers (top 5):")
    cur.execute("""
        SELECT 
            rf.researcher, 
            f.first_name, 
            f.last_name, 
            rf.match_confidence
        FROM infoed.research_funding rf
        JOIN uw.faculty f ON rf.faculty_id = f.faculty_id
        WHERE rf.faculty_id IS NOT NULL
        ORDER BY rf.match_confidence DESC
        LIMIT 5
    """)
    for row in cur.fetchall():
        researcher, first_name, last_name, match_confidence = row
        print(f"  {researcher} -> {first_name} {last_name} (confidence: {match_confidence}%)")
    
    # Close the connection
    cur.close()
    conn.close()

if __name__ == "__main__":
    main()
