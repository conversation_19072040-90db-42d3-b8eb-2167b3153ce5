-- Create schema if not exists
CREATE SCHEMA IF NOT EXISTS uw;

-- Create course_info table
CREATE TABLE uw.course_info (
    id SERIAL PRIMARY KEY,
    course_section_id VARCHAR(50) UNIQUE NOT NULL,
    term_code VARCHAR(10) NOT NULL,
    term VARCHAR(50),
    session VARCHAR(50),
    faculty VARCHAR(100),
    department VARCHAR(100),
    course_career VARCHAR(50),
    course_id VARCHAR(50),
    course_offering_nbr VARCHAR(10),
    class_number VARCHAR(20),
    subject VARCHAR(10),
    course_number VARCHAR(10),
    section VARCHAR(10),
    class_component VARCHAR(50),
    course_title VARCHAR(200),
    course_topic_id VARCHAR(50),
    course_topic_title VARCHAR(200),
    units DECIMAL(5,2),
    class_association VARCHAR(50),
    grading_basis VARCHAR(50),
    graded_component VARCHAR(50),
    enrolment_capacity INTEGER,
    enrolment_total INTEGER,
    campus VARCHAR(50),
    campus_description VARCHAR(100),
    location VARCHAR(50),
    location_description VARCHAR(100),
    class_status VARCHAR(50),
    class_type VARCHAR(50),
    class_note TEXT,
    auto_enrol_section_1 VARCHAR(50),
    auto_enrol_section_2 VARCHAR(50),
    combined_section_id VARCHAR(50),
    combined_section_descr VARCHAR(200),
    class_meeting_number INTEGER,
    meeting_pattern VARCHAR(50),
    start_time TIME,
    end_time TIME,
    building VARCHAR(50),
    room VARCHAR(50),
    room_capacity INTEGER,
    room_owner VARCHAR(100),
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Drop existing course_instructor table if it exists
DROP TABLE IF EXISTS uw.course_instructor;

-- Create course_instructor table with new columns
CREATE TABLE uw.course_instructor (
    id SERIAL PRIMARY KEY,
    course_section_id VARCHAR(50) NOT NULL REFERENCES uw.course_info(course_section_id),
    instructor_name VARCHAR(200) NOT NULL,
    instructor_id VARCHAR(50),
    userid VARCHAR(50),
    email VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(course_section_id, instructor_name)
);

-- Create index on course_section_id for better performance
CREATE INDEX idx_course_info_course_section_id ON uw.course_info(course_section_id);
CREATE INDEX idx_course_instructor_course_section_id ON uw.course_instructor(course_section_id);
CREATE INDEX idx_course_instructor_instructor_id ON uw.course_instructor(instructor_id);
CREATE INDEX idx_course_instructor_userid ON uw.course_instructor(userid);
CREATE INDEX idx_course_instructor_email ON uw.course_instructor(email); 