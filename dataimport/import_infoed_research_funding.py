#!/usr/bin/env python3
"""
Script to import research funding data from Excel file into infoed.research_funding table.
Matches researchers to faculty in the uw.faculty table using name matching.
"""

import os
import sys
import argparse
import logging
import pandas as pd
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from psycopg2 import sql
from psycopg2.extras import RealDictCursor
from fuzzywuzzy import fuzz, process

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('import_infoed_research_funding')

# Database connection string
DB_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Import research funding data from Excel file')
    parser.add_argument('--file', type=str, default='raw_data/InfoED/ResearchFunding_20240428.xlsx',
                        help='Path to the Excel file')
    parser.add_argument('--create-schema', action='store_true',
                        help='Create the infoed schema if it does not exist')
    parser.add_argument('--create-table', action='store_true',
                        help='Create the research_funding table if it does not exist')
    parser.add_argument('--truncate', action='store_true',
                        help='Truncate the table before importing data')
    parser.add_argument('--fuzzy-threshold', type=int, default=80,
                        help='Threshold for fuzzy matching (0-100, higher is stricter)')
    return parser.parse_args()

def connect_to_db():
    """Connect to the database"""
    try:
        conn = psycopg2.connect(DB_URL)
        conn.autocommit = True
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)

def create_schema(conn):
    """Create the infoed schema if it does not exist"""
    try:
        cur = conn.cursor()
        cur.execute("CREATE SCHEMA IF NOT EXISTS infoed")
        cur.close()
        logger.info("Schema 'infoed' created or already exists")
    except Exception as e:
        logger.error(f"Failed to create schema: {e}")
        sys.exit(1)

def create_table(conn):
    """Create the research_funding table if it does not exist"""
    try:
        cur = conn.cursor()
        cur.execute("""
            CREATE TABLE IF NOT EXISTS infoed.research_funding (
                funding_id SERIAL PRIMARY KEY,
                award_year VARCHAR(100),
                breakout_sponsor_type VARCHAR(100),
                breakout_tri_agency VARCHAR(100),
                associated_department VARCHAR(100),
                faculty VARCHAR(100),
                investigator_role VARCHAR(100),
                original_sponsor VARCHAR(200),
                overall_tri_agency VARCHAR(100),
                project_number VARCHAR(100),
                project_title TEXT,
                researcher VARCHAR(100),
                sponsor_grouping VARCHAR(100),
                sponsor_name VARCHAR(200),
                sponsor_type VARCHAR(100),
                total_award NUMERIC(15, 2),
                work_order VARCHAR(100),
                prime_project_number VARCHAR(100),
                installment VARCHAR(100),
                instrument_type VARCHAR(100),
                number_of_prime_pts_with_awards NUMERIC(10, 2),
                number_of_researchers NUMERIC(10, 2),
                number_of_pis_only NUMERIC(10, 2),
                first_pd NUMERIC(10, 2),
                faculty_id INTEGER,
                match_confidence NUMERIC(5, 2),
                import_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                FOREIGN KEY (faculty_id) REFERENCES uw.faculty(faculty_id)
            )
        """)

        # Create indexes
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_research_funding_researcher
            ON infoed.research_funding(researcher)
        """)

        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_research_funding_faculty_id
            ON infoed.research_funding(faculty_id)
        """)

        cur.close()
        logger.info("Table 'infoed.research_funding' created or already exists")
    except Exception as e:
        logger.error(f"Failed to create table: {e}")
        sys.exit(1)

def truncate_table(conn):
    """Truncate the research_funding table"""
    try:
        cur = conn.cursor()
        cur.execute("TRUNCATE TABLE infoed.research_funding RESTART IDENTITY")
        cur.close()
        logger.info("Table 'infoed.research_funding' truncated")
    except Exception as e:
        logger.error(f"Failed to truncate table: {e}")
        sys.exit(1)

def get_faculty_mapping(conn):
    """Get a mapping of faculty names to faculty_id"""
    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("""
            SELECT faculty_id, first_name, last_name, work_email
            FROM uw.faculty
            WHERE is_deleted = FALSE
        """)

        faculty_records = cur.fetchall()
        cur.close()

        # Create different mappings for different matching strategies
        faculty_map = {}
        faculty_full_names = {}
        faculty_records_list = []

        for record in faculty_records:
            faculty_id = record['faculty_id']
            first_name = record['first_name']
            last_name = record['last_name']

            # Create a key with lowercase first and last name for case-insensitive matching
            key = f"{first_name.lower()} {last_name.lower()}"
            faculty_map[key] = faculty_id

            # Store full names for fuzzy matching
            full_name = f"{first_name} {last_name}"
            faculty_full_names[full_name] = faculty_id

            # Store the complete record for advanced matching
            faculty_records_list.append(record)

        logger.info(f"Loaded {len(faculty_map)} faculty members for matching")
        return {
            'exact_match_map': faculty_map,
            'full_names': faculty_full_names,
            'records': faculty_records_list
        }
    except Exception as e:
        logger.error(f"Failed to get faculty mapping: {e}")
        sys.exit(1)

def match_researcher_to_faculty(researcher_name, faculty_mapping, fuzzy_threshold=80):
    """Match researcher name to faculty using various matching strategies"""
    if not researcher_name or pd.isna(researcher_name):
        return None, 0

    researcher_name = str(researcher_name).strip()

    # Try exact match first (case-insensitive)
    researcher_name_lower = researcher_name.lower()
    if researcher_name_lower in faculty_mapping['exact_match_map']:
        return faculty_mapping['exact_match_map'][researcher_name_lower], 100

    # Try fuzzy matching
    if faculty_mapping['full_names']:
        best_match, score = process.extractOne(
            researcher_name,
            list(faculty_mapping['full_names'].keys()),
            scorer=fuzz.token_sort_ratio
        )

        if score >= fuzzy_threshold:
            return faculty_mapping['full_names'][best_match], score

    # Try matching first and last name separately
    name_parts = researcher_name.split()
    if len(name_parts) >= 2:
        # Try different combinations of first and last name
        for record in faculty_mapping['records']:
            first_name = record['first_name'].lower()
            last_name = record['last_name'].lower()

            # Check if first and last name are in the researcher name
            if first_name in researcher_name_lower and last_name in researcher_name_lower:
                return record['faculty_id'], 90

    return None, 0

def import_data(conn, file_path, faculty_mapping, fuzzy_threshold=80):
    """Import data from Excel file to database"""
    try:
        # Read Excel file
        logger.info(f"Reading Excel file: {file_path}")
        df = pd.read_excel(file_path)
        logger.info(f"Read {len(df)} rows from Excel file")

        # Prepare data for import
        rows_processed = 0
        rows_matched = 0
        rows_not_matched = 0

        cur = conn.cursor()

        for _, row in df.iterrows():
            try:
                # Match researcher to faculty
                researcher_name = row['Researcher']
                faculty_id, match_confidence = match_researcher_to_faculty(
                    researcher_name,
                    faculty_mapping,
                    fuzzy_threshold
                )

                # Track matching statistics
                if faculty_id:
                    rows_matched += 1
                else:
                    rows_not_matched += 1
                    logger.debug(f"No match found for researcher: {researcher_name}")

                # Truncate values if they're too long
                award_year = str(row['Award Year'])[:100] if not pd.isna(row['Award Year']) else None
                breakout_sponsor_type = str(row['Breakout Sponsor Type'])[:100] if not pd.isna(row['Breakout Sponsor Type']) else None
                breakout_tri_agency = str(row['Breakout Tri-Agency'])[:100] if not pd.isna(row['Breakout Tri-Agency']) else None
                associated_department = str(row['Associated Department'])[:100] if not pd.isna(row['Associated Department']) else None
                faculty = str(row['Faculty'])[:100] if not pd.isna(row['Faculty']) else None
                investigator_role = str(row['Investigator Role'])[:100] if not pd.isna(row['Investigator Role']) else None
                original_sponsor = str(row['Original Sponsor'])[:200] if not pd.isna(row['Original Sponsor']) else None
                overall_tri_agency = str(row['Overall Tri-Agency'])[:100] if not pd.isna(row['Overall Tri-Agency']) else None
                project_number = str(row['Project Number'])[:100] if not pd.isna(row['Project Number']) else None
                project_title = str(row['Project Title']) if not pd.isna(row['Project Title']) else None
                researcher = str(row['Researcher'])[:100] if not pd.isna(row['Researcher']) else None
                sponsor_grouping = str(row['Sponsor Grouping'])[:100] if not pd.isna(row['Sponsor Grouping']) else None
                sponsor_name = str(row['Sponsor Name'])[:200] if not pd.isna(row['Sponsor Name']) else None
                sponsor_type = str(row['Sponsor Type'])[:100] if not pd.isna(row['Sponsor Type']) else None
                total_award = float(row['Total Award']) if not pd.isna(row['Total Award']) else None
                work_order = str(row['Work Order'])[:100] if not pd.isna(row['Work Order']) else None
                prime_project_number = str(row['Prime Project Number'])[:100] if not pd.isna(row['Prime Project Number']) else None
                installment = str(row['Installment'])[:100] if not pd.isna(row['Installment']) else None
                instrument_type = str(row['Instrument Type'])[:100] if not pd.isna(row['Instrument Type']) else None
                number_of_prime_pts = float(row['Number of Prime PTs with Awards']) if not pd.isna(row['Number of Prime PTs with Awards']) else None
                number_of_researchers = float(row['Number of Researchers']) if not pd.isna(row['Number of Researchers']) else None
                number_of_pis_only = float(row['Number of PIs only']) if not pd.isna(row['Number of PIs only']) else None
                first_pd = float(row['FirstPd']) if not pd.isna(row['FirstPd']) else None

                # Insert data into database
                cur.execute("""
                    INSERT INTO infoed.research_funding (
                        award_year, breakout_sponsor_type, breakout_tri_agency,
                        associated_department, faculty, investigator_role,
                        original_sponsor, overall_tri_agency, project_number,
                        project_title, researcher, sponsor_grouping,
                        sponsor_name, sponsor_type, total_award,
                        work_order, prime_project_number, installment,
                        instrument_type, number_of_prime_pts_with_awards,
                        number_of_researchers, number_of_pis_only, first_pd,
                        faculty_id, match_confidence
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    award_year, breakout_sponsor_type, breakout_tri_agency,
                    associated_department, faculty, investigator_role,
                    original_sponsor, overall_tri_agency, project_number,
                    project_title, researcher, sponsor_grouping,
                    sponsor_name, sponsor_type, total_award,
                    work_order, prime_project_number, installment,
                    instrument_type, number_of_prime_pts,
                    number_of_researchers, number_of_pis_only, first_pd,
                    faculty_id, match_confidence
                ))

                rows_processed += 1
                if rows_processed % 100 == 0:
                    logger.info(f"Processed {rows_processed} rows")
            except Exception as e:
                logger.error(f"Error processing row {rows_processed + 1}: {e}")
                # Continue with the next row
                continue

        cur.close()

        # Log import statistics
        logger.info(f"Import completed. Processed {rows_processed} rows.")
        logger.info(f"Matched {rows_matched} researchers to faculty ({rows_matched/rows_processed*100:.2f}%).")
        logger.info(f"Could not match {rows_not_matched} researchers ({rows_not_matched/rows_processed*100:.2f}%).")

    except Exception as e:
        logger.error(f"Failed to import data: {e}")
        sys.exit(1)

def main():
    """Main function"""
    args = parse_args()

    # Check if the Excel file exists
    if not os.path.isfile(args.file):
        logger.error(f"Excel file not found: {args.file}")
        sys.exit(1)

    # Connect to the database
    conn = connect_to_db()

    # Create schema if requested
    if args.create_schema:
        create_schema(conn)

    # Create table if requested
    if args.create_table:
        create_table(conn)

    # Truncate table if requested
    if args.truncate:
        truncate_table(conn)

    # Get faculty mapping
    faculty_mapping = get_faculty_mapping(conn)

    # Import data
    import_data(conn, args.file, faculty_mapping, args.fuzzy_threshold)

    # Close the database connection
    conn.close()

    logger.info("Import process completed successfully")

if __name__ == "__main__":
    main()
