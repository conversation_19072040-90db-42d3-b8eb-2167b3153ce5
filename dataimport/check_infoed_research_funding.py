#!/usr/bin/env python3
"""
Script to check the structure and data in the infoed.research_funding table.
"""

import sys
import argparse
import psycopg2
from psycopg2.extras import RealDictCursor

# Database connection string
DB_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Check research funding data')
    parser.add_argument('--show-unmatched', action='store_true',
                        help='Show researchers that could not be matched to faculty')
    parser.add_argument('--show-matched', action='store_true',
                        help='Show researchers that were matched to faculty')
    parser.add_argument('--limit', type=int, default=10,
                        help='Limit the number of results')
    return parser.parse_args()

def main():
    """Main function"""
    args = parse_args()

    # Connect to the database
    try:
        conn = psycopg2.connect(DB_URL)
        cur = conn.cursor(cursor_factory=RealDictCursor)

        # Check table structure
        print("Table Structure:")
        cur.execute("""
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_schema = 'infoed'
            AND table_name = 'research_funding'
            ORDER BY ordinal_position
        """)
        for row in cur.fetchall():
            print(f"  {row['column_name']}: {row['data_type']}")

        # Check row count
        cur.execute("SELECT COUNT(*) as count FROM infoed.research_funding")
        count = cur.fetchone()['count']
        print(f"\nTotal rows in infoed.research_funding: {count}")

        # Check match statistics
        cur.execute("""
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN faculty_id IS NOT NULL THEN 1 END) as matched,
                COUNT(CASE WHEN faculty_id IS NULL THEN 1 END) as unmatched
            FROM infoed.research_funding
        """)
        stats = cur.fetchone()
        print(f"\nMatch Statistics:")
        print(f"  Total: {stats['total']}")
        print(f"  Matched: {stats['matched']} ({stats['matched']/stats['total']*100:.2f}%)")
        print(f"  Unmatched: {stats['unmatched']} ({stats['unmatched']/stats['total']*100:.2f}%)")

        # Show unmatched researchers
        if args.show_unmatched:
            print(f"\nUnmatched Researchers (top {args.limit}):")
            cur.execute("""
                SELECT researcher, award_year, project_title, total_award
                FROM infoed.research_funding
                WHERE faculty_id IS NULL
                ORDER BY total_award DESC NULLS LAST
                LIMIT %s
            """, (args.limit,))
            for row in cur.fetchall():
                researcher = row['researcher'] or 'Unknown'
                award_year = row['award_year'] or 'Unknown'
                project_title = row['project_title'] or 'No title'
                total_award = row['total_award'] or 0
                print(f"  {researcher} - {award_year} - ${total_award:,.2f} - {project_title[:50]}...")

        # Show matched researchers
        if args.show_matched:
            print(f"\nMatched Researchers (top {args.limit}):")
            cur.execute("""
                SELECT
                    rf.researcher,
                    f.first_name,
                    f.last_name,
                    rf.match_confidence,
                    rf.award_year,
                    rf.total_award
                FROM infoed.research_funding rf
                JOIN uw.faculty f ON rf.faculty_id = f.faculty_id
                WHERE rf.faculty_id IS NOT NULL
                ORDER BY rf.match_confidence DESC, rf.total_award DESC NULLS LAST
                LIMIT %s
            """, (args.limit,))
            for row in cur.fetchall():
                researcher = row['researcher'] or 'Unknown'
                first_name = row['first_name'] or ''
                last_name = row['last_name'] or ''
                match_confidence = row['match_confidence'] or 0
                award_year = row['award_year'] or 'Unknown'
                total_award = row['total_award'] or 0
                print(f"  {researcher} -> {first_name} {last_name} (confidence: {match_confidence}%) - {award_year} - ${total_award:,.2f}")

        # Close the connection
        cur.close()
        conn.close()

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
