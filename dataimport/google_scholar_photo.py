#!/usr/bin/env python3
"""
Google Scholar Photo Downloader

This script downloads faculty photos from Google Scholar using the scholar_id
from the googlescholar.author_profile table.

Usage:
    python google_scholar_photo.py [--limit N] [--faculty-id ID] [--verbose] [--force-update]

Options:
    --limit N        Limit processing to N faculty members
    --faculty-id ID  Process only the faculty member with the given ID
    --verbose        Enable verbose logging
    --force-update   Force update all photos, even if they already have a profile_image_url
"""

import os
import sys
import time
import argparse
import logging
from typing import Dict, List, Optional

import psycopg2
from psycopg2.extras import RealDictCursor
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('google_scholar_photo')

# Database configuration
DB_URL = os.getenv('POSTGRES_URL_new')

if not DB_URL:
    logger.error("Database connection string is not set")
    sys.exit(1)

# Create photos directory in public/faculty/avatar
# Get the project root directory (assuming dataimport is in the project root)
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PHOTOS_DIR = os.path.join(PROJECT_ROOT, 'public', 'faculty', 'avatar')
os.makedirs(PHOTOS_DIR, exist_ok=True)
logger.info(f"Photos will be saved to: {PHOTOS_DIR}")

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Download Google Scholar photos for faculty members')
    parser.add_argument('--limit', type=int, help='Limit processing to N faculty members')
    parser.add_argument('--faculty-id', type=int, help='Process only the faculty member with the given ID')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--force-update', action='store_true', help='Force update all photos, even if they already have a profile_image_url')
    parser.add_argument('--retry', type=int, default=3, help='Number of retries for failed downloads')
    parser.add_argument('--delay', type=int, default=5, help='Base delay between retries in seconds')
    parser.add_argument('--wait', type=int, default=2, help='Wait time between processing faculty members in seconds')
    return parser.parse_args()

def connect_to_db(connection_string: str) -> psycopg2.extensions.connection:
    """Connect to the database"""
    try:
        conn = psycopg2.connect(connection_string)
        conn.autocommit = True
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)

def get_faculty_with_scholar_id(conn: psycopg2.extensions.connection, faculty_id: Optional[int] = None,
                               limit: Optional[int] = None, force_update: bool = False) -> List[Dict]:
    """Get faculty members with Google Scholar IDs"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            query = """
                SELECT ap.author_id, ap.scholar_id, ap.faculty_id, f.first_name, f.last_name, ap.profile_image_url
                FROM googlescholar.author_profile ap
                JOIN uw.faculty f ON ap.faculty_id = f.faculty_id
                WHERE ap.scholar_id IS NOT NULL
            """

            params = []

            if not force_update:
                query += " AND (ap.profile_image_url IS NULL OR ap.profile_image_url = '')"

            if faculty_id:
                query += " AND ap.faculty_id = %s"
                params.append(faculty_id)

            query += " ORDER BY ap.faculty_id"

            if limit:
                query += " LIMIT %s"
                params.append(limit)

            cur.execute(query, params)
            return cur.fetchall()
    except Exception as e:
        logger.error(f"Failed to get faculty members with Google Scholar IDs: {e}")
        return []

def download_photo(scholar_id: str, faculty_id: int, verbose: bool = False, max_retries: int = 3, retry_delay: int = 5) -> Optional[str]:
    """Download faculty photo from Google Scholar"""
    # Note: verbose parameter is kept for API compatibility but not used
    # Construct the photo URL
    photo_url = f"https://scholar.googleusercontent.com/citations?view_op=view_photo&user={scholar_id}&citpid=1"

    # Create a filename for the photo using faculty_id to make it more identifiable
    photo_filename = f"{faculty_id}.jpg"
    photo_path = os.path.join(PHOTOS_DIR, photo_filename)

    # Download the photo with retries
    logger.info(f"Downloading photo for faculty_id: {faculty_id}, scholar_id: {scholar_id}")

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': f'https://scholar.google.com/citations?user={scholar_id}'
    }

    for attempt in range(max_retries):
        try:
            # Use requests to download the photo
            response = requests.get(photo_url, stream=True, headers=headers)
            response.raise_for_status()

            # Check if the response is an image
            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                logger.warning(f"Response is not an image for scholar_id {scholar_id}: {content_type}")
                return None

            # Save the photo
            with open(photo_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"Saved photo to {photo_path}")

            # Return the relative path to the photo (relative to the public directory)
            return f"/faculty/avatar/{photo_filename}"

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:  # Too Many Requests
                wait_time = retry_delay * (attempt + 1)
                logger.warning(f"Rate limited (429). Waiting {wait_time} seconds before retry {attempt + 1}/{max_retries}")
                time.sleep(wait_time)
            else:
                logger.error(f"HTTP error downloading photo for scholar_id {scholar_id}: {e}")
                return None
        except Exception as e:
            logger.error(f"Failed to download photo for scholar_id {scholar_id}: {e}")
            return None

    logger.error(f"Failed to download photo for scholar_id {scholar_id} after {max_retries} retries")
    return None

def update_profile_image_url(conn: psycopg2.extensions.connection, scholar_id: str, image_path: str) -> bool:
    """Update profile_image_url in the database"""
    try:
        with conn.cursor() as cur:
            query = """
                UPDATE googlescholar.author_profile
                SET profile_image_url = %s, last_updated = NOW()
                WHERE scholar_id = %s
            """
            cur.execute(query, (image_path, scholar_id))
            return True
    except Exception as e:
        logger.error(f"Failed to update profile_image_url for scholar_id {scholar_id}: {e}")
        return False

def save_log(conn: psycopg2.extensions.connection, faculty_id: int, scholar_id: str,
            log_type: str, status: str, message: Optional[str] = None) -> bool:
    """Save log to the database"""
    try:
        with conn.cursor() as cur:
            query = """
                INSERT INTO googlescholar.scrape_log
                (faculty_id, scholar_id, log_type, status, message)
                VALUES (%s, %s, %s, %s, %s)
            """
            cur.execute(query, (faculty_id, scholar_id, log_type, status, message))
            return True
    except Exception as e:
        logger.error(f"Failed to save log: {e}")
        return False

def main():
    """Main function"""
    args = parse_args()

    # Set logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # Connect to the database
    conn = connect_to_db(DB_URL)

    # Get faculty members with Google Scholar IDs
    faculty_members = get_faculty_with_scholar_id(
        conn,
        faculty_id=args.faculty_id,
        limit=args.limit,
        force_update=args.force_update
    )

    logger.info(f"Found {len(faculty_members)} faculty members with Google Scholar IDs")

    # Process each faculty member
    for faculty in faculty_members:
        scholar_id = faculty['scholar_id']
        faculty_id = faculty['faculty_id']
        first_name = faculty['first_name']
        last_name = faculty['last_name']

        logger.info(f"Processing {first_name} {last_name} (scholar_id: {scholar_id})")

        # Download photo with retry logic
        image_path = download_photo(
            scholar_id,
            faculty_id,
            verbose=args.verbose,
            max_retries=args.retry,
            retry_delay=args.delay
        )

        if image_path:
            # Update profile_image_url in the database
            if update_profile_image_url(conn, scholar_id, image_path):
                logger.info(f"Updated profile_image_url for {first_name} {last_name}")
                save_log(conn, faculty_id, scholar_id, 'photo', 'success')
            else:
                logger.error(f"Failed to update profile_image_url for {first_name} {last_name}")
                save_log(conn, faculty_id, scholar_id, 'photo', 'failed', "Failed to update profile_image_url")
        else:
            logger.error(f"Failed to download photo for {first_name} {last_name}")
            save_log(conn, faculty_id, scholar_id, 'photo', 'failed', "Failed to download photo")

        # Add a delay to avoid overloading the server
        wait_time = args.wait
        logger.debug(f"Waiting {wait_time} seconds before processing next faculty member")
        time.sleep(wait_time)

    # Close the database connection
    conn.close()

    logger.info("Done")

if __name__ == "__main__":
    main()
