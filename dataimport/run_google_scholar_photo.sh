#!/bin/bash

# This script runs the Google Scholar Photo Downloader script to download faculty photos
# from Google Scholar for faculty members with scholar_id in the googlescholar.author_profile table.

# Load environment variables
source .env

# Check if database connection string is set
if [ -z "$POSTGRES_URL_new" ]; then
    echo "Error: Database connection string is not set"
    echo "Please add POSTGRES_URL_new to your .env file"
    exit 1
fi

# Create photos directory in public/faculty/avatar
mkdir -p public/faculty/avatar

# Run the script
echo "Starting Google Scholar Photo Downloader script..."
python dataimport/google_scholar_photo.py "$@"

# Check exit status
if [ $? -eq 0 ]; then
    echo "Google Scholar Photo Downloader script completed successfully"
else
    echo "Google Scholar Photo Downloader script failed"
    exit 1
fi
