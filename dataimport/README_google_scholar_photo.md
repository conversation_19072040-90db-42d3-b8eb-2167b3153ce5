# Google Scholar Photo Downloader

This script downloads faculty photos from Google Scholar using the scholar_id from the googlescholar.author_profile table.

## Prerequisites

1. Python 3.6 or higher
2. PostgreSQL database with the googlescholar schema created
3. Faculty data with scholar_id in the googlescholar.author_profile table

## Setup

1. Make sure the googlescholar schema and tables are already created:

```bash
psql -h your-database-host -p 5432 -U your-username -W -d your-database -f ../dbscripts/ddl/institution/googlescholar.sql
```

2. Install required Python packages:

```bash
pip install psycopg2-binary requests python-dotenv
```

3. Make sure your `.env` file in the project root contains the database connection string:

```
POSTGRES_URL_new=postgresql://username:password@hostname:port/database?sslmode=require
```

## Usage

Run the script with the following command:

```bash
python google_scholar_photo.py [options]
```

Or use the shell script:

```bash
./run_google_scholar_photo.sh [options]
```

### Options

- `--limit N`: Limit processing to N faculty members
- `--faculty-id ID`: Process only the faculty member with the given ID
- `--verbose`: Enable verbose logging
- `--force-update`: Force update all photos, even if they already have a profile_image_url
- `--retry N`: Number of retries for failed downloads (default: 3)
- `--delay N`: Base delay between retries in seconds (default: 5)
- `--wait N`: Wait time between processing faculty members in seconds (default: 2)

### Examples

Download photos for all faculty members without a profile_image_url:

```bash
python google_scholar_photo.py
```

Download photos for only 10 faculty members:

```bash
python google_scholar_photo.py --limit 10
```

Download photo for a specific faculty member:

```bash
python google_scholar_photo.py --faculty-id 123
```

Force update all photos, even if they already have a profile_image_url:

```bash
python google_scholar_photo.py --force-update
```

Enable verbose logging:

```bash
python google_scholar_photo.py --verbose
```

Customize retry logic and wait times:

```bash
python google_scholar_photo.py --retry 5 --delay 10 --wait 3
```

Combine multiple options:

```bash
python google_scholar_photo.py --faculty-id 123 --force-update --verbose --retry 5 --delay 10
```

## How It Works

1. The script queries the googlescholar.author_profile table to get faculty members with scholar_id.
2. For each scholar_id, it constructs the photo URL: `https://scholar.googleusercontent.com/citations?view_op=view_photo&user={scholar_id}&citpid=1`
3. It downloads the photo and saves it to the `public/faculty/avatar` directory, with retry logic for handling rate limiting.
4. The photos are saved using the faculty_id as the filename (e.g., `123.jpg`) for easy identification.
5. It updates the profile_image_url field in the googlescholar.author_profile table with the relative path to the photo (e.g., `/faculty/avatar/123.jpg`).
6. It logs the results to the googlescholar.scrape_log table.

## Notes

- The script will only process faculty members who have a scholar_id in the googlescholar.author_profile table.
- By default, it will only process faculty members who don't have a profile_image_url set.
- Use the `--force-update` option to process all faculty members, even if they already have a profile_image_url.
- The script adds a delay between requests to avoid overloading the Google Scholar server (configurable with `--wait`).
- The script includes retry logic for handling rate limiting (configurable with `--retry` and `--delay`).
- Photos are saved in the `public/faculty/avatar` directory with the faculty_id as the filename (e.g., `123.jpg`).
- The profile_image_url in the database is updated to point to the local file path (e.g., `/faculty/avatar/123.jpg`) which can be directly used in the web application.
- The script uses a custom User-Agent and Referer headers to mimic a browser request, which helps avoid being blocked by Google Scholar.
