import pandas as pd

# File paths
csv_file = '20250407_Q910_Instructors_teaching_courses_at_campus_location_L_between_terms_S_and_T.csv'
excel_file = 'UW_ENG_employees_20250401.xlsx'

# Read CSV file and extract email addresses
csv_df = pd.read_csv(csv_file)
csv_emails = set(csv_df['email'].dropna().astype(str).str.strip().str.lower())

# Read Excel file and extract email addresses from 'Email - Work' column
excel_df = pd.read_excel(excel_file)
excel_emails = set(excel_df['Email - Work'].dropna().astype(str).str.strip().str.lower())

print("\nSummary:")
print(f"Total unique email addresses in CSV: {len(csv_emails)}")
print(f"Total unique email addresses in Excel: {len(excel_emails)}")

# Find emails that are in CSV but not in Excel
missing_emails = csv_emails - excel_emails
if missing_emails:
    print(f"\nEmails found in CSV but not in Excel ({len(missing_emails)} emails):")
    for email in sorted(missing_emails):
        print(f"- {email}")
else:
    print("\nAll email addresses from CSV exist in the Excel file!") 