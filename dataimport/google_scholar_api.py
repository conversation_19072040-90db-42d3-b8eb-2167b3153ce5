#!/usr/bin/env python3
"""
Google Scholar API Script

This script downloads citation information from Google Scholar using SerpAPI
for faculty members in the uw.faculty table with job_family='Regular Faculty'.

Usage:
    python google_scholar_api.py [--limit N] [--faculty-id ID] [--scholar-id ID] [--verbose]

Options:
    --limit N        Limit processing to N faculty members
    --faculty-id ID  Process only the faculty member with the given ID
    --scholar-id ID  Directly specify a Google Scholar ID to use (requires --faculty-id)
    --verbose        Enable verbose logging
"""

import os
import sys
import json
import time
import argparse
import logging
import urllib.parse
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

import psycopg2
from psycopg2.extras import RealDictCursor
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('google_scholar_api')

# SerpAPI configuration
SERPAPI_KEY = os.getenv('SERPAPI_KEY')
if not SERPAPI_KEY:
    logger.error("SERPAPI_KEY environment variable is not set")
    sys.exit(1)

# Database configuration
DB_URL = os.getenv('POSTGRES_URL_new')

if not DB_URL:
    logger.error("Database connection string is not set")
    sys.exit(1)

# Create results directory
RESULTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'google_scholar_results')
os.makedirs(RESULTS_DIR, exist_ok=True)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Download Google Scholar data for faculty members')
    parser.add_argument('--limit', type=int, help='Limit processing to N faculty members')
    parser.add_argument('--faculty-id', type=int, help='Process only the faculty member with the given ID')
    parser.add_argument('--scholar-id', type=str, help='Directly specify a Google Scholar ID to use (requires --faculty-id)')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--force-update', action='store_true', help='Force update all faculty members, even if they already have a successful search')
    return parser.parse_args()

def connect_to_db(connection_string: str) -> psycopg2.extensions.connection:
    """Connect to the database"""
    try:
        conn = psycopg2.connect(connection_string)
        conn.autocommit = True
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)

def get_faculty_members(conn: psycopg2.extensions.connection, faculty_id: Optional[int] = None, limit: Optional[int] = None) -> List[Dict]:
    """Get faculty members from the database"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            query = """
                SELECT faculty_id, first_name, last_name, work_email
                FROM uw.faculty
                WHERE job_family = 'Regular Faculty'
                AND is_deleted = FALSE
            """
            params = []

            if faculty_id:
                query += " AND faculty_id = %s"
                params.append(faculty_id)

            query += " ORDER BY faculty_id"

            if limit:
                query += " LIMIT %s"
                params.append(limit)

            cur.execute(query, params)
            return cur.fetchall()
    except Exception as e:
        logger.error(f"Failed to get faculty members: {e}")
        return []

def search_google_scholar(first_name: str, last_name: str, verbose: bool = False) -> Optional[Dict]:
    """Search for a faculty member on Google Scholar using SerpAPI"""
    search_query = f"{first_name} {last_name} waterloo"
    logger.info(f"Searching Google Scholar for: {search_query}")

    try:
        # Prepare the API request
        params = {
            "engine": "google_scholar_profiles",
            "mauthors": search_query,
            "api_key": SERPAPI_KEY
        }

        # Make the API request
        response = requests.get("https://serpapi.com/search", params=params)
        response.raise_for_status()
        data = response.json()

        # Save response to file for debugging
        if verbose:
            results_path = os.path.join(RESULTS_DIR, f"search_{first_name}_{last_name}.json")
            with open(results_path, 'w') as f:
                json.dump(data, f, indent=2)
            logger.debug(f"Saved search results to {results_path}")

        # Check if there are any profiles
        if "profiles" not in data or not data["profiles"]:
            logger.info(f"No profiles found for {search_query}")
            return None

        # Return the first profile (most relevant)
        return data
    except Exception as e:
        logger.error(f"Failed to search Google Scholar: {e}")
        return None

def get_author_page(author_id: str, start: int = 0, verbose: bool = False) -> Optional[Dict]:
    """Get a single page of author details from Google Scholar using SerpAPI"""
    try:
        # Prepare the API request
        params = {
            "engine": "google_scholar_author",
            "author_id": author_id,
            "api_key": SERPAPI_KEY
        }

        # Add pagination parameters if not the first page
        if start > 0:
            params["start"] = start

        # Make the API request
        response = requests.get("https://serpapi.com/search", params=params)
        response.raise_for_status()
        data = response.json()

        # Save response to file for debugging
        if verbose:
            results_path = os.path.join(RESULTS_DIR, f"details_{author_id}_page_{start}.json")
            with open(results_path, 'w') as f:
                json.dump(data, f, indent=2)
            logger.debug(f"Saved author details page {start} to {results_path}")

        return data
    except Exception as e:
        logger.error(f"Failed to get author details page {start}: {e}")
        return None

def get_author_details(author_id: str, verbose: bool = False) -> Optional[Dict]:
    """Get all author details from Google Scholar using SerpAPI with pagination"""
    logger.info(f"Getting author details for: {author_id}")

    try:
        # Get the first page
        first_page = get_author_page(author_id, 0, verbose)
        if not first_page:
            return None

        # Initialize the result with the first page
        result = first_page

        # Check if there are more pages
        all_articles = first_page.get("articles", [])

        # Continue fetching pages until there are no more
        page_num = 1
        while "serpapi_pagination" in first_page and "next" in first_page["serpapi_pagination"]:
            # Extract the next page URL from the response
            next_url = first_page["serpapi_pagination"]["next"]

            # Parse the start parameter from the URL
            parsed_url = urllib.parse.urlparse(next_url)
            query_params = urllib.parse.parse_qs(parsed_url.query)

            if "start" not in query_params:
                logger.warning(f"Could not find 'start' parameter in next URL: {next_url}")
                break

            next_page_start = int(query_params["start"][0])
            logger.info(f"Fetching additional publications page {page_num} for {author_id}, starting at {next_page_start}")

            # Add a small delay to avoid rate limiting
            time.sleep(1)

            # Get the next page
            next_page = get_author_page(author_id, next_page_start, verbose)
            if not next_page or "articles" not in next_page or not next_page["articles"]:
                logger.warning(f"No more articles found on page {page_num} for {author_id}")
                break

            # Add articles from the next page to the result
            new_articles = next_page.get("articles", [])
            logger.info(f"Found {len(new_articles)} more articles on page {page_num}")
            all_articles.extend(new_articles)

            # Update the first page with the next page's pagination info
            first_page = next_page
            page_num += 1

        # Update the result with all articles
        result["articles"] = all_articles
        logger.info(f"Retrieved a total of {len(all_articles)} publications for {author_id}")

        # Save the complete result for debugging
        if verbose:
            complete_results_path = os.path.join(RESULTS_DIR, f"details_{author_id}_complete.json")
            with open(complete_results_path, 'w') as f:
                json.dump(result, f, indent=2)
            logger.debug(f"Saved complete author details to {complete_results_path}")

        return result
    except Exception as e:
        logger.error(f"Failed to get author details: {e}")
        return None

def save_faculty_search(conn: psycopg2.extensions.connection, faculty_id: int, first_name: str, last_name: str,
                        search_query: str, scholar_id: Optional[str] = None) -> Optional[int]:
    """Save faculty search information to the database"""
    try:
        with conn.cursor() as cur:
            query = """
                INSERT INTO googlescholar.faculty_search
                (faculty_id, first_name, last_name, search_query, scholar_id, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING search_id
            """
            cur.execute(query, (faculty_id, first_name, last_name, search_query, scholar_id, 'pending'))
            return cur.fetchone()[0]
    except Exception as e:
        logger.error(f"Failed to save faculty search: {e}")
        return None

def update_faculty_search(conn: psycopg2.extensions.connection, search_id: int, status: str,
                          scholar_id: Optional[str] = None, error_message: Optional[str] = None) -> bool:
    """Update faculty search status in the database"""
    try:
        with conn.cursor() as cur:
            query = """
                UPDATE googlescholar.faculty_search
                SET status = %s, scholar_id = %s, error_message = %s
                WHERE search_id = %s
            """
            cur.execute(query, (status, scholar_id, error_message, search_id))
            return True
    except Exception as e:
        logger.error(f"Failed to update faculty search: {e}")
        return False

def save_author_profile(conn: psycopg2.extensions.connection, faculty_id: int, author_data: Dict) -> Optional[int]:
    """Save author profile to the database"""
    try:
        # Extract data from the author profile
        # The SerpAPI response has a different structure
        author = author_data.get("author", {})
        if not author and "profiles" in author_data:
            # If we're using the search results directly
            profile = author_data["profiles"][0]
            author_id = profile.get("author_id")
            name = profile.get("name", "")
            affiliation = profile.get("affiliations", "")
            interests = profile.get("interests", [])
            citations_all = profile.get("cited_by", 0)
            citations_since_2019 = 0
            h_index_all = 0
            h_index_since_2019 = 0
            i10_index_all = 0
            i10_index_since_2019 = 0
        else:
            # If we're using the author details
            author_id = author_data.get("search_parameters", {}).get("author_id")
            name = author.get("name", "")
            affiliation = author.get("affiliations", "")
            interests = author.get("interests", [])

            # Extract citation metrics
            cited_by = author_data.get("cited_by", {})
            table = cited_by.get("table", [])

            citations_all = 0
            citations_since_2019 = 0
            h_index_all = 0
            h_index_since_2019 = 0
            i10_index_all = 0
            i10_index_since_2019 = 0

            if table and len(table) >= 3:
                citations = table[0].get("citations", {})
                h_index = table[1].get("h_index", {})
                i10_index = table[2].get("i10_index", {})

                citations_all = citations.get("all", 0)
                citations_since_2019 = citations.get("since_2020", 0)  # Note: SerpAPI uses since_2020 now
                h_index_all = h_index.get("all", 0)
                h_index_since_2019 = h_index.get("since_2020", 0)
                i10_index_all = i10_index.get("all", 0)
                i10_index_since_2019 = i10_index.get("since_2020", 0)

        if isinstance(affiliation, list):
            affiliation = ", ".join(affiliation)

        # Get profile URL
        profile_url = f"https://scholar.google.com/citations?user={author_id}"

        # Get interests
        areas_of_interest = ", ".join([interest.get("title", "") for interest in interests]) if interests else ""

        with conn.cursor() as cur:
            query = """
                INSERT INTO googlescholar.author_profile
                (scholar_id, faculty_id, name, affiliation, areas_of_interest,
                citations_all, citations_since_2019, h_index_all, h_index_since_2019,
                i10_index_all, i10_index_since_2019, profile_url)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (scholar_id)
                DO UPDATE SET
                    faculty_id = EXCLUDED.faculty_id,
                    name = EXCLUDED.name,
                    affiliation = EXCLUDED.affiliation,
                    areas_of_interest = EXCLUDED.areas_of_interest,
                    citations_all = EXCLUDED.citations_all,
                    citations_since_2019 = EXCLUDED.citations_since_2019,
                    h_index_all = EXCLUDED.h_index_all,
                    h_index_since_2019 = EXCLUDED.h_index_since_2019,
                    i10_index_all = EXCLUDED.i10_index_all,
                    i10_index_since_2019 = EXCLUDED.i10_index_since_2019,
                    profile_url = EXCLUDED.profile_url,
                    last_updated = NOW()
                RETURNING author_id
            """
            cur.execute(query, (
                author_id, faculty_id, name, affiliation, areas_of_interest,
                citations_all, citations_since_2019, h_index_all, h_index_since_2019,
                i10_index_all, i10_index_since_2019, profile_url
            ))
            return cur.fetchone()[0]
    except Exception as e:
        logger.error(f"Failed to save author profile: {e}")
        return None

def save_publications(conn: psycopg2.extensions.connection, scholar_id: str, articles: List[Dict]) -> bool:
    """Save publications to the database"""
    try:
        with conn.cursor() as cur:
            for article in articles:
                title = article.get("title", "")
                authors = article.get("authors", "")
                venue = article.get("publication", "")
                year = article.get("year")
                if year and str(year).isdigit():
                    year = int(year)
                else:
                    year = None

                cited_by = article.get("cited_by", {})
                citations = cited_by.get("value", 0) if isinstance(cited_by, dict) else 0
                link = article.get("link")
                citation_id = article.get("citation_id")

                # Use citation_id as the primary identifier if available
                if citation_id:
                    # Check if publication already exists by citation_id
                    cur.execute(
                        "SELECT publication_id FROM googlescholar.publication WHERE scholar_id = %s AND citation_id = %s",
                        (scholar_id, citation_id)
                    )
                    existing = cur.fetchone()

                    if existing:
                        # Update existing publication
                        cur.execute(
                            """
                            UPDATE googlescholar.publication SET
                                title = %s,
                                authors = %s,
                                venue = %s,
                                year = %s,
                                citations = %s,
                                publication_url = %s,
                                last_updated = NOW()
                            WHERE publication_id = %s
                            """,
                            (title, authors, venue, year, citations, link, existing[0])
                        )
                    else:
                        # Insert new publication with citation_id
                        cur.execute(
                            """
                            INSERT INTO googlescholar.publication
                            (scholar_id, citation_id, title, authors, venue, year, citations, publication_url)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                            """,
                            (scholar_id, citation_id, title, authors, venue, year, citations, link)
                        )
                else:
                    # Fallback to using title if citation_id is not available
                    # Check if publication already exists by title
                    cur.execute(
                        "SELECT publication_id FROM googlescholar.publication WHERE scholar_id = %s AND title = %s",
                        (scholar_id, title)
                    )
                    existing = cur.fetchone()

                    if existing:
                        # Update existing publication
                        cur.execute(
                            """
                            UPDATE googlescholar.publication SET
                                authors = %s,
                                venue = %s,
                                year = %s,
                                citations = %s,
                                publication_url = %s,
                                last_updated = NOW()
                            WHERE publication_id = %s
                            """,
                            (authors, venue, year, citations, link, existing[0])
                        )
                    else:
                        # Insert new publication without citation_id
                        cur.execute(
                            """
                            INSERT INTO googlescholar.publication
                            (scholar_id, title, authors, venue, year, citations, publication_url)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """,
                            (scholar_id, title, authors, venue, year, citations, link)
                        )
        return True
    except Exception as e:
        logger.error(f"Failed to save publications: {e}")
        return False

def save_citation_history(conn: psycopg2.extensions.connection, author_id: int, citations_data: Dict) -> bool:
    """Save citation history to the database"""
    try:
        if not citations_data:
            return False

        # Check if we have graph data in the SerpAPI response
        citations_over_time = {}
        if "graph" in citations_data:
            for entry in citations_data.get("graph", []):
                year = entry.get("year")
                citations = entry.get("citations", 0)
                if year and str(year).isdigit():
                    citations_over_time[str(year)] = citations

        with conn.cursor() as cur:
            for year, count in citations_over_time.items():
                if year.isdigit():
                    cur.execute(
                        """
                        INSERT INTO googlescholar.citation_history
                        (author_id, year, citations)
                        VALUES (%s, %s, %s)
                        ON CONFLICT (author_id, year)
                        DO UPDATE SET
                            citations = EXCLUDED.citations,
                            last_updated = NOW()
                        """,
                        (author_id, int(year), count)
                    )
        return True
    except Exception as e:
        logger.error(f"Failed to save citation history: {e}")
        return False

def save_co_authors(conn: psycopg2.extensions.connection, author_id: int, co_authors_data: List[Dict]) -> bool:
    """Save co-authors to the database"""
    try:
        if not co_authors_data:
            return False

        with conn.cursor() as cur:
            for co_author in co_authors_data:
                name = co_author.get("name", "")
                co_author_id = co_author.get("author_id")
                affiliation = co_author.get("affiliations", "")

                # Skip if no co_author_id
                if not co_author_id:
                    continue

                cur.execute(
                    """
                    INSERT INTO googlescholar.co_author
                    (author_id, co_author_name, co_author_scholar_id, co_author_affiliation)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (co_author_id)
                    DO UPDATE SET
                        co_author_name = EXCLUDED.co_author_name,
                        co_author_scholar_id = EXCLUDED.co_author_scholar_id,
                        co_author_affiliation = EXCLUDED.co_author_affiliation,
                        last_updated = NOW()
                    """,
                    (author_id, name, co_author_id, affiliation)
                )
        return True
    except Exception as e:
        logger.error(f"Failed to save co-authors: {e}")
        return False

def process_author_data(conn: psycopg2.extensions.connection, faculty_id: int, scholar_id: str,
                     author_id: int, author_details: Dict) -> bool:
    """Process author data including publications, citations, and co-authors"""
    try:
        # Save publications
        articles = author_details.get("articles", [])
        if articles:
            if save_publications(conn, scholar_id, articles):
                save_log(conn, faculty_id, scholar_id, "publications", "success")
            else:
                save_log(conn, faculty_id, scholar_id, "publications", "failed", "Failed to save publications")

        # Save citation history
        if save_citation_history(conn, author_id, author_details.get("cited_by", {})):
            save_log(conn, faculty_id, scholar_id, "citations", "success")
        else:
            save_log(conn, faculty_id, scholar_id, "citations", "failed", "Failed to save citation history")

        # Save co-authors
        co_authors = author_details.get("co_authors", [])
        if co_authors:
            if save_co_authors(conn, author_id, co_authors):
                save_log(conn, faculty_id, scholar_id, "co-authors", "success")
            else:
                save_log(conn, faculty_id, scholar_id, "co-authors", "failed", "Failed to save co-authors")

        return True
    except Exception as e:
        logger.error(f"Failed to process author data: {e}")
        return False

def save_log(conn: psycopg2.extensions.connection, faculty_id: Optional[int], scholar_id: Optional[str],
             log_type: str, status: str, message: Optional[str] = None) -> bool:
    """Save log entry to the database"""
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                INSERT INTO googlescholar.scrape_log
                (faculty_id, scholar_id, log_type, status, message)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (faculty_id, scholar_id, log_type, status, message)
            )
        return True
    except Exception as e:
        logger.error(f"Failed to save log: {e}")
        return False

def check_existing_faculty_search(conn: psycopg2.extensions.connection, faculty_id: int) -> Optional[str]:
    """Check if a faculty member already has a successful search record

    Returns:
        Optional[str]: The scholar_id if a successful search exists, None otherwise
    """
    try:
        with conn.cursor() as cur:
            # Check for a successful search with a scholar_id
            cur.execute(
                """
                SELECT scholar_id
                FROM googlescholar.faculty_search
                WHERE faculty_id = %s AND status = 'completed' AND scholar_id IS NOT NULL
                ORDER BY search_date DESC
                LIMIT 1
                """,
                (faculty_id,)
            )
            result = cur.fetchone()
            if result and result[0]:
                return result[0]
            return None
    except Exception as e:
        logger.error(f"Failed to check existing faculty search: {e}")
        return None

def check_existing_author_profile(conn: psycopg2.extensions.connection, scholar_id: str) -> Optional[int]:
    """Check if an author profile already exists for a scholar_id

    Returns:
        Optional[int]: The author_id if a profile exists, None otherwise
    """
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                SELECT author_id
                FROM googlescholar.author_profile
                WHERE scholar_id = %s
                LIMIT 1
                """,
                (scholar_id,)
            )
            result = cur.fetchone()
            if result and result[0]:
                return result[0]
            return None
    except Exception as e:
        logger.error(f"Failed to check existing author profile: {e}")
        return None

def get_publication_count(conn: psycopg2.extensions.connection, scholar_id: str) -> int:
    """Get the number of publications for a scholar_id

    Returns:
        int: The number of publications
    """
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                SELECT COUNT(*)
                FROM googlescholar.publication
                WHERE scholar_id = %s
                """,
                (scholar_id,)
            )
            result = cur.fetchone()
            if result and result[0]:
                return result[0]
            return 0
    except Exception as e:
        logger.error(f"Failed to get publication count: {e}")
        return 0

def process_faculty_member(conn: psycopg2.extensions.connection, faculty: Dict, verbose: bool = False,
                     force_update: bool = False, specified_scholar_id: Optional[str] = None) -> bool:
    """Process a single faculty member"""
    faculty_id = faculty['faculty_id']
    first_name = faculty['first_name']
    last_name = faculty['last_name']

    logger.info(f"Processing faculty member: {first_name} {last_name} (ID: {faculty_id})")

    # If a scholar_id is directly specified, use it instead of searching
    if specified_scholar_id:
        logger.info(f"Using specified Google Scholar ID: {specified_scholar_id} for {first_name} {last_name}")

        # Save faculty search record with the specified scholar_id
        search_query = f"Direct specification of scholar_id: {specified_scholar_id}"
        search_id = save_faculty_search(conn, faculty_id, first_name, last_name, search_query, specified_scholar_id)
        if not search_id:
            logger.error(f"Failed to save faculty search record for {first_name} {last_name}")
            return False

        # Update faculty search record with scholar ID
        update_faculty_search(conn, search_id, "completed", specified_scholar_id)
        save_log(conn, faculty_id, specified_scholar_id, "search", "success", "Using directly specified scholar_id")

        # Get author details
        author_details = get_author_details(specified_scholar_id, verbose)

        if not author_details:
            logger.error(f"Failed to get author details for {first_name} {last_name} with specified ID: {specified_scholar_id}")
            save_log(conn, faculty_id, specified_scholar_id, "profile", "failed", "Failed to get author details")
            return False

        # Save author profile
        db_author_id = save_author_profile(conn, faculty_id, author_details)
        if not db_author_id:
            logger.error(f"Failed to save author profile for {first_name} {last_name}")
            save_log(conn, faculty_id, specified_scholar_id, "profile", "failed", "Failed to save author profile")
            return False

        save_log(conn, faculty_id, specified_scholar_id, "profile", "success")

        # Process the author data (publications, citations, co-authors)
        process_author_data(conn, faculty_id, specified_scholar_id, db_author_id, author_details)

        logger.info(f"Successfully processed faculty member: {first_name} {last_name} with specified scholar_id: {specified_scholar_id}")
        return True

    # Normal flow when no scholar_id is specified
    # Check if faculty already has a successful search
    existing_scholar_id = check_existing_faculty_search(conn, faculty_id)
    if existing_scholar_id and not force_update:
        logger.info(f"Faculty member {first_name} {last_name} already has a successful search with scholar_id: {existing_scholar_id}")

        # Check if author profile exists
        existing_author_id = check_existing_author_profile(conn, existing_scholar_id)
        if existing_author_id:
            logger.info(f"Author profile already exists for {first_name} {last_name} with author_id: {existing_author_id}")

            # Check the publication count
            pub_count = get_publication_count(conn, existing_scholar_id)
            logger.info(f"Found {pub_count} existing publications for {first_name} {last_name}")

            # Only update if publication count is exactly 20 (default page size)
            if pub_count == 20:
                logger.info(f"Publication count is exactly 20, which suggests there might be more publications. Updating data for {first_name} {last_name}")

                # Get author details to update publications
                author_details = get_author_details(existing_scholar_id, verbose)
                if author_details:
                    # Update author profile and publications
                    db_author_id = save_author_profile(conn, faculty_id, author_details)
                    if db_author_id:
                        # Process publications, citations, and co-authors
                        process_author_data(conn, faculty_id, existing_scholar_id, db_author_id, author_details)
                        return True

                # If we can't get updated author details, just return success since we already have data
                logger.info(f"Using existing data for {first_name} {last_name} since author details couldn't be updated")
                return True
            else:
                logger.info(f"Publication count is not 20 (it's {pub_count}), skipping update for {first_name} {last_name}")
                return True

    # Create search query
    search_query = f"{first_name} {last_name} waterloo"

    # Save faculty search record
    search_id = save_faculty_search(conn, faculty_id, first_name, last_name, search_query)
    if not search_id:
        logger.error(f"Failed to save faculty search record for {first_name} {last_name}")
        return False

    # Search for faculty member on Google Scholar
    search_results = search_google_scholar(first_name, last_name, verbose)

    if not search_results or "profiles" not in search_results or not search_results["profiles"]:
        logger.info(f"No Google Scholar profile found for {first_name} {last_name}")
        update_faculty_search(conn, search_id, "completed", None, "No profiles found")
        save_log(conn, faculty_id, None, "search", "failed", "No profiles found")
        return False

    # Get the first (most relevant) profile
    profile = search_results["profiles"][0]
    author_id = profile.get("author_id")

    if not author_id:
        logger.error(f"No author ID found for {first_name} {last_name}")
        update_faculty_search(conn, search_id, "failed", None, "No author ID found")
        save_log(conn, faculty_id, None, "search", "failed", "No author ID found")
        return False

    # Update faculty search record with scholar ID
    update_faculty_search(conn, search_id, "completed", author_id)
    save_log(conn, faculty_id, author_id, "search", "success")

    # Get author details
    author_details = get_author_details(author_id, verbose)

    if not author_details:
        logger.error(f"Failed to get author details for {first_name} {last_name} (ID: {author_id})")
        save_log(conn, faculty_id, author_id, "profile", "failed", "Failed to get author details")
        return False

    # Save author profile
    db_author_id = save_author_profile(conn, faculty_id, author_details)
    if not db_author_id:
        logger.error(f"Failed to save author profile for {first_name} {last_name}")
        save_log(conn, faculty_id, author_id, "profile", "failed", "Failed to save author profile")
        return False

    save_log(conn, faculty_id, author_id, "profile", "success")

    # Process the author data (publications, citations, co-authors)
    process_author_data(conn, faculty_id, author_id, db_author_id, author_details)

    logger.info(f"Successfully processed faculty member: {first_name} {last_name}")
    return True

def main():
    """Main function"""
    args = parse_args()

    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # Connect to database
    conn = connect_to_db(DB_URL)

    try:
        # Validate arguments
        if args.scholar_id and not args.faculty_id:
            logger.error("--scholar-id requires --faculty-id to be specified")
            return

        # Get faculty members
        faculty_members = get_faculty_members(conn, args.faculty_id, args.limit)

        if not faculty_members:
            logger.error("No faculty members found")
            return

        logger.info(f"Found {len(faculty_members)} faculty members to process")

        # Process each faculty member
        for faculty in faculty_members:
            # If scholar_id is specified and this is the faculty member we want, use the specified scholar_id
            specified_scholar_id = None
            if args.scholar_id and faculty['faculty_id'] == args.faculty_id:
                specified_scholar_id = args.scholar_id
                logger.info(f"Using specified scholar_id {specified_scholar_id} for faculty ID {args.faculty_id}")

            process_faculty_member(conn, faculty, args.verbose, args.force_update, specified_scholar_id)

            # Add a delay to avoid rate limiting
            time.sleep(2)

        logger.info("Processing completed")

    finally:
        # Close database connection
        conn.close()

if __name__ == "__main__":
    main()
