#!/usr/bin/env python3
"""
Script to import Workday position data from Excel file into the workday schema.
"""

import os
import sys
import argparse
import logging
import pandas as pd
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('import_workday_positions')

# Database connection string - will be overridden by environment variable if available
DB_URL = os.environ.get(
    "POSTGRES_URL_ingest",
    "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"
)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Import Workday position data from Excel file')
    parser.add_argument('--file', type=str, default='raw_data/workday/UW_ENG_Position_Details_20250301.xlsx',
                        help='Path to the Excel file')
    parser.add_argument('--create-schema', action='store_true',
                        help='Create the workday schema if it does not exist')
    parser.add_argument('--create-table', action='store_true',
                        help='Create the position_details table if it does not exist')
    parser.add_argument('--truncate', action='store_true',
                        help='Truncate the table before importing data')
    parser.add_argument('--skip-rows', type=int, default=1,
                        help='Number of rows to skip in the Excel file (default: 1)')
    return parser.parse_args()

def connect_to_db():
    """Connect to the database"""
    try:
        conn = psycopg2.connect(DB_URL)
        conn.autocommit = True
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)

def create_schema(conn):
    """Create the workday schema if it does not exist"""
    try:
        cur = conn.cursor()
        cur.execute("CREATE SCHEMA IF NOT EXISTS workday")
        cur.close()
        logger.info("Schema 'workday' created or already exists")
    except Exception as e:
        logger.error(f"Failed to create schema: {e}")
        sys.exit(1)

def create_table(conn):
    """Create the position_details table if it does not exist"""
    try:
        cur = conn.cursor()
        
        # Create the table with columns matching the Excel file
        cur.execute("""
            CREATE TABLE IF NOT EXISTS workday.position_details (
                id SERIAL PRIMARY KEY,
                position_name TEXT,
                reference_id INTEGER,
                worker_type TEXT,
                employee_type TEXT,
                time_type TEXT,
                staffing_status TEXT,
                available_for_hire TEXT,
                worker TEXT,
                contract_end_date DATE,
                business_title TEXT,
                job_profile TEXT,
                frozen TEXT,
                freeze_date DATE,
                freeze_reason TEXT,
                previous_incumbent TEXT,
                position_vacate_date DATE,
                fte NUMERIC(5,2),
                job_family TEXT,
                job_family_groups TEXT,
                cost_center TEXT,
                class_indicator TEXT,
                manager TEXT,
                supervisory_organization TEXT,
                level_03 TEXT,
                level_04 TEXT,
                level_05 TEXT,
                level_06 TEXT,
                level_07 TEXT,
                import_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        
        # Create indexes for commonly queried fields
        cur.execute("CREATE INDEX IF NOT EXISTS idx_workday_reference_id ON workday.position_details(reference_id)")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_workday_worker ON workday.position_details(worker)")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_workday_job_family ON workday.position_details(job_family)")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_workday_level_04 ON workday.position_details(level_04)")
        
        cur.close()
        logger.info("Table 'workday.position_details' created or already exists")
    except Exception as e:
        logger.error(f"Failed to create table: {e}")
        sys.exit(1)

def truncate_table(conn):
    """Truncate the position_details table"""
    try:
        cur = conn.cursor()
        cur.execute("TRUNCATE TABLE workday.position_details")
        cur.close()
        logger.info("Table 'workday.position_details' truncated")
    except Exception as e:
        logger.error(f"Failed to truncate table: {e}")
        sys.exit(1)

def import_data(conn, file_path, skip_rows=1):
    """Import data from Excel file into position_details table"""
    try:
        # Read the Excel file
        logger.info(f"Reading Excel file: {file_path}")
        df = pd.read_excel(file_path, skiprows=skip_rows)
        logger.info(f"Read {len(df)} rows from Excel file")
        
        # Rename columns to match database table
        column_mapping = {
            'Position and Job - All Staffing Models': 'position_name',
            'Reference ID': 'reference_id',
            'Worker Type': 'worker_type',
            'Position - Employee Type': 'employee_type',
            'Time Type': 'time_type',
            'Staffing Status': 'staffing_status',
            'Available For Hire': 'available_for_hire',
            'Worker': 'worker',
            'Contract End Date': 'contract_end_date',
            'Business Title': 'business_title',
            'Job Profile': 'job_profile',
            'Frozen': 'frozen',
            'Freeze Date': 'freeze_date',
            'Freeze Reason': 'freeze_reason',
            'Previous Incumbent': 'previous_incumbent',
            'Position Vacate Date': 'position_vacate_date',
            'FTE': 'fte',
            'Job Family': 'job_family',
            'Job Family Groups': 'job_family_groups',
            'Cost Center': 'cost_center',
            'Class Indicator': 'class_indicator',
            'Manager': 'manager',
            'Supervisory Organization': 'supervisory_organization',
            'Level 03 from the Top': 'level_03',
            'Level 04 from the Top': 'level_04',
            'Level 05 from the Top': 'level_05',
            'Level 06 from the Top': 'level_06',
            'Level 07 from the Top': 'level_07'
        }
        
        df = df.rename(columns=column_mapping)
        
        # Create SQLAlchemy engine for bulk insert
        engine = create_engine(DB_URL)
        
        # Import data to database
        df.to_sql('position_details', engine, schema='workday', if_exists='append', index=False)
        
        logger.info(f"Successfully imported {len(df)} rows to workday.position_details")
    except Exception as e:
        logger.error(f"Failed to import data: {e}")
        sys.exit(1)

def main():
    """Main function"""
    args = parse_args()

    # Check if the Excel file exists
    if not os.path.isfile(args.file):
        logger.error(f"Excel file not found: {args.file}")
        sys.exit(1)

    # Connect to the database
    conn = connect_to_db()

    # Create schema if requested
    if args.create_schema:
        create_schema(conn)

    # Create table if requested
    if args.create_table:
        create_table(conn)

    # Truncate table if requested
    if args.truncate:
        truncate_table(conn)

    # Import data
    import_data(conn, args.file, args.skip_rows)

    # Close the database connection
    conn.close()

    logger.info("Import process completed successfully")

if __name__ == "__main__":
    main()
