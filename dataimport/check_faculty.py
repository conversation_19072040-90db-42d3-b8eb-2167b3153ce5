#!/usr/bin/env python3
"""
Check faculty information in the database
"""

import os
import sys
from dotenv import load_dotenv
import psycopg2

# Load environment variables
load_dotenv()

# Database configuration
DB_URL = os.getenv('POSTGRES_URL_new')

if not DB_URL:
    print("Database connection string is not set")
    sys.exit(1)

# Get faculty_id from command line
faculty_id = sys.argv[1] if len(sys.argv) > 1 else None
if not faculty_id:
    print("Please provide a faculty_id as a command line argument")
    sys.exit(1)

try:
    # Connect to the database
    conn = psycopg2.connect(DB_URL)
    cur = conn.cursor()
    
    # Query faculty information
    cur.execute("""
        SELECT f.faculty_id, f.first_name, f.last_name, f.work_email, f.sso_id
        FROM uw.faculty f
        WHERE f.faculty_id = %s
    """, (faculty_id,))
    
    faculty = cur.fetchone()
    if faculty:
        faculty_id, first_name, last_name, work_email, sso_id = faculty
        print(f"Faculty Information:")
        print(f"  ID: {faculty_id}")
        print(f"  Name: {first_name} {last_name}")
        print(f"  Email: {work_email}")
        print(f"  SSO ID: {sso_id}")
    else:
        print(f"No faculty found with ID {faculty_id}")
        sys.exit(1)
    
    # Query Google Scholar profile
    cur.execute("""
        SELECT ap.scholar_id, ap.profile_image_url, ap.name, ap.affiliation
        FROM googlescholar.author_profile ap
        WHERE ap.faculty_id = %s
    """, (faculty_id,))
    
    scholar = cur.fetchone()
    if scholar:
        scholar_id, profile_image_url, name, affiliation = scholar
        print(f"\nGoogle Scholar Profile:")
        print(f"  Scholar ID: {scholar_id}")
        print(f"  Profile Image URL: {profile_image_url}")
        print(f"  Name: {name}")
        print(f"  Affiliation: {affiliation}")
    else:
        print(f"\nNo Google Scholar profile found for faculty ID {faculty_id}")
    
    # Close the connection
    cur.close()
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
