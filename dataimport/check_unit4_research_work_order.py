#!/usr/bin/env python3
"""
Scrip<PERSON> to check the structure and data in the unit4.research_work_order table.
"""

import sys
import argparse
import psycopg2
from psycopg2.extras import RealDictCursor

# Database connection string
DB_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Check research work order data')
    parser.add_argument('--limit', type=int, default=10,
                        help='Limit the number of results')
    return parser.parse_args()

def main():
    """Main function"""
    args = parse_args()
    
    # Connect to the database
    try:
        conn = psycopg2.connect(DB_URL)
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check table structure
        print("Table Structure:")
        cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'unit4' 
            AND table_name = 'research_work_order' 
            ORDER BY ordinal_position
        """)
        for row in cur.fetchall():
            print(f"  {row['column_name']}: {row['data_type']}")
        
        # Check row count
        cur.execute("SELECT COUNT(*) as count FROM unit4.research_work_order")
        count = cur.fetchone()['count']
        print(f"\nTotal rows in unit4.research_work_order: {count}")
        
        # Check org unit distribution
        print("\nOrganization Unit Distribution:")
        cur.execute("""
            SELECT org_unit_name, COUNT(*) as count
            FROM unit4.research_work_order
            GROUP BY org_unit_name
            ORDER BY count DESC
            LIMIT %s
        """, (args.limit,))
        for row in cur.fetchall():
            print(f"  {row['org_unit_name']}: {row['count']}")
        
        # Check fund distribution
        print("\nFund Distribution:")
        cur.execute("""
            SELECT fund_name, COUNT(*) as count
            FROM unit4.research_work_order
            GROUP BY fund_name
            ORDER BY count DESC
            LIMIT %s
        """, (args.limit,))
        for row in cur.fetchall():
            print(f"  {row['fund_name']}: {row['count']}")
        
        # Show sample data
        print(f"\nSample Data (top {args.limit}):")
        cur.execute("""
            SELECT 
                work_order, 
                org_unit_name, 
                pi_name, 
                project_name, 
                net_funds
            FROM unit4.research_work_order
            ORDER BY net_funds DESC NULLS LAST
            LIMIT %s
        """, (args.limit,))
        for row in cur.fetchall():
            pi_name = row['pi_name'] or 'N/A'
            net_funds = row['net_funds'] or 0
            print(f"  {row['work_order']} - {row['org_unit_name']} - {pi_name} - ${net_funds:,.2f} - {row['project_name'][:50]}...")
        
        # Close the connection
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
