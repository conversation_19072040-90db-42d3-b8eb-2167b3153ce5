import pandas as pd
from fuzzywuzzy import process

def normalize_name(name):
    """Normalize a name by converting it to a sorted set of parts, ignoring case and commas."""
    # Convert to lowercase and remove commas
    name = name.lower().replace(',', '')
    # Split into parts and sort them
    parts = sorted(name.split())
    # Join back together
    return ' '.join(parts)

# Function to find close matches using fuzzy matching
def find_close_matches(name, name_set, threshold=80):
    """Find names in name_set that are similar to the given name based on a threshold."""
    matches = process.extract(name, name_set, limit=5)
    return [match for match, score in matches if score >= threshold]

# Read the Excel file
excel_file = 'QUEST_course_info.xlsx'
csv_file = '20250407_Q910_Instructors_teaching_courses_at_campus_location_L_between_terms_S_and_T.csv'

# Read Excel file and extract names from specified columns
excel_df = pd.read_excel(excel_file)
instructor_columns = ['Instructor Name', 'Instructor Name.1', 'Instructor Name.2', 
                     'Unnamed: 46', 'Unnamed: 47', 'Unnamed: 48', 'Unnamed: 49']
excel_names = set()
for col in instructor_columns:
    if col in excel_df.columns:
        # Get non-null values from the column and convert to set
        names = set(excel_df[col].dropna().astype(str).str.strip())
        # Remove empty strings and normalize names
        names = {normalize_name(name) for name in names if name and name != 'nan'}
        excel_names.update(names)

# Read CSV file
csv_df = pd.read_csv(csv_file)
csv_names = set(csv_df['instructor'].dropna().astype(str).str.strip())
csv_names = {normalize_name(name) for name in csv_names if name and name != 'nan'}

print("\nSummary:")
print(f"Total unique instructor names from Excel: {len(excel_names)}")
print(f"Total unique instructor names from CSV: {len(csv_names)}")

# Show some sample names from both files
print("\nSample names from Excel file (first 5):")
print(sorted(list(excel_names))[:5])
print("\nSample names from CSV file (first 5):")
print(sorted(list(csv_names))[:5])

# Find names that are in Excel but not in CSV (case insensitive)
missing_names = excel_names - csv_names
if missing_names:
    print(f"\nAll {len(missing_names)} names found in Excel but not in CSV:")
    for name in sorted(missing_names):
        print(f"- {name}")
        # Find close matches for each missing name
        close_matches = find_close_matches(name, csv_names)
        if close_matches:
            print(f"  Close matches in CSV: {', '.join(close_matches)}")
else:
    print("\nAll names from Excel exist in the CSV file!")

# Find some examples of matches
matches = excel_names & csv_names
if matches:
    print(f"\nFound {len(matches)} matching names between Excel and CSV") 