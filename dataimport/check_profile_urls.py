#!/usr/bin/env python3
"""
Check profile image URLs in the database
"""

import os
import sys
from dotenv import load_dotenv
import psycopg2

# Load environment variables
load_dotenv()

# Database configuration
DB_URL = os.getenv('POSTGRES_URL_new')

if not DB_URL:
    print("Database connection string is not set")
    sys.exit(1)

try:
    # Connect to the database
    conn = psycopg2.connect(DB_URL)
    cur = conn.cursor()
    
    # Query the database
    cur.execute("""
        SELECT f.faculty_id, f.first_name, f.last_name, ap.scholar_id, ap.profile_image_url
        FROM googlescholar.author_profile ap
        JOIN uw.faculty f ON ap.faculty_id = f.faculty_id
        WHERE ap.profile_image_url IS NOT NULL
        ORDER BY f.faculty_id
        LIMIT 10
    """)
    
    # Print the results
    print("Faculty ID | Name | Scholar ID | Profile Image URL")
    print("-" * 80)
    
    for row in cur.fetchall():
        faculty_id, first_name, last_name, scholar_id, profile_image_url = row
        print(f"{faculty_id} | {first_name} {last_name} | {scholar_id} | {profile_image_url}")
    
    # Close the connection
    cur.close()
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
