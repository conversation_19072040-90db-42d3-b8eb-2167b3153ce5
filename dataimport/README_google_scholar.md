# Google Scholar API Integration

This directory contains scripts for downloading citation information from Google Scholar using SerpAPI for faculty members in the uw.faculty table with job_family='Regular Faculty'.

## Prerequisites

1. Python 3.6 or higher
2. PostgreSQL database with the googlescholar schema created
3. SerpAPI API key

## Setup

1. Create the database schema and tables:

```bash
psql -h ep-crimson-star-a4puf5oa-pooler.us-east-1.aws.neon.tech -p 5432 -U neondb_owner -W -d ameliadb -f ../dbscripts/ddl/institution/googlescholar.sql
```

2. Install required Python packages:

```bash
pip install psycopg2-binary requests python-dotenv
```

3. Create a `.env` file in the project root with the following content:

```
POSTGRES_URL_new=postgresql://username:password@hostname:port/ameliadb?sslmode=require
SERPAPI_KEY=your_serpapi_api_key
```

## Usage

Run the script with the following command:

```bash
python google_scholar_api.py [options]
```

### Options

- `--limit N`: Limit processing to N faculty members
- `--faculty-id ID`: Process only the faculty member with the given ID
- `--verbose`: Enable verbose logging
- `--force-update`: Force update all faculty members, even if they already have a successful search

### Examples

Process all faculty members:

```bash
python google_scholar_api.py
```

Process only the first 5 faculty members:

```bash
python google_scholar_api.py --limit 5
```

Process a specific faculty member:

```bash
python google_scholar_api.py --faculty-id 123
```

Enable verbose logging:

```bash
python google_scholar_api.py --verbose
```

Force update all faculty members, even if they already have a successful search:

```bash
python google_scholar_api.py --force-update
```

Combine options:

```bash
python google_scholar_api.py --faculty-id 123 --force-update --verbose
```

## Data Structure

The script stores data in the following tables in the googlescholar schema:

1. `faculty_search`: Records of faculty search attempts
2. `author_profile`: Google Scholar author profiles
3. `publication`: Publications by authors
4. `co_author`: Co-authors of publications
5. `citation_history`: Citation history by year
6. `scrape_log`: Logs of scraping activities

## SerpAPI Integration

This script uses [SerpAPI](https://serpapi.com/google-scholar-api) to access Google Scholar data. SerpAPI provides a reliable way to access Google Scholar without being blocked by CAPTCHAs or IP bans.

The script uses the following SerpAPI endpoints:

1. `google_scholar_profiles`: To search for faculty members
2. `google_scholar_author`: To get author details and publications

The script implements pagination to fetch ALL publications for each faculty member, not just the default 20 publications. This ensures that the complete publication record is captured for each faculty member.

The script also intelligently handles previously downloaded faculty members:
- If a faculty member already has publications in the database, the script checks the publication count.
- If the count is exactly 20 (the default page size), the script assumes there might be more publications and updates the data.
- If the count is not 20, the script assumes all publications have been downloaded and skips the update to avoid unnecessary API calls.

## Troubleshooting

If you encounter any issues:

1. Check that your SerpAPI API key is valid and has sufficient credits
2. Ensure database connection strings are correct
3. Check the logs for detailed error messages
4. Try running with the `--verbose` flag for more detailed logging
