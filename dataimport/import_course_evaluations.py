import os
import pandas as pd
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine

# Database connection string
DB_URL = "postgresql://neondb_owner:npg_W27oUDZyK<PERSON><EMAIL>/ameliadb?sslmode=require"

def create_schema():
    """Create the perceptions schema"""
    conn = psycopg2.connect(DB_URL)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cur = conn.cursor()
    cur.execute("CREATE SCHEMA IF NOT EXISTS perceptions")
    cur.close()
    conn.close()

def import_course_evaluations():
    """Import course evaluations data from CSV"""
    engine = create_engine(DB_URL)
    
    # Read CSV file
    df = pd.read_csv('raw_data/perceptions/courseEvaluations_data_rev.csv')
    
    # Clean up column names
    df.columns = [col.strip().lower() for col in df.columns]
    
    # Rename the course_title column
    df = df.rename(columns={"'xxx' as course_title": "course_title"})
    
    # Convert numeric columns to appropriate types
    numeric_columns = [
        'term_id', 'section', 'class_size',
        'q1_responses', 'q1_avg', 'q1_std',
        'q2_responses', 'q2_avg', 'q2_std',
        'q3_responses', 'q3_avg', 'q3_std',
        'q4_responses', 'q4_avg', 'q4_std',
        'q5_responses', 'q5_avg', 'q5_std',
        'q6_responses', 'q6_avg', 'q6_std'
    ]
    
    for col in numeric_columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Create table and import data
    df.to_sql(
        name='course_evaluations',
        schema='perceptions',
        con=engine,
        if_exists='replace',
        index=False
    )
    
    print("Successfully imported course evaluations data")

def main():
    print("Creating schema...")
    create_schema()
    
    print("Importing course evaluations data...")
    import_course_evaluations()
    
    print("Import completed!")

if __name__ == "__main__":
    main() 