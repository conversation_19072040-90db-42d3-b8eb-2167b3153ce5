#!/usr/bin/env python3
"""
Script to import research work order data from text file into unit4.research_work_order table.
Skips non-related rows at the beginning and end of the file.
"""

import os
import sys
import argparse
import logging
import re
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from psycopg2 import sql
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('import_unit4_research_work_order')

# Database connection string
DB_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Import research work order data from text file')
    parser.add_argument('--file', type=str, default='raw_data/unit4/accounts_20250428.txt',
                        help='Path to the text file')
    parser.add_argument('--create-schema', action='store_true',
                        help='Create the unit4 schema if it does not exist')
    parser.add_argument('--create-table', action='store_true',
                        help='Create the research_work_order table if it does not exist')
    parser.add_argument('--truncate', action='store_true',
                        help='Truncate the table before importing data')
    return parser.parse_args()

def connect_to_db():
    """Connect to the database"""
    try:
        conn = psycopg2.connect(DB_URL)
        conn.autocommit = True
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)

def create_schema(conn):
    """Create the unit4 schema if it does not exist"""
    try:
        cur = conn.cursor()
        cur.execute("CREATE SCHEMA IF NOT EXISTS unit4")
        cur.close()
        logger.info("Schema 'unit4' created or already exists")
    except Exception as e:
        logger.error(f"Failed to create schema: {e}")
        sys.exit(1)

def create_table(conn):
    """Create the research_work_order table if it does not exist"""
    try:
        cur = conn.cursor()
        cur.execute("""
            CREATE TABLE IF NOT EXISTS unit4.research_work_order (
                id SERIAL PRIMARY KEY,
                activity VARCHAR(50),
                act_name VARCHAR(255),
                anticipated_end_date TIMESTAMP WITH TIME ZONE,
                client VARCHAR(50),
                concur VARCHAR(5),
                end_date TIMESTAMP WITH TIME ZONE,
                fund VARCHAR(50),
                fund_name VARCHAR(255),
                grs VARCHAR(5),
                hr VARCHAR(5),
                org_unit VARCHAR(50),
                org_unit_name VARCHAR(255),
                parent_wo_name TEXT,
                parent_work_order VARCHAR(50),
                pi_eid VARCHAR(50),
                pi_name VARCHAR(255),
                pi_watiam VARCHAR(50),
                project VARCHAR(50),
                project_name TEXT,
                research_compliance VARCHAR(5),
                rfa_eid VARCHAR(50),
                rfa_name VARCHAR(255),
                rfa_watiam VARCHAR(50),
                sa_eid VARCHAR(50),
                sa_name VARCHAR(255),
                sa_watiam VARCHAR(50),
                start_date TIMESTAMP WITH TIME ZONE,
                status VARCHAR(5),
                wo_manager_eid VARCHAR(50),
                wo_manager_name VARCHAR(255),
                wo_manager_watiam VARCHAR(50),
                work_order VARCHAR(50),
                work_order_name TEXT,
                work_request VARCHAR(50),
                activity_balance VARCHAR(50),
                work_order_balance VARCHAR(50),
                client_balance VARCHAR(50),
                future_budget NUMERIC(15, 2),
                net_funds NUMERIC(15, 2),
                import_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)

        # Create indexes
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_research_work_order_work_order
            ON unit4.research_work_order(work_order)
        """)

        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_research_work_order_org_unit
            ON unit4.research_work_order(org_unit)
        """)

        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_research_work_order_pi_name
            ON unit4.research_work_order(pi_name)
        """)

        cur.close()
        logger.info("Table 'unit4.research_work_order' created or already exists")
    except Exception as e:
        logger.error(f"Failed to create table: {e}")
        sys.exit(1)

def truncate_table(conn):
    """Truncate the research_work_order table"""
    try:
        cur = conn.cursor()
        cur.execute("TRUNCATE TABLE unit4.research_work_order RESTART IDENTITY")
        cur.close()
        logger.info("Table 'unit4.research_work_order' truncated")
    except Exception as e:
        logger.error(f"Failed to truncate table: {e}")
        sys.exit(1)

def parse_text_file(file_path):
    """Parse the text file and extract data rows"""
    try:
        logger.info(f"Reading text file: {file_path}")

        with open(file_path, 'r') as file:
            content = file.readlines()

        # Find the start of the data (after the header row with column names)
        start_index = None
        for i, line in enumerate(content):
            if '| activity ' in line and '| actName ' in line:
                # Found the header row
                header_index = i

                # Look for the separator line after the header
                for j in range(header_index + 1, min(header_index + 5, len(content))):
                    if '+----------+' in content[j]:
                        start_index = j + 1
                        break

                if start_index:
                    break

        if start_index is None:
            logger.error("Could not find the start of data in the file")
            return []

        # Find the end of the data (before the row count summary)
        end_index = None
        for i in range(start_index, len(content)):
            if 'rows in set' in content[i]:
                end_index = i - 1
                break

        if end_index is None:
            # Try alternative end marker
            for i in range(start_index, len(content)):
                if '+----------+' in content[i] and i > start_index + 10:  # Make sure we've processed some data
                    for j in range(i+1, min(i+5, len(content))):
                        if 'unit4@' in content[j] or 'quit' in content[j]:
                            end_index = i
                            break
                    if end_index:
                        break

        if end_index is None:
            logger.error("Could not find the end of data in the file")
            return []

        # Extract the data rows
        data_rows = content[start_index:end_index]

        # Parse the data rows
        parsed_rows = []
        for row in data_rows:
            # Skip separator rows
            if '+----------+' in row or '+-----+' in row:
                continue

            # Skip empty rows
            if not row.strip():
                continue

            # Parse the row
            parsed_row = parse_row(row)
            if parsed_row:
                parsed_rows.append(parsed_row)

        logger.info(f"Parsed {len(parsed_rows)} rows from the file")
        return parsed_rows

    except Exception as e:
        logger.error(f"Failed to parse text file: {e}")
        return []

def parse_row(row):
    """Parse a single row of data"""
    try:
        # Split the row by the pipe character
        parts = row.split('|')

        # Remove leading/trailing whitespace from each part
        parts = [part.strip() for part in parts]

        # Skip rows that don't have the expected number of columns
        if len(parts) < 40:
            return None

        # Extract the values
        activity = parts[1]
        act_name = parts[2]
        anticipated_end_date = parts[3] if parts[3] != 'NULL' else None
        client = parts[4]
        concur = parts[5]
        end_date = parts[6] if parts[6] != 'NULL' else None
        fund = parts[7]
        fund_name = parts[8]
        grs = parts[9]
        hr = parts[10]
        org_unit = parts[11]
        org_unit_name = parts[12]
        parent_wo_name = parts[13]
        parent_work_order = parts[14]
        pi_eid = parts[15] if parts[15] != 'NULL' else None
        pi_name = parts[16] if parts[16] != 'NULL' else None
        pi_watiam = parts[17]
        project = parts[18]
        project_name = parts[19]
        research_compliance = parts[20]
        rfa_eid = parts[21] if parts[21] != 'NULL' else None
        rfa_name = parts[22] if parts[22] != 'NULL' else None
        rfa_watiam = parts[23] if parts[23] != 'NULL' else None
        sa_eid = parts[24]
        sa_name = parts[25]
        sa_watiam = parts[26]
        start_date = parts[27] if parts[27] != 'NULL' else None
        status = parts[28]
        wo_manager_eid = parts[29]
        wo_manager_name = parts[30]
        wo_manager_watiam = parts[31]
        work_order = parts[32]
        work_order_name = parts[33]
        work_request = parts[34] if parts[34] != 'NULL' else None
        activity_balance = parts[35]
        work_order_balance = parts[36]
        client_balance = parts[37]

        # Convert numeric values
        try:
            future_budget = float(parts[38]) if parts[38] != 'NULL' else 0.0
        except ValueError:
            future_budget = 0.0

        try:
            net_funds = float(parts[39]) if parts[39] != 'NULL' else 0.0
        except ValueError:
            net_funds = 0.0

        # Clean up date fields
        if anticipated_end_date:
            anticipated_end_date = anticipated_end_date.replace('T', ' ')
        if end_date:
            end_date = end_date.replace('T', ' ')
        if start_date:
            start_date = start_date.replace('T', ' ')

        return {
            'activity': activity,
            'act_name': act_name,
            'anticipated_end_date': anticipated_end_date,
            'client': client,
            'concur': concur,
            'end_date': end_date,
            'fund': fund,
            'fund_name': fund_name,
            'grs': grs,
            'hr': hr,
            'org_unit': org_unit,
            'org_unit_name': org_unit_name,
            'parent_wo_name': parent_wo_name,
            'parent_work_order': parent_work_order,
            'pi_eid': pi_eid,
            'pi_name': pi_name,
            'pi_watiam': pi_watiam,
            'project': project,
            'project_name': project_name,
            'research_compliance': research_compliance,
            'rfa_eid': rfa_eid,
            'rfa_name': rfa_name,
            'rfa_watiam': rfa_watiam,
            'sa_eid': sa_eid,
            'sa_name': sa_name,
            'sa_watiam': sa_watiam,
            'start_date': start_date,
            'status': status,
            'wo_manager_eid': wo_manager_eid,
            'wo_manager_name': wo_manager_name,
            'wo_manager_watiam': wo_manager_watiam,
            'work_order': work_order,
            'work_order_name': work_order_name,
            'work_request': work_request,
            'activity_balance': activity_balance,
            'work_order_balance': work_order_balance,
            'client_balance': client_balance,
            'future_budget': future_budget,
            'net_funds': net_funds
        }

    except Exception as e:
        logger.error(f"Failed to parse row: {e}")
        return None

def import_data(conn, data_rows):
    """Import data into the research_work_order table"""
    try:
        if not data_rows:
            logger.error("No data to import")
            return

        cur = conn.cursor()

        # Prepare the SQL statement
        columns = ', '.join(data_rows[0].keys())
        placeholders = ', '.join(['%s'] * len(data_rows[0]))

        insert_query = f"""
            INSERT INTO unit4.research_work_order ({columns})
            VALUES ({placeholders})
        """

        # Import the data
        rows_processed = 0
        for row in data_rows:
            values = list(row.values())
            cur.execute(insert_query, values)
            rows_processed += 1

            if rows_processed % 100 == 0:
                logger.info(f"Processed {rows_processed} rows")

        cur.close()
        logger.info(f"Successfully imported {rows_processed} rows")

    except Exception as e:
        logger.error(f"Failed to import data: {e}")
        sys.exit(1)

def main():
    """Main function"""
    args = parse_args()

    # Check if the file exists
    if not os.path.isfile(args.file):
        logger.error(f"File not found: {args.file}")
        sys.exit(1)

    # Connect to the database
    conn = connect_to_db()

    # Create schema if requested
    if args.create_schema:
        create_schema(conn)

    # Create table if requested
    if args.create_table:
        create_table(conn)

    # Truncate table if requested
    if args.truncate:
        truncate_table(conn)

    # Parse the text file
    data_rows = parse_text_file(args.file)

    # Import the data
    import_data(conn, data_rows)

    # Close the database connection
    conn.close()

    logger.info("Import process completed successfully")

if __name__ == "__main__":
    main()
