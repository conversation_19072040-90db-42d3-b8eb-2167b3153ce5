#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the structure and data in the quest.supervision_data table.
"""

import psycopg2

# Database connection string
DB_URL = "postgresql://neondb_owner:<EMAIL>/ameliadb?sslmode=require"

def main():
    """Main function"""
    # Connect to the database
    conn = psycopg2.connect(DB_URL)
    cur = conn.cursor()
    
    # Check table structure
    print("Table Structure:")
    cur.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'quest' 
        AND table_name = 'supervision_data' 
        ORDER BY ordinal_position
    """)
    for row in cur.fetchall():
        print(f"  {row[0]}: {row[1]}")
    
    # Check row count
    print("\nRow Counts:")
    cur.execute("SELECT COUNT(*) FROM quest.supervision_data")
    print(f"  Total rows: {cur.fetchone()[0]}")
    
    cur.execute("SELECT COUNT(*) FROM quest.supervision_data WHERE faculty_id IS NOT NULL")
    print(f"  Rows with matched faculty: {cur.fetchone()[0]}")
    
    cur.execute("SELECT COUNT(DISTINCT faculty_id) FROM quest.supervision_data WHERE faculty_id IS NOT NULL")
    print(f"  Distinct faculty matched: {cur.fetchone()[0]}")
    
    # Sample data
    print("\nSample Data (5 rows):")
    cur.execute("""
        SELECT supervision_id, term, student_id, student_name, 
               supervisor_first_name, supervisor_last_name, faculty_id
        FROM quest.supervision_data
        LIMIT 5
    """)
    for row in cur.fetchall():
        print(f"  {row}")
    
    # Sample data with matched faculty
    print("\nSample Data with Matched Faculty (5 rows):")
    cur.execute("""
        SELECT sd.supervision_id, sd.term, sd.student_id, sd.student_name, 
               sd.supervisor_first_name, sd.supervisor_last_name, sd.faculty_id,
               f.first_name, f.last_name
        FROM quest.supervision_data sd
        JOIN uw.faculty f ON sd.faculty_id = f.faculty_id
        LIMIT 5
    """)
    for row in cur.fetchall():
        print(f"  {row}")
    
    # Close the database connection
    cur.close()
    conn.close()

if __name__ == "__main__":
    main()
