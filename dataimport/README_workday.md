# Workday Data Import

This directory contains scripts for importing data from Workday into the database.

## Workday Schema

The `workday` schema is used to store data imported from Workday. Currently, it contains the following tables:

- `position_details`: Contains position details from Workday, including information about employees, job profiles, organizational structure, etc.

## Import Scripts

### `import_workday_positions.py`

This script imports position details from a Workday Excel export into the `workday.position_details` table.

#### Usage

```bash
python3 dataimport/import_workday_positions.py [options]
```

#### Options

- `--file`: Path to the Excel file (default: `raw_data/workday/UW_ENG_Position_Details_20250301.xlsx`)
- `--create-schema`: Create the workday schema if it does not exist
- `--create-table`: Create the position_details table if it does not exist
- `--truncate`: Truncate the table before importing data
- `--skip-rows`: Number of rows to skip in the Excel file (default: 1)

#### Example

```bash
# Create schema, create table, truncate table, and import data
python3 dataimport/import_workday_positions.py --create-schema --create-table --truncate

# Import data from a different file
python3 dataimport/import_workday_positions.py --file raw_data/workday/new_data.xlsx
```

### `check_workday_data.py`

This script checks the structure and data in the `workday.position_details` table.

#### Usage

```bash
python3 dataimport/check_workday_data.py
```

## Data Structure

The `position_details` table contains the following columns:

- `id`: Serial primary key
- `position_name`: Full position name including reference ID and worker name
- `reference_id`: Unique reference ID for the position
- `worker_type`: Type of worker (Employee, Contingent Worker, etc.)
- `employee_type`: Employee type (Regular, Temporary, etc.)
- `time_type`: Time type (Full time, Part time, etc.)
- `staffing_status`: Current staffing status (Filled, Vacant, etc.)
- `available_for_hire`: Whether the position is available for hire
- `worker`: Name of the worker currently in the position
- `contract_end_date`: End date of the contract if applicable
- `business_title`: Business title of the position
- `job_profile`: Job profile including code
- `frozen`: Whether the position is frozen
- `freeze_date`: Date when the position was frozen
- `freeze_reason`: Reason for freezing the position
- `previous_incumbent`: Name of the previous incumbent
- `position_vacate_date`: Date when the position was vacated
- `fte`: Full-time equivalent value
- `job_family`: Job family (Staff Family, Faculty, etc.)
- `job_family_groups`: Job family groups
- `cost_center`: Cost center including code and description
- `class_indicator`: Class indicator code and description
- `manager`: Name of the manager
- `supervisory_organization`: Supervisory organization name
- `level_03`: Organization level 3 from the top
- `level_04`: Organization level 4 from the top
- `level_05`: Organization level 5 from the top
- `level_06`: Organization level 6 from the top
- `level_07`: Organization level 7 from the top
- `import_date`: Date and time when the record was imported

## Data Source

The data is exported from Workday as an Excel file. The current file is:

- `raw_data/workday/UW_ENG_Position_Details_20250301.xlsx`

This file contains position details for the University of Waterloo Engineering faculty as of March 1, 2025.
