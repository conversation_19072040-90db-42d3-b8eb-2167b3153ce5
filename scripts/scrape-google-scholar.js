#!/usr/bin/env node

/**
 * Google Scholar Scraper
 *
 * This script scrapes Google Scholar data for faculty members and stores it in the database.
 * It uses Puppeteer for web scraping and PostgreSQL for data storage.
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { Pool } = require('pg');
// Remove unused imports
// const fs = require('fs');
// const path = require('path');
const { program } = require('commander');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Add stealth plugin to puppeteer
puppeteer.use(StealthPlugin());

// Configure command line options
program
  .option('-l, --limit <number>', 'Limit the number of faculty members to process', parseInt)
  .option('-o, --offset <number>', 'Offset for faculty members to process', parseInt, 0)
  .option('-d, --delay <number>', 'Delay between requests in milliseconds', parseInt, 3000)
  .option('-r, --retry <number>', 'Number of retries for failed requests', parseInt, 3)
  .option('-t, --timeout <number>', 'Timeout for page navigation in milliseconds', parseInt, 30000)
  .option('-v, --verbose', 'Enable verbose logging')
  .option('-f, --force', 'Force update even if already processed')
  .option('--faculty-id <id>', 'Process only a specific faculty ID', parseInt)
  .option('--headless <boolean>', 'Run in headless mode', (val) => val === 'true', true)
  .parse(process.argv);

const options = program.opts();

// Configure database connections
const ingestPool = new Pool({
  connectionString: process.env.POSTGRES_URL_ingest,
  ssl: { rejectUnauthorized: false }
});

// Connection to the main database containing faculty data
const mainPool = new Pool({
  connectionString: process.env.POSTGRES_URL_new,
  ssl: { rejectUnauthorized: false }
});

// Create a logger
const logger = {
  info: (message) => {
    console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
  },
  error: (message, error) => {
    console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error || '');
  },
  debug: (message) => {
    if (options.verbose) {
      console.log(`[DEBUG] ${new Date().toISOString()}: ${message}`);
    }
  }
};

// Helper function to log to database
async function logToDatabase(facultyId, scholarId, logType, status, message) {
  try {
    await ingestPool.query(
      'INSERT INTO googlescholar.scrape_log (faculty_id, scholar_id, log_type, status, message) VALUES ($1, $2, $3, $4, $5)',
      [facultyId, scholarId, logType, status, message]
    );
  } catch (error) {
    logger.error(`Failed to log to database: ${error.message}`);
  }
}

// Helper function to delay execution
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to get faculty members from database
async function getFacultyMembers() {
  try {
    // Try to get faculty data from the main database
    try {
      let query = `
        SELECT faculty_id, first_name, last_name, work_email
        FROM uw.faculty
        WHERE job_family = 'Regular Faculty'
        AND is_deleted = FALSE
      `;

      const params = [];

      if (options.facultyId) {
        query += ' AND faculty_id = $1';
        params.push(options.facultyId);
      }

      // Add ORDER BY to ensure consistent results with offset
      query += ' ORDER BY faculty_id';

      // Add OFFSET if specified
      if (options.offset && options.offset > 0) {
        query += ' OFFSET $' + (params.length + 1);
        params.push(options.offset);
      }

      // Add LIMIT if specified
      if (options.limit) {
        query += ' LIMIT $' + (params.length + 1);
        params.push(options.limit);
      }

      const result = await mainPool.query(query, params);

      if (result.rows.length > 0) {
        logger.info(`Found ${result.rows.length} faculty members in the main database.`);
        return result.rows;
      } else {
        logger.info('No faculty members found in the main database. Using mock data for testing.');
      }
    } catch (error) {
      logger.info(`Error querying faculty data from main database: ${error.message}. Using mock data for testing.`);
    }

    // If we couldn't get data from the main database, use mock data
    return [
      { faculty_id: 1, first_name: 'John', last_name: 'Doe', work_email: '<EMAIL>' },
      { faculty_id: 2, first_name: 'Jane', last_name: 'Smith', work_email: '<EMAIL>' },
      { faculty_id: 3, first_name: 'Michael', last_name: 'Johnson', work_email: '<EMAIL>' },
      { faculty_id: 4, first_name: 'Emily', last_name: 'Williams', work_email: '<EMAIL>' },
      { faculty_id: 5, first_name: 'David', last_name: 'Brown', work_email: '<EMAIL>' }
    ];
  } catch (error) {
    logger.error('Failed to get faculty members:', error);
    throw error;
  }
}

// Function to search for a faculty member on Google Scholar
async function searchGoogleScholar(page, firstName, lastName) {
  try {
    // Create screenshots directory if it doesn't exist
    const fs = require('fs');
    const path = require('path');
    const screenshotsDir = path.join(__dirname, 'screenshots');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
    }

    // Try different search approaches
    const searchApproaches = [
      // Approach 1: Direct author search with name only
      async () => {
        const searchQuery = `${firstName} ${lastName}`;
        logger.debug(`Approach 1: Searching for author: ${searchQuery}`);

        // Navigate to Google Scholar
        await page.goto('https://scholar.google.com/', { timeout: options.timeout });
        await delay(options.delay);

        // Take screenshot of the initial page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `initial1_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
          logger.debug(`Saved initial page screenshot to ${screenshotPath}`);
        }

        // Type search query
        await page.type('input[name="q"]', searchQuery);

        // Submit search
        await Promise.all([
          page.waitForNavigation({ timeout: options.timeout }),
          page.keyboard.press('Enter')
        ]);

        await delay(options.delay);

        // Take screenshot of the results page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `results1_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
          logger.debug(`Saved results page screenshot to ${screenshotPath}`);
        }

        // Check if there are any results
        const noResults = await page.evaluate(() => {
          return document.body.innerText.includes('No profiles found');
        });

        if (!noResults) {
          // Extract author profiles
          return await extractAuthorProfiles(page);
        }

        return null;
      },

      // Approach 2: Author search with university affiliation
      async () => {
        const searchQuery = `${firstName} ${lastName} University of Waterloo`;
        logger.debug(`Approach 2: Searching for author with affiliation: ${searchQuery}`);

        // Navigate to Google Scholar
        await page.goto('https://scholar.google.com/', { timeout: options.timeout });
        await delay(options.delay);

        // Take screenshot of the initial page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `initial2_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
          logger.debug(`Saved initial page screenshot to ${screenshotPath}`);
        }

        // Type search query
        await page.type('input[name="q"]', searchQuery);

        // Submit search
        await Promise.all([
          page.waitForNavigation({ timeout: options.timeout }),
          page.keyboard.press('Enter')
        ]);

        await delay(options.delay);

        // Take screenshot of the results page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `results2_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
          logger.debug(`Saved results page screenshot to ${screenshotPath}`);
        }

        // Check if there are any results
        const noResults = await page.evaluate(() => {
          return document.body.innerText.includes('No profiles found');
        });

        if (!noResults) {
          // Extract author profiles
          return await extractAuthorProfiles(page);
        }

        return null;
      },

      // Approach 3: Direct search for publications
      async () => {
        const searchQuery = `author:"${firstName} ${lastName}" University of Waterloo`;
        logger.debug(`Approach 3: Searching for publications: ${searchQuery}`);

        // Navigate to Google Scholar
        await page.goto('https://scholar.google.com/', { timeout: options.timeout });
        await delay(options.delay);

        // Take screenshot of the initial page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `initial3_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
          logger.debug(`Saved initial page screenshot to ${screenshotPath}`);
        }

        // Type search query
        await page.type('input[name="q"]', searchQuery);

        // Submit search
        await Promise.all([
          page.waitForNavigation({ timeout: options.timeout }),
          page.keyboard.press('Enter')
        ]);

        await delay(options.delay);

        // Take screenshot of the results page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `results3_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
          logger.debug(`Saved results page screenshot to ${screenshotPath}`);
        }

        // Check if there are any publication results
        const hasResults = await page.evaluate(() => {
          return document.querySelectorAll('.gs_r').length > 0;
        });

        if (hasResults) {
          // Try to find author profile links in the publications
          const authorLinks = await page.evaluate(() => {
            const links = [];
            const authorElements = document.querySelectorAll('.gs_a a');
            authorElements.forEach(el => {
              const href = el.href;
              if (href && href.includes('/citations?user=')) {
                links.push({
                  name: el.innerText,
                  profileUrl: href,
                  scholarId: href.match(/user=([^&]+)/)?.[1] || ''
                });
              }
            });
            return links;
          });

          if (authorLinks.length > 0) {
            // Filter for links that might match our faculty member
            const matchingLinks = authorLinks.filter(link => {
              const nameLower = link.name.toLowerCase();
              return nameLower.includes(firstName.toLowerCase()) ||
                     nameLower.includes(lastName.toLowerCase());
            });

            if (matchingLinks.length > 0) {
              return matchingLinks.map(link => ({
                name: link.name,
                scholarId: link.scholarId,
                profileUrl: link.profileUrl,
                affiliation: 'Unknown (found via publication)',
                interests: ''
              }));
            }
          }
        }

        return null;
      }
    ];

    // Try each approach in sequence until we find results
    for (const approach of searchApproaches) {
      const results = await approach();
      if (results && results.length > 0) {
        return results;
      }

      // Add a delay between approaches
      await delay(options.delay);
    }

    logger.debug(`No authors found for ${firstName} ${lastName} after trying all approaches`);
    return null;
  } catch (error) {
    logger.error(`Search failed for ${firstName} ${lastName}:`, error);
    return null;
  }
}

// Helper function to extract author profiles from search results
async function extractAuthorProfiles(page) {
  try {
    // Check for captcha
    const hasCaptcha = await page.evaluate(() => {
      return document.body.innerText.includes('captcha') ||
             document.body.innerText.includes('unusual traffic') ||
             document.body.innerText.includes('verify you\'re a human');
    });

    if (hasCaptcha) {
      logger.error('Captcha detected when extracting author profiles');
      return null;
    }

    // Extract author profiles
    return await page.evaluate(() => {
      const authorElements = document.querySelectorAll('.gs_ai_t');
      return Array.from(authorElements).map(element => {
        const nameElement = element.querySelector('.gs_ai_name a');
        const affiliationElement = element.querySelector('.gs_ai_aff');
        const interestsElement = element.querySelector('.gs_ai_int');

        // Extract scholar ID from profile URL
        const profileUrl = nameElement ? nameElement.href : '';
        const scholarId = profileUrl.match(/user=([^&]+)/)?.[1] || '';

        return {
          name: nameElement ? nameElement.innerText : '',
          scholarId,
          profileUrl,
          affiliation: affiliationElement ? affiliationElement.innerText : '',
          interests: interestsElement ? interestsElement.innerText : ''
        };
      });
    });
  } catch (error) {
    logger.error('Error extracting author profiles:', error);
    return null;
  }
}

// Function to scrape author profile
async function scrapeAuthorProfile(page, scholarId, facultyId) {
  try {
    const profileUrl = `https://scholar.google.com/citations?user=${scholarId}&hl=en`;
    logger.debug(`Scraping profile: ${profileUrl}`);

    await page.goto(profileUrl, { timeout: options.timeout });
    await delay(options.delay);

    // Extract profile information
    const profileData = await page.evaluate(() => {
      const name = document.querySelector('#gsc_prf_in')?.innerText || '';
      const affiliation = document.querySelector('.gsc_prf_il')?.innerText || '';
      const emailDomain = document.querySelector('.gsc_prf_ila')?.innerText || '';
      const areasOfInterest = Array.from(document.querySelectorAll('a.gsc_prf_inta')).map(a => a.innerText).join(', ');
      const homepageUrl = document.querySelector('a.gsc_prf_ila')?.href || '';
      const profileImageUrl = document.querySelector('#gsc_prf_pua img')?.src || '';

      // Extract citation stats
      const citationStats = {};
      const statTables = document.querySelectorAll('#gsc_rsb_st');
      if (statTables.length > 0) {
        const rows = statTables[0].querySelectorAll('tr');
        rows.forEach((row, index) => {
          if (index === 0) return; // Skip header row

          const cells = row.querySelectorAll('td');
          if (cells.length >= 3) {
            const statName = cells[0].innerText.trim().toLowerCase().replace(/\s+/g, '_');
            citationStats[`${statName}_all`] = parseInt(cells[1].innerText) || 0;
            citationStats[`${statName}_since_2019`] = parseInt(cells[2].innerText) || 0;
          }
        });
      }

      return {
        name,
        affiliation,
        emailDomain,
        areasOfInterest,
        homepageUrl,
        profileImageUrl,
        ...citationStats
      };
    });

    // Save profile data to database
    const query = `
      INSERT INTO googlescholar.author_profile (
        scholar_id, faculty_id, name, affiliation, email_domain, areas_of_interest,
        homepage_url, citations_all, citations_since_2019, h_index_all, h_index_since_2019,
        i10_index_all, i10_index_since_2019, profile_url, profile_image_url
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      ON CONFLICT (scholar_id)
      DO UPDATE SET
        faculty_id = $2,
        name = $3,
        affiliation = $4,
        email_domain = $5,
        areas_of_interest = $6,
        homepage_url = $7,
        citations_all = $8,
        citations_since_2019 = $9,
        h_index_all = $10,
        h_index_since_2019 = $11,
        i10_index_all = $12,
        i10_index_since_2019 = $13,
        profile_url = $14,
        profile_image_url = $15,
        last_updated = NOW()
      RETURNING author_id
    `;

    const result = await ingestPool.query(query, [
      scholarId,
      facultyId,
      profileData.name,
      profileData.affiliation,
      profileData.emailDomain,
      profileData.areasOfInterest,
      profileData.homepageUrl,
      profileData.citations_all || 0,
      profileData.citations_since_2019 || 0,
      profileData.h_index_all || 0,
      profileData.h_index_since_2019 || 0,
      profileData.i10_index_all || 0,
      profileData.i10_index_since_2019 || 0,
      profileUrl,
      profileData.profileImageUrl
    ]);

    const authorId = result.rows[0].author_id;

    // Scrape citation history
    await scrapeCitationHistory(page, authorId, scholarId);

    // Scrape publications
    await scrapePublications(page, scholarId);

    // Scrape co-authors
    await scrapeCoAuthors(page, authorId, scholarId);

    return authorId;
  } catch (error) {
    logger.error(`Failed to scrape profile for ${scholarId}:`, error);
    await logToDatabase(facultyId, scholarId, 'profile', 'failed', error.message);
    return null;
  }
}

// Function to scrape citation history
async function scrapeCitationHistory(page, authorId, scholarId) {
  try {
    logger.debug(`Scraping citation history for author ID: ${authorId}`);

    // Click on the citation history button
    const citationButton = await page.$('#gsc_rsb_cit a');
    if (!citationButton) {
      logger.debug('No citation history button found');
      return;
    }

    await Promise.all([
      page.waitForNavigation({ timeout: options.timeout }),
      citationButton.click()
    ]);

    await delay(options.delay);

    // Extract citation history
    const citationHistory = await page.evaluate(() => {
      const table = document.querySelector('#gsc_rsb_cit table');
      if (!table) return [];

      const rows = table.querySelectorAll('tr');
      return Array.from(rows).slice(1).map(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
          return {
            year: parseInt(cells[0].innerText) || 0,
            citations: parseInt(cells[1].innerText) || 0
          };
        }
        return null;
      }).filter(Boolean);
    });

    // Save citation history to database
    for (const citation of citationHistory) {
      await ingestPool.query(
        `INSERT INTO googlescholar.citation_history (author_id, year, citations)
         VALUES ($1, $2, $3)
         ON CONFLICT (author_id, year)
         DO UPDATE SET citations = $3, last_updated = NOW()`,
        [authorId, citation.year, citation.citations]
      );
    }

    // Go back to profile page
    await page.goBack({ timeout: options.timeout });
    await delay(options.delay);

    return citationHistory;
  } catch (error) {
    logger.error(`Failed to scrape citation history for ${scholarId}:`, error);
    return null;
  }
}

// Function to scrape publications
async function scrapePublications(page, scholarId) {
  try {
    logger.debug(`Scraping publications for scholar ID: ${scholarId}`);

    // Check if we need to click "Show more" button
    let hasMorePublications = true;
    let publicationCount = 0;

    while (hasMorePublications) {
      // Check for "Show more" button
      const showMoreButton = await page.$('#gsc_bpf_more');
      const isDisabled = await page.evaluate(button => {
        return button && button.disabled;
      }, showMoreButton);

      if (!showMoreButton || isDisabled) {
        hasMorePublications = false;
        break;
      }

      // Click "Show more" button
      await showMoreButton.click();
      await delay(options.delay);

      // Check if we've loaded enough publications or if there are no more
      const currentCount = await page.evaluate(() => {
        return document.querySelectorAll('.gsc_a_tr').length;
      });

      if (currentCount <= publicationCount) {
        hasMorePublications = false;
        break;
      }

      publicationCount = currentCount;
      logger.debug(`Loaded ${publicationCount} publications so far`);

      // Avoid loading too many publications to prevent memory issues
      if (publicationCount >= 100) {
        logger.debug('Reached maximum publication count (100), stopping');
        break;
      }
    }

    // Extract publications
    const publications = await page.evaluate(() => {
      const publicationElements = document.querySelectorAll('.gsc_a_tr');
      return Array.from(publicationElements).map(element => {
        const titleElement = element.querySelector('.gsc_a_t a');
        const authorsElement = element.querySelector('.gsc_a_t .gs_gray:nth-child(1)');
        const venueElement = element.querySelector('.gsc_a_t .gs_gray:nth-child(2)');
        const yearElement = element.querySelector('.gsc_a_y span');
        const citationsElement = element.querySelector('.gsc_a_c a');

        return {
          title: titleElement ? titleElement.innerText : '',
          publicationUrl: titleElement ? titleElement.href : '',
          authors: authorsElement ? authorsElement.innerText : '',
          venue: venueElement ? venueElement.innerText : '',
          year: yearElement ? parseInt(yearElement.innerText) || null : null,
          citations: citationsElement ? parseInt(citationsElement.innerText) || 0 : 0,
          scholarUrl: titleElement ? titleElement.href : ''
        };
      });
    });

    // Save publications to database
    for (const pub of publications) {
      await ingestPool.query(
        `INSERT INTO googlescholar.publication (
          scholar_id, title, authors, venue, year, citations, publication_url, scholar_url
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (scholar_id, title)
        DO UPDATE SET
          authors = $3,
          venue = $4,
          year = $5,
          citations = $6,
          publication_url = $7,
          scholar_url = $8,
          last_updated = NOW()`,
        [
          scholarId,
          pub.title,
          pub.authors,
          pub.venue,
          pub.year,
          pub.citations,
          pub.publicationUrl,
          pub.scholarUrl
        ]
      );
    }

    return publications;
  } catch (error) {
    logger.error(`Failed to scrape publications for ${scholarId}:`, error);
    return null;
  }
}

// Function to scrape co-authors
async function scrapeCoAuthors(page, authorId, scholarId) {
  try {
    logger.debug(`Scraping co-authors for author ID: ${authorId}`);

    // Click on the co-authors tab
    const coAuthorsTab = await page.$('#gsc_coauth_tab');
    if (!coAuthorsTab) {
      logger.debug('No co-authors tab found');
      return;
    }

    await coAuthorsTab.click();
    await delay(options.delay);

    // Extract co-authors
    const coAuthors = await page.evaluate(() => {
      const coAuthorElements = document.querySelectorAll('.gsc_rsb_aa');
      return Array.from(coAuthorElements).map(element => {
        const nameElement = element.querySelector('a');
        const affiliationElement = element.querySelector('.gsc_rsb_a_ext');

        // Extract scholar ID from profile URL
        const profileUrl = nameElement ? nameElement.href : '';
        const scholarId = profileUrl.match(/user=([^&]+)/)?.[1] || '';

        return {
          name: nameElement ? nameElement.innerText : '',
          scholarId,
          affiliation: affiliationElement ? affiliationElement.innerText : ''
        };
      });
    });

    // Save co-authors to database
    for (const coAuthor of coAuthors) {
      await ingestPool.query(
        `INSERT INTO googlescholar.co_author (
          author_id, co_author_name, co_author_scholar_id, co_author_affiliation
        ) VALUES ($1, $2, $3, $4)
        ON CONFLICT (author_id, co_author_scholar_id)
        DO UPDATE SET
          co_author_name = $2,
          co_author_affiliation = $4,
          last_updated = NOW()`,
        [authorId, coAuthor.name, coAuthor.scholarId, coAuthor.affiliation]
      );
    }

    return coAuthors;
  } catch (error) {
    logger.error(`Failed to scrape co-authors for ${scholarId}:`, error);
    return null;
  }
}

// Main function to process faculty members
async function processFacultyMembers() {
  let browser;

  try {
    logger.info('Starting Google Scholar scraper');

    // Launch browser
    browser = await puppeteer.launch({
      headless: "new",  // Use the new headless mode
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu',
        '--window-size=1920,1080',
      ],
      defaultViewport: { width: 1920, height: 1080 },
      executablePath: process.env.CHROME_PATH || undefined  // Allow specifying Chrome path in env
    });

    const page = await browser.newPage();

    // Set user agent
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    // Get faculty members
    const facultyMembers = await getFacultyMembers();
    logger.info(`Found ${facultyMembers.length} faculty members to process`);

    // Process each faculty member
    for (const faculty of facultyMembers) {
      try {
        logger.info(`Processing faculty member: ${faculty.first_name} ${faculty.last_name} (ID: ${faculty.faculty_id})`);

        // Check if already processed
        if (!options.force) {
          const existingSearch = await ingestPool.query(
            'SELECT * FROM googlescholar.faculty_search WHERE faculty_id = $1 AND status = $2',
            [faculty.faculty_id, 'completed']
          );

          if (existingSearch.rows.length > 0) {
            logger.debug(`Faculty member ${faculty.faculty_id} already processed, skipping`);
            continue;
          }
        }

        // Create search record
        const searchQuery = `${faculty.first_name} ${faculty.last_name}`;
        const searchRecord = await ingestPool.query(
          'INSERT INTO googlescholar.faculty_search (faculty_id, first_name, last_name, search_query, status) VALUES ($1, $2, $3, $4, $5) RETURNING search_id',
          [faculty.faculty_id, faculty.first_name, faculty.last_name, searchQuery, 'pending']
        );

        const searchId = searchRecord.rows[0].search_id;

        // Search for faculty member on Google Scholar
        let retryCount = 0;
        let authors = null;

        while (retryCount < options.retry && !authors) {
          if (retryCount > 0) {
            logger.debug(`Retry ${retryCount} for ${faculty.first_name} ${faculty.last_name}`);
            await delay(options.delay * 2);
          }

          authors = await searchGoogleScholar(page, faculty.first_name, faculty.last_name);
          retryCount++;
        }

        if (!authors || authors.length === 0) {
          logger.debug(`No authors found for ${faculty.first_name} ${faculty.last_name}`);
          await ingestPool.query(
            'UPDATE googlescholar.faculty_search SET status = $1, error_message = $2 WHERE search_id = $3',
            ['completed', 'No authors found', searchId]
          );
          continue;
        }

        // Find the most likely author match
        const bestMatch = findBestAuthorMatch(authors, faculty);

        if (!bestMatch) {
          logger.debug(`No suitable match found for ${faculty.first_name} ${faculty.last_name}`);
          await ingestPool.query(
            'UPDATE googlescholar.faculty_search SET status = $1, error_message = $2 WHERE search_id = $3',
            ['completed', 'No suitable match found', searchId]
          );
          continue;
        }

        // Update search record with scholar ID
        await ingestPool.query(
          'UPDATE googlescholar.faculty_search SET scholar_id = $1 WHERE search_id = $2',
          [bestMatch.scholarId, searchId]
        );

        // Scrape author profile
        const authorId = await scrapeAuthorProfile(page, bestMatch.scholarId, faculty.faculty_id);

        if (authorId) {
          logger.info(`Successfully processed ${faculty.first_name} ${faculty.last_name} (Scholar ID: ${bestMatch.scholarId})`);
          await ingestPool.query(
            'UPDATE googlescholar.faculty_search SET status = $1 WHERE search_id = $2',
            ['completed', searchId]
          );
          await logToDatabase(faculty.faculty_id, bestMatch.scholarId, 'search', 'success', 'Successfully processed faculty member');
        } else {
          logger.error(`Failed to process ${faculty.first_name} ${faculty.last_name}`);
          await ingestPool.query(
            'UPDATE googlescholar.faculty_search SET status = $1, error_message = $2 WHERE search_id = $3',
            ['failed', 'Failed to scrape profile', searchId]
          );
        }

        // Add delay between faculty members
        await delay(options.delay * 2);
      } catch (error) {
        logger.error(`Error processing faculty member ${faculty.faculty_id}:`, error);
        await logToDatabase(faculty.faculty_id, null, 'search', 'failed', error.message);

        // Update search record with error
        await ingestPool.query(
          'UPDATE googlescholar.faculty_search SET status = $1, error_message = $2 WHERE faculty_id = $3',
          ['failed', error.message, faculty.faculty_id]
        );
      }
    }

    logger.info('Finished processing all faculty members');
  } catch (error) {
    logger.error('Fatal error:', error);
  } finally {
    // Close browser and database connections
    if (browser) {
      await browser.close();
    }

    await ingestPool.end();
    await mainPool.end();
    logger.info('Scraper finished');
  }
}

// Function to find the best author match
function findBestAuthorMatch(authors, faculty) {
  if (!authors || authors.length === 0) {
    return null;
  }

  // If there's only one author, return it
  if (authors.length === 1) {
    return authors[0];
  }

  // Calculate match score for each author
  const scoredAuthors = authors.map(author => {
    let score = 0;

    // Check name match
    const authorName = author.name.toLowerCase();
    const facultyFirstName = faculty.first_name.toLowerCase();
    const facultyLastName = faculty.last_name.toLowerCase();

    if (authorName.includes(facultyFirstName) && authorName.includes(facultyLastName)) {
      score += 3;
    } else if (authorName.includes(facultyFirstName) || authorName.includes(facultyLastName)) {
      score += 1;
    }

    // Check email domain match if available
    if (faculty.work_email && author.affiliation) {
      const emailDomain = faculty.work_email.split('@')[1]?.toLowerCase();
      const affiliation = author.affiliation.toLowerCase();

      if (emailDomain && affiliation.includes(emailDomain)) {
        score += 2;
      }
    }

    return { ...author, score };
  });

  // Sort by score (descending)
  scoredAuthors.sort((a, b) => b.score - a.score);

  // Return the author with the highest score if it's above a threshold
  return scoredAuthors[0].score >= 1 ? scoredAuthors[0] : null;
}

// Run the main function
processFacultyMembers().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
