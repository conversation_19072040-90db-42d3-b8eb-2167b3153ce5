#!/usr/bin/env node

/**
 * Test script for Publications API with specific faculty ID
 */

const { Pool } = require('pg');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configure database connection
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL_new,
  ssl: { rejectUnauthorized: false }
});

// Create a logger
const logger = {
  info: (message) => {
    console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
  },
  error: (message, error) => {
    console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error || '');
  },
  debug: (message) => {
    console.log(`[DEBUG] ${new Date().toISOString()}: ${message}`);
  }
};

async function testPublicationsAPI() {
  try {
    logger.info('Testing Publications API with specific faculty ID');

    // Test with faculty ID 1180 (<PERSON><PERSON><PERSON>)
    const facultyId = 1180;

    // Get faculty details
    const facultyResult = await pool.query(`
      SELECT f.faculty_id, f.first_name, f.last_name, f.work_email, u.full_name as department
      FROM uw.faculty f
      LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
      WHERE f.faculty_id = $1 AND f.is_deleted = FALSE
    `, [facultyId]);

    if (facultyResult.rows.length === 0) {
      logger.error(`Faculty with ID ${facultyId} not found`);
      return;
    }

    const faculty = facultyResult.rows[0];
    logger.info(`Found faculty member: ${faculty.first_name} ${faculty.last_name}`);

    // Check if the faculty has an author profile
    const authorCheck = await pool.query(`
      SELECT author_id, scholar_id FROM googlescholar.author_profile
      WHERE faculty_id = $1
    `, [facultyId]);

    if (authorCheck.rows.length === 0) {
      logger.error(`No author profile found for faculty ID: ${facultyId}`);
      return;
    }

    const authorId = authorCheck.rows[0].author_id;
    const scholarId = authorCheck.rows[0].scholar_id;
    logger.info(`Found author profile with ID: ${authorId}, Scholar ID: ${scholarId}`);

    // Get publications
    const publicationsResult = await pool.query(`
      SELECT
        p.publication_id,
        p.scholar_id,
        p.title,
        p.authors,
        p.venue,
        p.year,
        p.citations,
        p.publication_url,
        p.scholar_url,
        p.pdf_url,
        p.abstract,
        p.last_updated
      FROM googlescholar.publication p
      JOIN googlescholar.author_profile ap ON p.scholar_id = ap.scholar_id
      WHERE ap.faculty_id = $1
      ORDER BY p.last_updated DESC, p.year DESC
      LIMIT 5
    `, [facultyId]);

    logger.info(`Found ${publicationsResult.rows.length} publications for faculty member`);

    if (publicationsResult.rows.length > 0) {
      logger.info('Sample publications:');
      publicationsResult.rows.forEach((pub, index) => {
        console.log(`\nPublication ${index + 1}:`);
        console.log(pub);
      });
    }

    logger.info('Test completed successfully');
  } catch (error) {
    logger.error('Error testing Publications API:', error);
  } finally {
    // Close the database connection
    await pool.end();
  }
}

// Run the test
testPublicationsAPI();
