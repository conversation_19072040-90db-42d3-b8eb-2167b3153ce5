// Function to search for a faculty member on Google Scholar
async function searchGoogleScholar(page, firstName, lastName) {
  try {
    // Create screenshots directory if it doesn't exist
    const fs = require('fs');
    const path = require('path');
    const screenshotsDir = path.join(__dirname, 'screenshots');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
    }
    
    // Try different search approaches
    const searchApproaches = [
      // Approach 1: Direct author search with name only
      async () => {
        const searchQuery = `${firstName} ${lastName}`;
        logger.debug(`Approach 1: Searching for author: ${searchQuery}`);
        
        // Navigate to Google Scholar
        await page.goto('https://scholar.google.com/', { timeout: options.timeout });
        await delay(options.delay);
        
        // Take screenshot of the initial page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `initial1_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
        }
        
        // Type search query
        await page.type('input[name="q"]', searchQuery);
        
        // Submit search
        await Promise.all([
          page.waitForNavigation({ timeout: options.timeout }),
          page.keyboard.press('Enter')
        ]);
        
        await delay(options.delay);
        
        // Take screenshot of the results page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `results1_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
        }
        
        // Check if there are any results
        const noResults = await page.evaluate(() => {
          return document.body.innerText.includes('No profiles found');
        });
        
        if (!noResults) {
          // Extract author profiles
          return await extractAuthorProfiles(page);
        }
        
        return null;
      },
      
      // Approach 2: Author search with university affiliation
      async () => {
        const searchQuery = `${firstName} ${lastName} University of Waterloo`;
        logger.debug(`Approach 2: Searching for author with affiliation: ${searchQuery}`);
        
        // Navigate to Google Scholar
        await page.goto('https://scholar.google.com/', { timeout: options.timeout });
        await delay(options.delay);
        
        // Take screenshot of the initial page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `initial2_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
        }
        
        // Type search query
        await page.type('input[name="q"]', searchQuery);
        
        // Submit search
        await Promise.all([
          page.waitForNavigation({ timeout: options.timeout }),
          page.keyboard.press('Enter')
        ]);
        
        await delay(options.delay);
        
        // Take screenshot of the results page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `results2_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
        }
        
        // Check if there are any results
        const noResults = await page.evaluate(() => {
          return document.body.innerText.includes('No profiles found');
        });
        
        if (!noResults) {
          // Extract author profiles
          return await extractAuthorProfiles(page);
        }
        
        return null;
      },
      
      // Approach 3: Direct search for publications
      async () => {
        const searchQuery = `author:"${firstName} ${lastName}" University of Waterloo`;
        logger.debug(`Approach 3: Searching for publications: ${searchQuery}`);
        
        // Navigate to Google Scholar
        await page.goto('https://scholar.google.com/', { timeout: options.timeout });
        await delay(options.delay);
        
        // Take screenshot of the initial page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `initial3_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
        }
        
        // Type search query
        await page.type('input[name="q"]', searchQuery);
        
        // Submit search
        await Promise.all([
          page.waitForNavigation({ timeout: options.timeout }),
          page.keyboard.press('Enter')
        ]);
        
        await delay(options.delay);
        
        // Take screenshot of the results page
        if (options.verbose) {
          const screenshotPath = path.join(screenshotsDir, `results3_${firstName}_${lastName}.png`);
          await page.screenshot({ path: screenshotPath });
        }
        
        // Check if there are any publication results
        const hasResults = await page.evaluate(() => {
          return document.querySelectorAll('.gs_r').length > 0;
        });
        
        if (hasResults) {
          // Try to find author profile links in the publications
          const authorLinks = await page.evaluate(() => {
            const links = [];
            const authorElements = document.querySelectorAll('.gs_a a');
            authorElements.forEach(el => {
              const href = el.href;
              if (href && href.includes('/citations?user=')) {
                links.push({
                  name: el.innerText,
                  profileUrl: href,
                  scholarId: href.match(/user=([^&]+)/)?.[1] || ''
                });
              }
            });
            return links;
          });
          
          if (authorLinks.length > 0) {
            // Filter for links that might match our faculty member
            const matchingLinks = authorLinks.filter(link => {
              const nameLower = link.name.toLowerCase();
              return nameLower.includes(firstName.toLowerCase()) || 
                     nameLower.includes(lastName.toLowerCase());
            });
            
            if (matchingLinks.length > 0) {
              return matchingLinks.map(link => ({
                name: link.name,
                scholarId: link.scholarId,
                profileUrl: link.profileUrl,
                affiliation: 'Unknown (found via publication)',
                interests: ''
              }));
            }
          }
        }
        
        return null;
      }
    ];
    
    // Try each approach in sequence until we find results
    for (const approach of searchApproaches) {
      const results = await approach();
      if (results && results.length > 0) {
        return results;
      }
      
      // Add a delay between approaches
      await delay(options.delay);
    }
    
    logger.debug(`No authors found for ${firstName} ${lastName} after trying all approaches`);
    return null;
  } catch (error) {
    logger.error(`Search failed for ${firstName} ${lastName}:`, error);
    return null;
  }
}

// Helper function to extract author profiles from search results
async function extractAuthorProfiles(page) {
  try {
    // Check for captcha
    const hasCaptcha = await page.evaluate(() => {
      return document.body.innerText.includes('captcha') || 
             document.body.innerText.includes('unusual traffic') ||
             document.body.innerText.includes('verify you\'re a human');
    });
    
    if (hasCaptcha) {
      logger.error('Captcha detected when extracting author profiles');
      return null;
    }
    
    // Extract author profiles
    return await page.evaluate(() => {
      const authorElements = document.querySelectorAll('.gs_ai_t');
      return Array.from(authorElements).map(element => {
        const nameElement = element.querySelector('.gs_ai_name a');
        const affiliationElement = element.querySelector('.gs_ai_aff');
        const interestsElement = element.querySelector('.gs_ai_int');
        
        // Extract scholar ID from profile URL
        const profileUrl = nameElement ? nameElement.href : '';
        const scholarId = profileUrl.match(/user=([^&]+)/)?.[1] || '';
        
        return {
          name: nameElement ? nameElement.innerText : '',
          scholarId,
          profileUrl,
          affiliation: affiliationElement ? affiliationElement.innerText : '',
          interests: interestsElement ? interestsElement.innerText : ''
        };
      });
    });
  } catch (error) {
    logger.error('Error extracting author profiles:', error);
    return null;
  }
}
