#!/usr/bin/env node

/**
 * Test script for Publications API
 *
 * This script tests the Publications API by making a direct database query
 * to simulate what the API endpoint should return.
 */

const { Pool } = require('pg');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configure database connection
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL_new,
  ssl: { rejectUnauthorized: false }
});

// Create a logger
const logger = {
  info: (message) => {
    console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
  },
  error: (message, error) => {
    console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error || '');
  },
  debug: (message) => {
    console.log(`[DEBUG] ${new Date().toISOString()}: ${message}`);
  }
};

async function testPublicationsAPI() {
  try {
    logger.info('Testing Publications API');

    // Test 1: Get faculty list
    logger.info('Test 1: Getting faculty list');
    const facultyResult = await pool.query(`
      SELECT f.faculty_id, f.first_name, f.last_name, f.work_email, u.full_name as department
      FROM uw.faculty f
      LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
      WHERE f.is_deleted = FALSE
      ORDER BY f.last_name, f.first_name
      LIMIT 5
    `);

    logger.info(`Found ${facultyResult.rows.length} faculty members`);

    // Map faculty members to FacultySummary objects
    const facultyList = facultyResult.rows.map(faculty => ({
      faculty_id: faculty.faculty_id,
      faculty_name: `${faculty.first_name} ${faculty.last_name}`,
      work_email: faculty.work_email,
      department: faculty.department
    }));

    logger.info('Faculty list:');
    console.log(facultyList);

    // Test 2: Get publications for a faculty member
    if (facultyResult.rows.length > 0) {
      const faculty = facultyResult.rows[0];
      logger.info(`Test 2: Getting publications for faculty member: ${faculty.first_name} ${faculty.last_name}`);

      // Check if the faculty has an author profile
      const authorCheck = await pool.query(`
        SELECT author_id FROM googlescholar.author_profile
        WHERE faculty_id = $1
      `, [faculty.faculty_id]);

      if (authorCheck.rows.length === 0) {
        logger.info(`No author profile found for faculty ID: ${faculty.faculty_id}`);
      } else {
        const facultyPublications = await pool.query(`
          SELECT
            p.publication_id,
            p.scholar_id,
            p.title,
            p.authors,
            p.venue,
            p.year,
            p.citations,
            p.publication_url,
            p.scholar_url,
            p.pdf_url,
            p.abstract,
            p.last_updated
          FROM googlescholar.publication p
          JOIN googlescholar.author_profile ap ON p.scholar_id = ap.scholar_id
          WHERE ap.faculty_id = $1
          ORDER BY p.last_updated DESC, p.year DESC
          LIMIT 5
        `, [faculty.faculty_id]);

        logger.info(`Found ${facultyPublications.rows.length} publications for faculty member`);

        if (facultyPublications.rows.length > 0) {
          logger.info('Sample publication:');
          console.log(facultyPublications.rows[0]);
        }
      }
    }

    logger.info('All tests completed successfully');
  } catch (error) {
    logger.error('Error testing Publications API:', error);
  } finally {
    // Close the database connection
    await pool.end();
  }
}

// Run the test
testPublicationsAPI();
