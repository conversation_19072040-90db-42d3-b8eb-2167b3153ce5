# Google Scholar Scraper

This script scrapes Google Scholar data for faculty members and stores it in the database.

## Prerequisites

- Node.js (v14 or higher)
- PostgreSQL database
- Faculty data in the `uw.faculty` table

## Setup

1. Create the database schema and tables:

```bash
psql -U your_username -d your_database -f ../dbscripts/ddl/ingest/googlescholar.sql
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file with the following content:

```
POSTGRES_URL_ingest=postgresql://username:password@hostname:port/database?sslmode=require
```

## Usage

Run the script with the following command:

```bash
node scrape-google-scholar.js [options]
```

### Options

- `-l, --limit <number>`: Limit the number of faculty members to process
- `-d, --delay <number>`: Delay between requests in milliseconds (default: 3000)
- `-r, --retry <number>`: Number of retries for failed requests (default: 3)
- `-t, --timeout <number>`: Timeout for page navigation in milliseconds (default: 30000)
- `-v, --verbose`: Enable verbose logging
- `-f, --force`: Force update even if already processed
- `--faculty-id <id>`: Process only a specific faculty ID
- `--headless <boolean>`: Run in headless mode (default: true)

### Examples

Process all faculty members:

```bash
node scrape-google-scholar.js
```

Process only 10 faculty members:

```bash
node scrape-google-scholar.js --limit 10
```

Process a specific faculty member:

```bash
node scrape-google-scholar.js --faculty-id 123
```

Run with visible browser (for debugging):

```bash
node scrape-google-scholar.js --headless false
```

## Data Model

The script stores data in the following tables:

- `googlescholar.faculty_search`: Records of faculty search attempts
- `googlescholar.author_profile`: Google Scholar author profiles
- `googlescholar.publication`: Publications by authors
- `googlescholar.co_author`: Co-authors of authors
- `googlescholar.citation_history`: Citation history by year
- `googlescholar.scrape_log`: Logs of scraping activities

## Notes

- Google Scholar may block requests if too many are made in a short period. Adjust the `--delay` option if needed.
- The script uses Puppeteer with stealth mode to avoid detection, but it's still possible to be blocked.
- The script tries to find the best match for each faculty member based on name and affiliation.
- The script logs all activities to the database for tracking and debugging.

## Troubleshooting

If you encounter issues with Google Scholar blocking requests:

1. Increase the delay between requests (`--delay` option)
2. Run with visible browser (`--headless false`) to see what's happening
3. Check the logs in the `googlescholar.scrape_log` table for error messages
4. Try using a different IP address or VPN
