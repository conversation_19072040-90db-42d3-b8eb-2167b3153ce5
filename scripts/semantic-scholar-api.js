#!/usr/bin/env node

/**
 * Semantic Scholar API Client
 *
 * This script fetches publication data from Semantic Scholar API for faculty members
 * and stores it in the database.
 */

const axios = require('axios');
const { Pool } = require('pg');
const dotenv = require('dotenv');
const { program } = require('commander');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config();

// Configure command line options
program
  .option('-l, --limit <number>', 'Limit the number of faculty members to process', parseInt)
  .option('-o, --offset <number>', 'Offset for faculty members to process', parseInt, 0)
  .option('-d, --delay <number>', 'Delay between requests in milliseconds', parseInt, 1000)
  .option('-r, --retry <number>', 'Number of retries for failed requests', parseInt, 3)
  .option('-v, --verbose', 'Enable verbose logging')
  .option('-f, --force', 'Force update even if already processed')
  .option('--faculty-id <id>', 'Process only a specific faculty ID', parseInt)
  .parse(process.argv);

const options = program.opts();

// Configure database connections
const ingestPool = new Pool({
  connectionString: process.env.POSTGRES_URL_ingest,
  ssl: { rejectUnauthorized: false }
});

// Connection to the main database containing faculty data
const mainPool = new Pool({
  connectionString: process.env.POSTGRES_URL_new,
  ssl: { rejectUnauthorized: false }
});

// Create a logger
const logger = {
  info: (message) => {
    console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
  },
  error: (message, error) => {
    console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error || '');
  },
  debug: (message) => {
    if (options.verbose) {
      console.log(`[DEBUG] ${new Date().toISOString()}: ${message}`);
    }
  }
};

// Helper function to log to database
async function logToDatabase(facultyId, authorId, logType, status, message) {
  try {
    await ingestPool.query(
      'INSERT INTO semanticscholar.scrape_log (faculty_id, scholar_id, log_type, status, message) VALUES ($1, $2, $3, $4, $5)',
      [facultyId, authorId, logType, status, message]
    );
  } catch (error) {
    logger.error(`Failed to log to database: ${error.message}`);
  }
}

// Helper function to delay execution
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to get faculty members from database
async function getFacultyMembers() {
  try {
    // Try to get faculty data from the main database
    try {
      let query = `
        SELECT faculty_id, first_name, last_name, work_email
        FROM uw.faculty
        WHERE job_family = 'Regular Faculty'
        AND is_deleted = FALSE
      `;

      const params = [];

      if (options.facultyId) {
        query += ' AND faculty_id = $1';
        params.push(options.facultyId);
      }

      // Add ORDER BY to ensure consistent results with offset
      query += ' ORDER BY faculty_id';

      // Add OFFSET if specified
      if (options.offset && options.offset > 0) {
        query += ' OFFSET $' + (params.length + 1);
        params.push(options.offset);
      }

      // Add LIMIT if specified
      if (options.limit) {
        query += ' LIMIT $' + (params.length + 1);
        params.push(options.limit);
      }

      const result = await mainPool.query(query, params);

      if (result.rows.length > 0) {
        logger.info(`Found ${result.rows.length} faculty members in the main database.`);
        return result.rows;
      } else {
        logger.info('No faculty members found in the main database. Using mock data for testing.');
      }
    } catch (error) {
      logger.info(`Error querying faculty data from main database: ${error.message}. Using mock data for testing.`);
    }

    // If we couldn't get data from the main database, use mock data
    return [
      { faculty_id: 1, first_name: 'John', last_name: 'Doe', work_email: '<EMAIL>' },
      { faculty_id: 2, first_name: 'Jane', last_name: 'Smith', work_email: '<EMAIL>' },
      { faculty_id: 3, first_name: 'Michael', last_name: 'Johnson', work_email: '<EMAIL>' },
      { faculty_id: 4, first_name: 'Emily', last_name: 'Williams', work_email: '<EMAIL>' },
      { faculty_id: 5, first_name: 'David', last_name: 'Brown', work_email: '<EMAIL>' }
    ];
  } catch (error) {
    logger.error('Failed to get faculty members:', error);
    throw error;
  }
}

// Function to search for a faculty member on Semantic Scholar
async function searchSemanticScholar(firstName, lastName) {
  try {
    const searchQuery = `${firstName} ${lastName}`;
    logger.debug(`Searching for: ${searchQuery}`);

    // Create results directory if it doesn't exist
    const resultsDir = path.join(__dirname, 'semantic_scholar_results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    // Search for author
    const searchUrl = `https://api.semanticscholar.org/graph/v1/author/search?query=${encodeURIComponent(searchQuery)}&fields=name,affiliations,paperCount,citationCount,hIndex`;
    logger.debug(`Search URL: ${searchUrl}`);

    const response = await axios.get(searchUrl);

    // Save response to file for debugging
    if (options.verbose) {
      const resultsPath = path.join(resultsDir, `search_${firstName}_${lastName}.json`);
      fs.writeFileSync(resultsPath, JSON.stringify(response.data, null, 2));
      logger.debug(`Saved search results to ${resultsPath}`);
    }

    if (!response.data || !response.data.data || response.data.data.length === 0) {
      logger.debug(`No authors found for ${searchQuery}`);
      return null;
    }

    // Filter authors by affiliation (looking for University of Waterloo)
    const authors = response.data.data;

    // First try to find exact matches with University of Waterloo affiliation
    const uwAuthors = authors.filter(author => {
      if (!author.affiliations) return false;
      return author.affiliations.some(affiliation =>
        affiliation.toLowerCase().includes('waterloo') ||
        affiliation.toLowerCase().includes('university of waterloo')
      );
    });

    if (uwAuthors.length > 0) {
      logger.debug(`Found ${uwAuthors.length} authors with Waterloo affiliation`);
      return uwAuthors;
    }

    // If no Waterloo-affiliated authors found, return all authors
    logger.debug(`Found ${authors.length} authors without filtering for affiliation`);
    return authors;
  } catch (error) {
    logger.error(`Search failed for ${firstName} ${lastName}:`, error);
    return null;
  }
}

// Function to get author details from Semantic Scholar
async function getAuthorDetails(authorId) {
  try {
    logger.debug(`Getting details for author ID: ${authorId}`);

    // Create results directory if it doesn't exist
    const resultsDir = path.join(__dirname, 'semantic_scholar_results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    // Get author details
    const detailsUrl = `https://api.semanticscholar.org/graph/v1/author/${authorId}?fields=name,affiliations,paperCount,citationCount,hIndex,papers.title,papers.year,papers.citationCount,papers.authors,papers.url,papers.venue`;
    logger.debug(`Details URL: ${detailsUrl}`);

    const response = await axios.get(detailsUrl);

    // Save response to file for debugging
    if (options.verbose) {
      const resultsPath = path.join(resultsDir, `details_${authorId}.json`);
      fs.writeFileSync(resultsPath, JSON.stringify(response.data, null, 2));
      logger.debug(`Saved author details to ${resultsPath}`);
    }

    return response.data;
  } catch (error) {
    logger.error(`Failed to get details for author ${authorId}:`, error);
    return null;
  }
}

// Function to save author profile to database
async function saveAuthorProfile(authorData, facultyId) {
  try {
    logger.debug(`Saving author profile for ${authorData.name}`);

    // Insert author profile
    const query = `
      INSERT INTO semanticscholar.author_profile (
        scholar_id, faculty_id, name, affiliation,
        citations_all, h_index_all, profile_url
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (scholar_id)
      DO UPDATE SET
        faculty_id = $2,
        name = $3,
        affiliation = $4,
        citations_all = $5,
        h_index_all = $6,
        profile_url = $7,
        last_updated = NOW()
      RETURNING author_id
    `;

    const result = await ingestPool.query(query, [
      authorData.authorId,
      facultyId,
      authorData.name,
      Array.isArray(authorData.affiliations) ? authorData.affiliations.join(', ') : authorData.affiliations || '',
      authorData.citationCount || 0,
      authorData.hIndex || 0,
      `https://www.semanticscholar.org/author/${authorData.authorId}`
    ]);

    return result.rows[0].author_id;
  } catch (error) {
    logger.error(`Failed to save author profile: ${error.message}`);
    return null;
  }
}

// Function to save publications to database
async function savePublications(papers, scholarId) {
  try {
    logger.debug(`Saving ${papers.length} publications for scholar ID: ${scholarId}`);

    for (const paper of papers) {
      try {
        // Insert publication - using a simpler approach without ON CONFLICT
        // First check if the publication already exists
        const existingPub = await ingestPool.query(
          `SELECT publication_id FROM semanticscholar.publication
           WHERE scholar_id = $1 AND title = $2`,
          [scholarId, paper.title]
        );

        if (existingPub.rows.length > 0) {
          // Update existing publication
          await ingestPool.query(
            `UPDATE semanticscholar.publication SET
              authors = $1,
              venue = $2,
              year = $3,
              citations = $4,
              publication_url = $5,
              last_updated = NOW()
             WHERE publication_id = $6`,
            [
              paper.authors ? paper.authors.map(a => a.name).join(', ') : '',
              paper.venue || '',
              paper.year || null,
              paper.citationCount || 0,
              paper.url || '',
              existingPub.rows[0].publication_id
            ]
          );
        } else {
          // Insert new publication
          await ingestPool.query(
            `INSERT INTO semanticscholar.publication (
              scholar_id, title, authors, venue, year, citations, publication_url
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
            [
              scholarId,
              paper.title,
              paper.authors ? paper.authors.map(a => a.name).join(', ') : '',
              paper.venue || '',
              paper.year || null,
              paper.citationCount || 0,
              paper.url || ''
            ]
          );
        }
      } catch (paperError) {
        logger.error(`Failed to save publication "${paper.title}": ${paperError.message}`);
      }
    }

    return true;
  } catch (error) {
    logger.error(`Failed to save publications: ${error.message}`);
    return false;
  }
}

// Function to process a faculty member
async function processFacultyMember(faculty) {
  try {
    logger.info(`Processing faculty member: ${faculty.first_name} ${faculty.last_name} (ID: ${faculty.faculty_id})`);

    // Check if already processed
    if (!options.force) {
      const existingSearch = await ingestPool.query(
        'SELECT * FROM semanticscholar.faculty_search WHERE faculty_id = $1 AND status = $2',
        [faculty.faculty_id, 'completed']
      );

      if (existingSearch.rows.length > 0) {
        logger.debug(`Faculty member ${faculty.faculty_id} already processed, skipping`);
        return;
      }
    }

    // Create search record
    const searchQuery = `${faculty.first_name} ${faculty.last_name}`;
    const searchRecord = await ingestPool.query(
      'INSERT INTO semanticscholar.faculty_search (faculty_id, first_name, last_name, search_query, status) VALUES ($1, $2, $3, $4, $5) RETURNING search_id',
      [faculty.faculty_id, faculty.first_name, faculty.last_name, searchQuery, 'pending']
    );

    const searchId = searchRecord.rows[0].search_id;

    // Search for faculty member on Semantic Scholar
    let retryCount = 0;
    let authors = null;

    while (retryCount < options.retry && !authors) {
      if (retryCount > 0) {
        logger.debug(`Retry ${retryCount} for ${faculty.first_name} ${faculty.last_name}`);
        await delay(options.delay * 2);
      }

      authors = await searchSemanticScholar(faculty.first_name, faculty.last_name);
      retryCount++;
    }

    if (!authors || authors.length === 0) {
      logger.debug(`No authors found for ${faculty.first_name} ${faculty.last_name}`);
      await ingestPool.query(
        'UPDATE semanticscholar.faculty_search SET status = $1, error_message = $2 WHERE search_id = $3',
        ['completed', 'No authors found', searchId]
      );
      return;
    }

    // Find the most likely author match
    const bestMatch = findBestAuthorMatch(authors, faculty);

    if (!bestMatch) {
      logger.debug(`No suitable match found for ${faculty.first_name} ${faculty.last_name}`);
      await ingestPool.query(
        'UPDATE semanticscholar.faculty_search SET status = $1, error_message = $2 WHERE search_id = $3',
        ['completed', 'No suitable match found', searchId]
      );
      return;
    }

    // Update search record with scholar ID
    await ingestPool.query(
      'UPDATE semanticscholar.faculty_search SET scholar_id = $1 WHERE search_id = $2',
      [bestMatch.authorId, searchId]
    );

    // Get author details
    const authorDetails = await getAuthorDetails(bestMatch.authorId);

    if (!authorDetails) {
      logger.error(`Failed to get details for ${faculty.first_name} ${faculty.last_name}`);
      await ingestPool.query(
        'UPDATE semanticscholar.faculty_search SET status = $1, error_message = $2 WHERE search_id = $3',
        ['failed', 'Failed to get author details', searchId]
      );
      return;
    }

    // Save author profile
    const authorId = await saveAuthorProfile(authorDetails, faculty.faculty_id);

    if (!authorId) {
      logger.error(`Failed to save profile for ${faculty.first_name} ${faculty.last_name}`);
      await ingestPool.query(
        'UPDATE semanticscholar.faculty_search SET status = $1, error_message = $2 WHERE search_id = $3',
        ['failed', 'Failed to save author profile', searchId]
      );
      return;
    }

    // Save publications
    if (authorDetails.papers && authorDetails.papers.length > 0) {
      const savedPublications = await savePublications(authorDetails.papers, bestMatch.authorId);

      if (!savedPublications) {
        logger.error(`Failed to save publications for ${faculty.first_name} ${faculty.last_name}`);
      }
    }

    // Update search record as completed
    await ingestPool.query(
      'UPDATE semanticscholar.faculty_search SET status = $1 WHERE search_id = $2',
      ['completed', searchId]
    );

    await logToDatabase(faculty.faculty_id, bestMatch.authorId, 'search', 'success', 'Successfully processed faculty member');
    logger.info(`Successfully processed ${faculty.first_name} ${faculty.last_name} (Author ID: ${bestMatch.authorId})`);

    // Add delay between faculty members
    await delay(options.delay);
  } catch (error) {
    logger.error(`Error processing faculty member ${faculty.faculty_id}:`, error);
    await logToDatabase(faculty.faculty_id, null, 'search', 'failed', error.message);

    // Update search record with error
    await ingestPool.query(
      'UPDATE semanticscholar.faculty_search SET status = $1, error_message = $2 WHERE faculty_id = $3',
      ['failed', error.message, faculty.faculty_id]
    );
  }
}

// Function to find the best author match
function findBestAuthorMatch(authors, faculty) {
  if (!authors || authors.length === 0) {
    return null;
  }

  // If there's only one author, return it
  if (authors.length === 1) {
    return authors[0];
  }

  // Calculate match score for each author
  const scoredAuthors = authors.map(author => {
    let score = 0;

    // Check name match
    const authorName = author.name.toLowerCase();
    const facultyFirstName = faculty.first_name.toLowerCase();
    const facultyLastName = faculty.last_name.toLowerCase();

    if (authorName.includes(facultyFirstName) && authorName.includes(facultyLastName)) {
      score += 3;
    } else if (authorName.includes(facultyFirstName) || authorName.includes(facultyLastName)) {
      score += 1;
    }

    // Check affiliation match
    if (author.affiliations) {
      const affiliations = Array.isArray(author.affiliations)
        ? author.affiliations.join(' ').toLowerCase()
        : author.affiliations.toLowerCase();

      if (affiliations.includes('waterloo') || affiliations.includes('university of waterloo')) {
        score += 3;
      } else if (affiliations.includes('university') || affiliations.includes('college')) {
        score += 1;
      }
    }

    // Bonus for higher paper count and citations
    if (author.paperCount > 10) score += 1;
    if (author.citationCount > 100) score += 1;

    return { ...author, score };
  });

  // Sort by score (descending)
  scoredAuthors.sort((a, b) => b.score - a.score);

  // Return the author with the highest score if it's above a threshold
  return scoredAuthors[0].score >= 2 ? scoredAuthors[0] : null;
}

// Main function to process faculty members
async function processFacultyMembers() {
  try {
    logger.info('Starting Semantic Scholar data collection');

    // Get faculty members
    const facultyMembers = await getFacultyMembers();
    logger.info(`Found ${facultyMembers.length} faculty members to process`);

    // Process each faculty member
    for (const faculty of facultyMembers) {
      await processFacultyMember(faculty);
    }

    logger.info('Finished processing all faculty members');
  } catch (error) {
    logger.error('Fatal error:', error);
  } finally {
    // Close database connections
    await ingestPool.end();
    await mainPool.end();
    logger.info('Data collection finished');
  }
}

// Run the main function
processFacultyMembers().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
