#!/usr/bin/env node

/**
 * Test script for GoogleScholarPublicationRepository
 *
 * This script tests the GoogleScholarPublicationRepository by fetching faculty members
 * and their publications from the googlescholar schema.
 */

const { Pool } = require('pg');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configure database connection
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL_new,
  ssl: { rejectUnauthorized: false }
});

// Create a logger
const logger = {
  info: (message) => {
    console.log(`[INFO] ${new Date().toISOString()}: ${message}`);
  },
  error: (message, error) => {
    console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error || '');
  },
  debug: (message) => {
    console.log(`[DEBUG] ${new Date().toISOString()}: ${message}`);
  }
};

async function testGoogleScholarRepo() {
  try {
    logger.info('Testing GoogleScholarPublicationRepository');

    // Test 1: Check if googlescholar schema exists
    logger.info('Test 1: Checking if googlescholar schema exists');
    const schemaResult = await pool.query(`
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name = 'googlescholar'
    `);

    if (schemaResult.rows.length === 0) {
      logger.error('googlescholar schema does not exist');
      return;
    }

    logger.info('googlescholar schema exists');

    // Test 2: Check if author_profile table exists and has data
    logger.info('Test 2: Checking if author_profile table has data');
    const authorResult = await pool.query(`
      SELECT COUNT(*) as count
      FROM googlescholar.author_profile
    `);

    const authorCount = parseInt(authorResult.rows[0].count);
    logger.info(`Found ${authorCount} author profiles`);

    // Test 3: Check if publication table exists and has data
    logger.info('Test 3: Checking if publication table has data');
    const publicationResult = await pool.query(`
      SELECT COUNT(*) as count
      FROM googlescholar.publication
    `);

    const publicationCount = parseInt(publicationResult.rows[0].count);
    logger.info(`Found ${publicationCount} publications`);

    // Test 4: Get faculty members with author profiles
    logger.info('Test 4: Getting faculty members with author profiles');
    const facultyResult = await pool.query(`
      SELECT f.faculty_id, f.first_name, f.last_name, f.work_email, ap.scholar_id
      FROM uw.faculty f
      JOIN googlescholar.author_profile ap ON f.faculty_id = ap.faculty_id
      WHERE f.is_deleted = FALSE
      LIMIT 5
    `);

    logger.info(`Found ${facultyResult.rows.length} faculty members with author profiles`);

    // Test 5: Get publications for a faculty member
    if (facultyResult.rows.length > 0) {
      const faculty = facultyResult.rows[0];
      logger.info(`Test 5: Getting publications for faculty member: ${faculty.first_name} ${faculty.last_name}`);

      const facultyPublications = await pool.query(`
        SELECT p.*
        FROM googlescholar.publication p
        JOIN googlescholar.author_profile ap ON p.scholar_id = ap.scholar_id
        WHERE ap.faculty_id = $1
        ORDER BY p.last_updated DESC, p.year DESC
        LIMIT 5
      `, [faculty.faculty_id]);

      logger.info(`Found ${facultyPublications.rows.length} publications for faculty member`);

      if (facultyPublications.rows.length > 0) {
        logger.info('Sample publication:');
        console.log(facultyPublications.rows[0]);
      }
    }

    logger.info('All tests completed successfully');
  } catch (error) {
    logger.error('Error testing GoogleScholarPublicationRepository:', error);
  } finally {
    // Close the database connection
    await pool.end();
  }
}

// Run the test
testGoogleScholarRepo();
