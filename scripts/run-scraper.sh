#!/bin/bash

# <PERSON><PERSON>t to run the Google Scholar scraper

# Change to the script directory
cd "$(dirname "$0")"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js to run this script."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "npm is not installed. Please install npm to run this script."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Run the scraper
echo "Running Google Scholar scraper..."
node scrape-google-scholar.js "$@"

# Check exit status
if [ $? -eq 0 ]; then
    echo "Scraper completed successfully."
else
    echo "<PERSON><PERSON><PERSON> failed with exit code $?."
    exit 1
fi
