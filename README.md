# Amelia - Faculty Information System

A Next.js application for managing faculty information, publications, and activities.

## Project Structure

```
/
├── app/                    # Next.js App Router
│   ├── api/                # API routes
│   ├── dashboard/          # Dashboard pages
│   ├── lib/                # App-specific utilities
│   └── ui/                 # UI components
├── components/             # Shared React components
├── lib/                    # Shared utilities and business logic
│   ├── error-handler.ts    # Error handling utilities
│   ├── repositories/       # Data access layer
│   └── utils/              # Utility functions
├── public/                 # Static assets
└── dbscripts/              # Database scripts
    ├── ddl/                # Data definition language scripts
    └── dml/                # Data manipulation language scripts
```

## Key Features

- Authentication and authorization with NextAuth.js
- Role-based access control
- Faculty profile management
- Publication tracking
- Activity management
- Administrative tools

## Technology Stack

- **Frontend**: Next.js, React, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS, Radix UI

## Development

### Git Workflow

- Main branch is protected
- Develop branch is for development
- Any commit will trigger release on Vercel

### Local Setup

1. Create `.env` file locally
2. Run `pnpm dev` to start local server
3. Run `pnpm build` before commit to check any errors

### Documentation

- Docs branch is for documentation
- All detailed flows will be documented there

### Database

- Use [ByteBase](https://bytebase-992621902973.us-central1.run.app/) to manage the database
- Register first and then you can be added to the project
- [Data model documentation](dbscripts/data_model.md)

## Architecture

### Repository Pattern

The application uses the repository pattern to abstract database access:

```typescript
// Example usage
const publicationRepo = new PublicationRepository();
const publications = await publicationRepo.getFilteredPublications(query, page);
```

### Middleware

Authentication and authorization are handled by Next.js middleware:

```typescript
// middleware.ts
export async function middleware(req: NextRequest) {
  // Authentication and authorization logic
}
```

### Error Handling

Centralized error handling for API routes:

```typescript
try {
  // API logic
} catch (error) {
  const { error: errorMessage, status } = handleApiError(error);
  return NextResponse.json({ error: errorMessage }, { status });
}
```
