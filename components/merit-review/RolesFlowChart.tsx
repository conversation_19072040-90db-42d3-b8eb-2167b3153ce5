'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, UserCheck, FileText, CheckCircle, AlertCircle } from 'lucide-react';

interface RoleFunction {
  [key: string]: string[];
}

interface FacultyRole {
  faculty_institution_role_id: number;
  faculty_id: number;
  institution_role_id: number;
  unit_id: number;
  role_name: string;
  first_name: string;
  last_name: string;
  preferred_name?: string;
  work_email: string;
}

interface CommitteeMember {
  id: number;
  faculty_id: number;
  committee_name: string;
  first_name: string;
  last_name: string;
  preferred_name?: string;
  work_email: string;
}

interface RolesFlowChartProps {
  unitId: number;
}

export function RolesFlowChart({ unitId }: RolesFlowChartProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [facultyRoles, setFacultyRoles] = useState<FacultyRole[]>([]);
  const [committeeMembers, setCommitteeMembers] = useState<CommitteeMember[]>([]);
  const [roleFunctions, setRoleFunctions] = useState<RoleFunction>({});

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/merit-review/roles?unit_id=${unitId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch roles");
        }

        const data = await response.json();
        setFacultyRoles(data.facultyRoles);
        setCommitteeMembers(data.committeeMembers);
        setRoleFunctions(data.roleFunctions);
      } catch (error) {
        console.error("Error fetching roles:", error);
        setError(error instanceof Error ? error.message : "Failed to fetch roles");
      } finally {
        setLoading(false);
      }
    };

    if (unitId) {
      fetchRoles();
    }
  }, [unitId]);

  // Group faculty by role
  const roleGroups = facultyRoles.reduce((groups: Record<string, FacultyRole[]>, role) => {
    if (!groups[role.role_name]) {
      groups[role.role_name] = [];
    }
    groups[role.role_name].push(role);
    return groups;
  }, {});

  // Get display name for faculty
  const getDisplayName = (faculty: FacultyRole | CommitteeMember) => {
    return faculty.preferred_name
      ? `${faculty.preferred_name} ${faculty.last_name}`
      : `${faculty.first_name} ${faculty.last_name}`;
  };

  if (loading) {
    return <div>Loading roles...</div>;
  }

  if (error) {
    return (
      <div className="text-red-500">
        <AlertCircle className="h-4 w-4 inline mr-2" />
        Error loading roles: {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Flow Chart */}
      <div className="border rounded-lg p-4 bg-white">
        <h3 className="text-lg font-medium mb-4">Merit Review Process Roles</h3>
        <div className="flex flex-wrap gap-4 justify-center">
          {Object.entries(roleGroups).map(([roleName, faculty]) => {
            const functions = roleFunctions[roleName.toLowerCase()] || [];
            return (
              <Card key={roleName} className="w-80 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {roleName.includes('admin') ? (
                      <Users className="h-5 w-5 text-blue-500" />
                    ) : roleName.includes('approver') ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <FileText className="h-5 w-5 text-gray-500" />
                    )}
                    <h4 className="font-medium">{roleName}</h4>
                  </div>

                  {/* Role Functions */}
                  <div className="text-sm text-gray-600 space-y-1 mb-3">
                    {functions.map((func, i) => (
                      <div key={i} className="flex items-start">
                        <span className="text-gray-400 mr-1">•</span>
                        <span>{func}</span>
                      </div>
                    ))}
                  </div>

                  {/* Faculty Members */}
                  {faculty.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <h5 className="text-xs font-medium text-gray-500 mb-2">ASSIGNED FACULTY:</h5>
                      <div className="space-y-2">
                        {faculty.map((f) => (
                          <div key={f.faculty_institution_role_id} className="text-xs">
                            <div className="font-medium">{getDisplayName(f)}</div>
                            <div className="text-gray-500">{f.work_email}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}

          {/* Committee Member Card */}
          {committeeMembers.length > 0 && (
            <Card className="w-80 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <UserCheck className="h-5 w-5 text-orange-500" />
                  <h4 className="font-medium">Committee Member</h4>
                </div>

                {/* Role Functions */}
                <div className="text-sm text-gray-600 space-y-1 mb-3">
                  {(roleFunctions['committee_member'] || []).map((func, i) => (
                    <div key={i} className="flex items-start">
                      <span className="text-gray-400 mr-1">•</span>
                      <span>{func}</span>
                    </div>
                  ))}
                </div>

                {/* Committee Members */}
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <h5 className="text-xs font-medium text-gray-500 mb-2">COMMITTEE MEMBERS:</h5>
                  <div className="space-y-2">
                    {committeeMembers.map((member) => (
                      <div key={member.id} className="text-xs">
                        <div className="font-medium">{getDisplayName(member)}</div>
                        <div className="text-gray-500">{member.work_email}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>


    </div>
  );
}
