interface CustomToolbarProps {
  id: string;
}

export default function CustomToolbar({ id }: CustomToolbarProps) {


  return (
    <div id={`toolbar-${id}`} className="rounded-t-md p-2 border border-b-0 border-gray-300">
     
      <button className="ql-bold" />
      <button className="ql-italic" />
      <button className="ql-underline" />
      <button className="ql-link" />
      <button className="ql-list" value="ordered" />
      <button className="ql-list" value="bullet"></button>

    </div>
  );
}