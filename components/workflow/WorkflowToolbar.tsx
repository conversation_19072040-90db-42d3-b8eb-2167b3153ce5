'use client';

import { useWorkflowStore } from '@/lib/store/workflowStore';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Save } from 'lucide-react';
import { NodeType } from './CustomNode';

const NODE_TYPES: Array<{ type: NodeType; label: string }> = [
  { type: 'start', label: 'Start' },
  { type: 'process', label: 'Process' },
  { type: 'review', label: 'Review' },
  { type: 'document', label: 'Document' },
  { type: 'submission', label: 'Submission' },
  { type: 'notification', label: 'Notification' },
  { type: 'request', label: 'Request' },
  { type: 'end', label: 'End' },
];

export function WorkflowToolbar() {
  const { addNode, deleteNode, selectedNode } = useWorkflowStore();

  const handleAddNode = (type: NodeType) => {
    const newNode = {
      id: `${type}-${Date.now()}`,
      type: 'custom',
      position: { x: 100, y: 100 },
      data: {
        label: `${type} Node`,
        type,
        status: 'pending' as const,
        description: `New ${type} node`
      },
    };
    addNode(newNode);
  };

  const handleDeleteNode = () => {
    if (selectedNode) {
      deleteNode(selectedNode);
    }
  };

  return (
    <div className="flex items-center gap-2 p-4 border-b">
      <div className="flex gap-2">
        {NODE_TYPES.map(({ type, label }) => (
          <Button
            key={type}
            variant="outline"
            size="sm"
            onClick={() => handleAddNode(type)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add {label}
          </Button>
        ))}
      </div>
      <div className="flex gap-2 ml-auto">
        <Button
          variant="destructive"
          size="sm"
          disabled={!selectedNode}
          onClick={handleDeleteNode}
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Delete Node
        </Button>
        <Button variant="default" size="sm">
          <Save className="w-4 h-4 mr-2" />
          Save Workflow
        </Button>
      </div>
    </div>
  );
} 