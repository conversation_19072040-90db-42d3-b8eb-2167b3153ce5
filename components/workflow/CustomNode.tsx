'use client';

import { <PERSON><PERSON>, Posi<PERSON> } from 'reactflow';
import { cn } from '@/lib/utils';
import { useState, useRef, useEffect } from 'react';
import { useWorkflowStore, NodeStatus } from '@/lib/store/workflowStore';
import { CheckCircle2, XCircle, Clock, Circle, FileText, Users, Send, Flag, AlertCircle } from 'lucide-react';

export type NodeType = 'start' | 'process' | 'review' | 'document' | 'submission' | 'notification' | 'request' | 'end';

interface CustomNodeProps {
  data: {
    label: string;
    type: NodeType;
    status: NodeStatus;
    description?: string;
    config?: Record<string, any>;
  };
  selected: boolean;
  id: string;
}

const statusConfig = {
  success: { icon: CheckCircle2, color: 'text-green-500', bgColor: 'bg-green-50 border-green-200' },
  failed: { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-50 border-red-200' },
  in_progress: { icon: Clock, color: 'text-blue-500', bgColor: 'bg-blue-50 border-blue-200' },
  pending: { icon: Circle, color: 'text-gray-500', bgColor: 'bg-gray-50 border-gray-200' },
} as const;

const typeConfig = {
  start: { icon: Flag, color: 'text-purple-500' },
  process: { icon: FileText, color: 'text-blue-500' },
  review: { icon: Users, color: 'text-orange-500' },
  document: { icon: FileText, color: 'text-gray-500' },
  submission: { icon: Send, color: 'text-green-500' },
  notification: { icon: AlertCircle, color: 'text-blue-500' },
  request: { icon: Send, color: 'text-yellow-500' },
  end: { icon: Flag, color: 'text-green-500' },
} as const;

export function CustomNode({ data, selected, id }: CustomNodeProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(data.label);
  const inputRef = useRef<HTMLInputElement>(null);
  const { updateNode } = useWorkflowStore();
  const StatusIcon = statusConfig[data.status].icon;
  const TypeIcon = typeConfig[data.type]?.icon || FileText;

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    setIsEditing(false);
    updateNode(id, { label });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleBlur();
    }
  };

  return (
    <div
      className={cn(
        'px-3 py-2 shadow-md rounded-md border-2 min-w-[160px] max-w-[160px] group relative',
        statusConfig[data.status].bgColor,
        selected ? 'border-blue-500' : 'border-transparent'
      )}
    >
      <Handle type="target" position={Position.Left} className="!bg-gray-400" />
      <div className="flex items-center gap-2">
        <div className={cn('p-1 rounded-full', statusConfig[data.status].bgColor)}>
          <TypeIcon className={cn('w-4 h-4', typeConfig[data.type]?.color)} />
        </div>
        <div className="flex-1 truncate">
          <div className="font-medium text-sm truncate" onDoubleClick={handleDoubleClick}>
            {isEditing ? (
              <input
                ref={inputRef}
                type="text"
                value={label}
                onChange={(e) => setLabel(e.target.value)}
                onBlur={handleBlur}
                onKeyDown={handleKeyDown}
                className="w-full px-1 py-0.5 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            ) : (
              label
            )}
          </div>
        </div>
        <StatusIcon className={cn('w-4 h-4', statusConfig[data.status].color)} />
      </div>
      {data.description && (
        <div className="absolute left-0 top-full mt-2 bg-white border rounded-md shadow-lg p-3 w-64 z-10 text-sm hidden group-hover:block">
          <div className="font-medium mb-1">{label}</div>
          <div className="text-gray-600">{data.description}</div>
        </div>
      )}
      <Handle type="source" position={Position.Right} className="!bg-gray-400" />
    </div>
  );
} 