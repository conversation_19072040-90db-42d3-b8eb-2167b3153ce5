# Faculty Lifecycle Information System (FLIS) by Intelicampus

## Branding and Stratrgy
### [Vision](FLC/overview.md)
### [Intelicampus Brands](FLC/brands.md)
### [Comparison with Interfolio](FLC/compare_interfolio.md)

## System Access

###   - System Roles in Intelicampus: [System Roles](FLC/system_roles.md)

###   - User Login: [System Login](FLC/user_login_flow.md)

## Workflows

### Position Control: [Complement Planning & Position Control (CPPC) Workflow](FLC/position_control.md)

### Faculty Recruitment

### Appointment (hiring) Process

### Merit Review: [Merit Review - Workflow](FLC/merit_review.md)

### Tenure, Permanence and Promotion (TPP): [TPP workflow](FLC/TPP-flow.md)

### Faculty Record Management: [FRM Workflow](FLC/faculty_record_mgmt.md)

### Request for Sabbatical Leave: [RSL workflow](FLC/Sabbatical.md)

## UI

### Navigation Bar: [Left Nav Bar](FLC/left_nav_bar.md)
### Personal Profile: [Personal Profile](FLC/personal_profile.md)
### Public Profile
### EngRecords: [How to improve data display](FLC/engrecords.md)

## Data Integration

### Institutional Data: [UW Data Sources](FLC/data_sources.md)
### Publication & Citation Data: [Citation Sources](FLC/citation.md)

