# Faculty Records Management: Visualization

## Sections
Scope includes: personal information; educational background; work experiences; teaching, scholarship/research, and service.

A list of possible sections:

Personal info
Education
Work experience
Professional Licensures & Certifications
Teaching
Advising, mentorship and supervision
Scholarships (publications, etc.)
Funding (applied and awarded)
Awards & Honours
Service
Professional Development

## Data Input
There are 3 ways of getting information into Intelicampus:

Upload existing information (e.g. upload a CV)
Retrieve information from internal and external data sources (e.g. bibliographic sources)
Manual entry
Examples of bibliographic sources:

ORCID: ORCID is a free, unique, persistent identifier (PID) for individuals to use as they engage in research, scholarship, and innovation activities.
DBLP
Google Scholar: Google Scholar is a freely accessible web search engine that indexes the full text or metadata of scholarly literature across an array of publishing formats and disciplines.
Semantic Scholar
PubMed
ResearchGate
CiteSeerX
arXiv.org
bioRxiv.org
SSRN
Web of Science
CV systems that accept ORCID: NIH Biosketch, ScienCV, CiênciaVitae, Portuguese FCT, and OJS



## Visualization 
This section describes visualization required for different users.

### Areas of Interest

- Demographics
  - Gender
  - Age
- Stream & Rank
  - Stream: tenure, teaching
  - Rank: Assistant, Associate and Full Professor
- Faculty Performance Insights (FPI) in Teaching, Research & Service

### Faculty User
The following views would be particularly helpful:
1. Career pathway: from Assistant Professor --> Associate Professor --> Full Professor. Indicate the current sabbatical and admin leave credits, and when would be the earliest point to take the next sabbatical.
2. A quick snapshot of performance (# of publications, # of course taught, # of students supervised, etc.) in a given period. Default is last calendar year period. 
3. How performance has evolved year over year (calendar year). 

### Department Chair / School Director / Dean
Possible visualiation of interest:
1. Demographics: age (mean, median and distribution), gender, other characteristics of interest (to be defined). Option to select a research group or the entire unit.
2. Faculty count: headcount, FTEs
3. Stream and Rank: tenure- vs teachin-stream; Assistant, Associate and Full Professors.
4.Faculty Performance Insights (FPIs)
- Performance of an individual faculty member within the unit, in Teaching, Research & Service. Quantity. Quality if there is a way to measure.
- Performance of a collective (a research group or the whole unit).
- How an individual faculty member's performance has evolved over time.
- How an individual faculty member's performance relative to those in a research group or those in the same unit.

### Institutional User (AVP Faculty Relations)
Possible visualiation of interest:
Everything above, plus: How an unit's collective performance relatived to that of another unit on campus. 

