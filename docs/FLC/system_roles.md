# System Roles in Intelicampus

## Purpose 
This doc explains various roles for the FLIS prototype currently under development. 

## Roles
The prototype will require the following roles to be set up.

### User Roles
1. Public user: user who is not affiliated with an Intelicampus's institutional client. For example, external referees who are invited to write letters of reference for UW faculty members going through TPP. 
2. Affiliated user: user who is affiliated with an Intelicampus's institutional client.
3. Support for affiliated user: an assistant to an affiliated user. The assistant will be able to do everything except final approval or submission.

### Roles within Academic Units (Departments/Schools)
1. Unit Admin Support (e.g. Assistant to Department Chair)
2. Unit Admin Official (e.g. Administrative Officer)
3. Unit Approver (Department Chair or School Director)

### Roles at the Faculty Level
1. Dean's Office Admin Support
2. Dean's Office Admin Official
3. Dean or Delegate

### Roles at the Division Level
1. Division Admin Support
2. Division Admin Official
3. VP ((e.g. Provost)) or Delegate

### Roles at the Institutional Level
1. Institutional super user
2. President or Delegate

## Role Summary Table

| Category | Role Description | Role Name | DB table | User Example |
|----------|-----------------|-----------|-----------|-----------|
| User Roles | Public user (non-affiliated) | public_user | common.role, common.user_role |  |
| User Roles | System admin | system_admin | common.role, common.user_role |  |
| User Roles | Regular user (faculty) | regular_user | uw.institution_role, uw.faculty_institution_role |  <EMAIL> <EMAIL>|
| User Roles | Support for regular user (staff) | regular_user_support | uw.institution_role, uw.faculty_institution_role | <EMAIL>	 |
| Academic Unit | Unit Admin Support | department_support | uw.institution_role, uw.faculty_institution_role | <EMAIL> |
| Academic Unit | Unit Admin Official | department_admin | uw.institution_role, uw.faculty_institution_role | <EMAIL> |
| Academic Unit | Unit Approver | department_approver | uw.institution_role, uw.faculty_institution_role | <EMAIL> |
| Academic Unit | Department Graduate Officer (for sabbatical leave) | department_graduate_officer | uw.institution_role, uw.faculty_institution_role | <EMAIL>	 |
| Academic Unit | Peer Reviewer (for Sabbatical Research Grant) |  | uw.institution_role, uw.faculty_institution_role |	 |
| Faculty Level | Dean's Office Admin Support | faculty_support | uw.institution_role, uw.faculty_institution_role | <EMAIL> |
| Faculty Level | Dean's Office Admin Official | faculty_admin | uw.institution_role, uw.faculty_institution_role | <EMAIL> |
| Faculty Level | Dean or Delegate | faculty_approver | uw.institution_role, uw.faculty_institution_role | <EMAIL> |
| Division Level | Division Admin Support | division_support | uw.institution_role, uw.faculty_institution_role | <EMAIL>	 |
| Division Level | Division Admin Official | division_admin | uw.institution_role, uw.faculty_institution_role | <EMAIL>	 |
| Division Level | VP or Delegate | division_approver | uw.institution_role, uw.faculty_institution_role | <EMAIL>	 |
| Institution Level | Institutional super user | institution_admin | uw.institution_role, uw.faculty_institution_role | <EMAIL> |
| Institution Level | President or Delegate | institution_approver | uw.institution_role, uw.faculty_institution_role | <EMAIL> |

