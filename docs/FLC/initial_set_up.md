# Comments from <PERSON>
I guess this will be a hybrid process (manual + auto), options:

1. we define clear data format, and client prepare and upload the data files,
- pros: we only need define once
- cons: client will always have questions and make errors and there will be a lot back and forth
2. we ask for data from clients and they just dump whatever they have and we do the conversion
- pros: easy for client, less communication effort
- cons: more work on us
3. use ai to auto match input data with our data model









# Summary of initial set-up

Base Details
Set up foundational areas, including institution name, unit designations, faculty ranks, and academic terms

System Roles for Workflows
--> to be developed

Locations
Set up locations/campuses where courses are taught
--> to be developed

Organizational Structure
Set up the structure of academic units and subunits
--> Units Data Model

Committees
Set up committees in which faculty participate to enter committee activities
--> Committees Data Model

Faculty Classifications
Set up faculty classifications to categorize faculty members (e.g. faculty rank, tenure status)
--> to be developed

Activity Classifications
Set up activity classifications to categorize activities (e.g. journal review type)
--> to be developed

Program / Course Groupings
Set up course groupings for programs and other purposes (e.g. courses within an MBA program or a general education curriculum)
--> to be developed

Upload Form
Upload institutional data (e.g. faculty courses, faculty classifications)
--> to be developed

Profile Form
Configure input sections that to remain static over time (e.g. contact information, degrees, credentials, and work experience)
--> to be developed

Activity Input Form
Configure input sections that allow faculty members to manage their activities (e.g. teaching, research, service, professional development)
--> to be developed

Vita Templates
Configure standardized vita sections and settings
--> to be developed

FAS Configuration
Create a custom FAQ section for faculty and administrators
--> to be developed
