# User Login Flow

| Step | SSO Login | Email Login |
|------|-----------|-------------|
| 1 | Check if user email exists in `common.user` table | User must be pre-configured in `common.user` and `common.password` tables (registration not supported yet) |
| 2 | If email doesn't exist, register user by adding email and display name to `common.user` table | For first-time login, user is assigned `public_user` role |
| 3 | Assign `public_user` role to this user in `common.user_role` table | |
| 4 | No password stored in `common.password` table (SSO doesn't require password) | |
| 5 | Check email against `uw.faculty` table's `work_email` field | Check email against `uw.faculty` table's `work_email` field |
| 6 | If match found, check if job family is 'Regular Faculty' | If match found, check if job family is 'Regular Faculty' |
| 7 | If 'Regular Faculty', add `regular_user` role to `uw.faculty_institution_role` table | If 'Regular Faculty', add `regular_user` role to `uw.faculty_institution_role` table |
| 8 | Other roles must be assigned manually by `system_admin` or `institutional_admin` | Other roles must be assigned manually by `system_admin` or `institutional_admin` |

## Notes
- Both login methods follow similar faculty role assignment logic
- Only difference is in initial user registration/authentication
- Role management is critical for proper system access control
