# Faculty Complement Planning and Position Control
#### This module focuses on two tasks:
- Faculty Complement Planning;
- Position Control.

## Context
The University of Waterloo’s HR system is designed to separate positions from employees, ensuring that each position (defined as a specific, budgeted role within the organizational structure) remains distinct from the individual who occupies it. This position-based model provides clear visibility into both filled and vacant roles, enabling more effective workforce and budget planning. By maintaining independent records for positions, the University can accurately track vacancies, manage headcount, and uphold organizational continuity even as employees transition in and out of roles. This separation is essential for complement planning and position control, allowing leadership to align staffing with budgetary allocations, forecast future needs, and make data-driven decisions about recruitment, restructuring, and resource allocation.

## Scope
- all regular faculty positions including tenure- and teaching-streams.
- Potential to include staff positions in the future. 

## Purposes
- To Store and track position information including available positions and closed positions
- To collect info from units, and, for faculty searches, generate 1) Mission Critical Form and 2) “Authorization to Advertise” Form
- To manage approval flow (including rejection)
- To track current and historical incumbents of each position
- To track open positions
- To authorize/manage new positions
- Reporting

## Data Sources
- Workday position details report
- Engineering Faculty Hiring Plan
- Additional data (position control file) from Engineering

## Output
- [faculty_identification_mission_critical_form.docx](https://github.com/user-attachments/files/20140249/faculty_identification_mission_critical_form.docx)
- [caut-authorization-to-advertise-form.docx](https://github.com/user-attachments/files/20140289/caut-authorization-to-advertise-form.docx)

## Roles (for faculty position control)
- Unit head and unit admin:
  - Ability to review positions within their unit, add notes;
  - To initiate a request to add new positions, to change existing positions, and to declare a failed search.
- Dean's Office Admin Offical (for Engineering: Director of Integrated Planning, Director of Faculty Services): ability to review, edit, freeze positions within their Faculty;
- Dean or Delegate: approve changes (additions, revisions, deletions)
- VP or Delegate (Associate Vice-President, Faculty Planning & Policy): approve changes that are approved by Dean.



## Workflow for Faculty Positions
1. Unit submit a request to start a faculty search, filling out information for 1) mission critical form; 2) authorization to advertise; 3) advertisement text
2. Request reviewed & approved by Unit Head, if rejected, go to step #1
3. Request go to Donna for review, if rejected, go to step #1 – Donna able to change anything
4. Request go to Veronica for review, If rejected, go to step #1 – Veronica able to change anything
5. Request reviewed & approved by Dean, if rejected, go to step #4
6. System generates the two forms, compiles documents for Donna to submit to the Provost’s approval for review & approval
7. Provost approves, approval returns to Donna. Donna uploads Provost’s approval & record mission critical # (search authorization #)
8. System distributes approval package to Unit, Donna, Veronica and Martin, including position # (or in the absence of position #, Engineering Hiring Plan reference) and mission critical #
9 Unit can only initiate hiring process for positions approved via mission critical process

## UI Design

### Input Screen #1 - Hiring Unit(s)
- Is this a joint appointment? Yes or No
- Home department: 
  - Name of department #1
  - % of the position (if not jointly appointed, 100%)
  - Unit Head: auto-populate
- Department #2, if jointly appointed: 
  - Name of department #2
  - % of the position
  - Unit Head: auto-populate


### Input Screen #2 - Position Details
- What is the career path? (pick one): tenure stream, teaching stream, research faculty
- Is this a new position or replacement?
  - If new, 
    - choose one: 1) Addition to operating complement, or 2) Not in complement
    - Hiring plan reference: drop down list
  - If replacement for permanent loss
    - Position #:
    - Reason: choose one resignation/retirement/termination/death/other (explain)
    - faculty member occupying this position
      - Name: lastName, firstName
      - Employee ID number:
      - Terminating or anticipated retirement date:
    - Is this a bridge position? Yes or No
      - If YES, position # and anticipated end date
  - Funding sources: free text, include Work Order #(s) 
  - Note about position: free text


### Input Screen #3 - Advertisement
- Position Title:
- Body of advertisement (maximum 1,000 words):

### Input Screen #4 - Search Status (Unit to complete when a search is failed)
- Date when a failed search is declared:
- Upload
  - Mission Critical approval
  - Summary of Recruitment Efforts

### Interface for Unit
- Reporting for Unit Head and Unit Admin Official

### Interface for Dean’s Office senior staff (Veronica, Donna & Fred)
- Ability to view, query and edit (add & delete) hiring plans for faculty (phase I) and staff (phase II)
- Reporting

### Interface for Institution
- Reporting for Provost or Delegate (AVP-FPP)


