@tailwind base;
@tailwind components;
@tailwind utilities;


input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quill-editor .ql-container {
  min-height: 200px;

  border-bottom-left-radius: 0.5rem; /* optional: rounded corners */
  border-bottom-right-radius: 0.5rem; /* optional: rounded corners */
   /* Tailwind border-gray-300 */
  border: 1px solid #d1d5db;

}

.quill-editor .ql-editor {
  min-height: 180px; /* set this to ensure typing area also grows */
}

.ql-toolbar + .ql-toolbar {
  display: none !important;
}

.quill-editor .ql-toolbar {
  @apply border border-gray-300 rounded-t-lg;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    /* Improved contrast for muted text - changed from 45.1% to 35% for better readability */
    --muted-foreground: 0 0% 35%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    /* Additional accessibility colors */
    --focus-ring: 217 91% 60%;
    --error: 0 84% 60%;
    --success: 142 76% 36%;
    --warning: 38 92% 50%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Ensure page starts at the top */
    scroll-behavior: smooth;
    overflow-anchor: none;
  }

  /* Prevent automatic scrolling */
  html {
    scroll-behavior: smooth;
    height: 100%;
    overflow-y: auto;
  }

  /* Accessibility utilities */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus\:not-sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* High contrast focus indicators */
  .focus-visible\:ring-2:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px hsl(var(--focus-ring));
  }

  /* Ensure sufficient color contrast for interactive elements */
  .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }

  /* Skip link styling */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 0;
  }
}
