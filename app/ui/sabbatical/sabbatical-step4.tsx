'use client';

import { useForm, useFieldArray } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";

type FormValues = {

  lab_room_number : string;
  applicant_name : string;
  dept_name : string;
  department:string;
  abscensce_date : string;
  delg_supervisor : string;
  dept_head : string;
  lab_responsibility : string;
  emergency_name : string;
  lab_manager_name : string;
  
};

export  default function Form({ initialData = {} }: { initialData?: Partial<FormValues> }) {

  const router = useRouter();
  const [error, setError] = useState("");

  const { register, control, handleSubmit } = useForm<FormValues>({
    defaultValues: {
      ...initialData,
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      const response = await fetch('/api/sabatical/sabbatical-step4', {
        method: 'POST',
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const res = await response.json();
      if (res.success) {
        router.push('/dashboard/sabbatical/sabbatical-step5?message=Request+submitted+Complete+Step+5&status=success');
      } else {
        setError(res.error || 'Submission failed.');
      }
    } catch (err) {
      setError('Unexpected error occurred.');
    }
  };

  return (
 
    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
    {error && <p className="text-red-500">{error}</p>}
    <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">

      <h5 className="z-10 mb-1 font-bold text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500">This form must be filled out in full when:</h5>
      
       <p className='md:mt-3 leading-normal text-sm'>Absences of a nature/duration where aspects of physical laboratory supervision cannot be performed by the supervisor. This may include sabbaticals involving travel or extended absences (beyond one month) where the faculty member cannot attend the lab or adequately remotely oversee aspects of health and safety supervision. </p>

       <h5 className="md:mt-3 font-bold  z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500" >OR Please check applicable statement below:</h5>
       
      <div>
        <div className="flex flex-wrap mt-4 -mx-3">
          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex items-start">
                       <input type="radio" id="absence_is_less" {...register("lab_responsibility")} value="Absence is less than 1 month" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none float-left"/>

                      <label htmlFor='absence_is_less' className="mb-2 ml-1 font-bold text-xs text-slate-700 /80"> If the absence is less than 1 month or where the faculty member remains available to fully perform health and safety supervision duties (in-person or remotely on-call). </label>     
          </div>
          <div className="w-full max-w-full px-3 p-3 flex-0 sm:w-12/12 flex items-start">
            <input type="radio" id="lab_responsibility_assigned" {...register("lab_responsibility")} value="Permanent supervisory responsibility has been assigned." className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

            <label htmlFor='lab_responsibility_assigned' className="mb-2 ml-1 font-bold text-xs text-slate-700 /80 "> If permanent supervisory responsibility has been assigned to a competent employee lab manager
            </label>   
          </div>
          <div className="w-full max-w-full px-3 p-3 flex-0 sm:w-12/12 flex items-start">
                <input type="radio" id="no_lab" {...register("lab_responsibility")} value="Faculty member does not have a lab" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

                <label htmlFor='no_lab' className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">If the faculty member does not have a lab.</label> 
          </div>
        </div>
        <div className="flex flex-wrap mt-4 -mx-3">


          <div className="w-full max-w-full px-3 mt-0 flex-0 sm:mt-0 sm:w-12/12">
            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="lab_manager_name">Lab manager name.</label>
            <input type="text" id="lab_manager_name"  {...register("lab_manager_name")} placeholder="eg. Michael Brar" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

          </div>
          <div className="w-full max-w-full px-3 mt-5 flex-0 sm:mt-5 sm:w-12/12">

            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="emergency_name">Lab contact for emergency access.</label>
            <input type="text" id="emergency_name" {...register("emergency_name")} placeholder="eg. Michael Brar" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

          </div>
        </div>

        <div className="flex flex-wrap mt-4 -mx-3 p-4">
            <h5 className="mb-0 font-bold ">Roles & Responsibilities:</h5>
            <p className='leading-normal text-sm w-full pt-5'>
              Department Head

              <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                <li className='leading-normal text-sm'>Ensure that an appropriate delegate supervisor has been identified during the leave.</li>
                <li className='leading-normal text-sm'>If, during the leave period, adequate supervision is not in place, cease operation of the research space.</li>
              </ul>
            </p>
            <p className='leading-normal text-sm w-full pt-5'>
              <h5>Principal Investigator (Applicant)</h5>

              <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                <li className='leading-normal text-sm'>To identify health and safety related supervisor activities requiring delegation during a leave of absence. These should include, at a minimum:
                    <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                    <li className='leading-normal text-sm'>Responding to incidents and emergencies and conducting incident investigations</li>
                    <li className='leading-normal text-sm'>Conducting or overseeing monthly supervisory health and safety inspections</li>
                    <li className='leading-normal text-sm'>Carrying out laboratory training and orientations</li>
                    <li className='leading-normal text-sm'>Approving or providing written safe operating procedures</li>
                    </ul>
                </li>
                <li className='leading-normal text-sm'>Prior to starting a leave, to provide the delegate supervisor an overview of:
                    <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                    <li className='leading-normal text-sm'>Processes, equipment, materials within the labs, including any hazards that the delegate must be aware of</li>
                    <li className='leading-normal text-sm'>Rules established to ensure safe operation of equipment and processes, safe handling/disposal of materials</li>
                    <li className='leading-normal text-sm'>Research members’ competencies and limitations, to ensure workers are provided the supports they need and are not required or allowed to perform work for which they are not competent.</li>
                    <li className='leading-normal text-sm'>The Working Alone plan and its communication structure</li>
                    </ul>

                </li>
                <li className='leading-normal text-sm'>To communicate to researchers, the identity and role of the delegate supervisor during the leave of absence and the expectations of research members.</li>
              </ul>
            </p>
            <p className='leading-normal text-sm w-full pt-5'>
              Delegate Supervisor

              <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                <li className='leading-normal text-sm'>To conduct required health and safety supervisory activities during the leave of absence period.</li>
                <li className='leading-normal text-sm'>To identify and address health and safety hazards, issues or concerns identified during the leave of absence period, or to escalate issues which are not under their purview to Department Head for resolution if necessary.</li>
               
              </ul>
            </p>
        </div>
        <div className="flex flex-wrap mt-4 -mx-3">
          <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="applicant_name">Name of Applicant</label>
            <input type="text" id="applicant_name" {...register("applicant_name")} defaultValue={initialData.applicant_name} placeholder="eg. DMS890" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
          </div>
          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="lab_room_number">Building and Lab Room Number(s)</label>
            <input type="text" id="lab_room_number" {...register("lab_room_number")} placeholder="eg. DMS890" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

          </div>
        </div>
        <div className="flex flex-wrap mt-4 -mx-3">
          <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="inst_department">Department</label>
            <input type="text" id="dept_name" {...register("dept_name")} defaultValue={initialData.department} placeholder="eg. Michael Brar" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
          </div>
          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="abscensce_date">Dates of Absence</label>
            <input type="date" id="abscensce_date" {...register("abscensce_date")} placeholder="eg. Michael Brar" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
            

          </div>
        </div>
        <div className="flex flex-wrap mt-4 -mx-3">
          <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="delg_supervisor">Delegate Supervisor Name</label>
            <input type="text" id="delg_supervisor" {...register("delg_supervisor")}  placeholder="eg. Michael Brar" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
          </div>
          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
            <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Department Head Name</label>
            <input type="text" id="dept_head" {...register("dept_head")} placeholder="eg. Michael Brar" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

          </div>
        </div>


        <div className="flex justify-end mt-6">
        <button type="submit" aria-controls="address" next-form-btn=""  className="inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md bg-150 bg-x-25 hover:scale-102 active:opacity-85">Save and Continue</button>
        </div>
      </div>
    </div>

  </form>
  
  </div>


  );
}
        
      
