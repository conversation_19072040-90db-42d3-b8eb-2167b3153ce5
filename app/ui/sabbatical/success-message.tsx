'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function SuccessMessage() {

  const searchParams = useSearchParams();
  const message = searchParams.get('message');
  const status = searchParams.get('status');
  const requestId = searchParams.get('requestId');


  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setVisible(false), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);
  const [visible, setVisible] = useState(true);

  return( 
    <div>
    {message && visible && (
      <div
        className={`p-4 m-auto w-full  rounded-md mb-4 text-sm md:w-11/12 font-medium transition-opacity duration-1000 ${
          status === 'success'
            ? 'bg-green-100 text-green-700'
            : 'bg-red-100 text-red-700'
        }`}
      >
        {message}

      </div>
    )}
  </div>
)};
