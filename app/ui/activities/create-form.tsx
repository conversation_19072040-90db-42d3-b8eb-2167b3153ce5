import Link from 'next/link';

import { Button } from '@/app/ui/button';
import { createActivity } from '@/app/lib/actions';


// Define the type for initialData
interface InitialData {
  date?: string;
  venue?: string;
  attendee?: string;
  publication?: string;
  summary?: string;
}

export default function Form({ initialData = {} }: { initialData?: InitialData }) {
  return (
    <form action={createActivity}> 
      <div className="rounded-md bg-gray-50 p-4 md:p-6">

        <div className="mb-4">
          <label htmlFor="amount" className="mb-2 block text-sm font-medium">
            Date
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                type="date"
                id="date"
                name="date"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.date || ""}
                required
                />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="amount" className="mb-2 block text-sm font-medium">
            Venue
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="venue"
                name="venue"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.venue}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="amount" className="mb-2 block text-sm font-medium">
            Attendee
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="attendee"
                name="attendee"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.attendee}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="amount" className="mb-2 block text-sm font-medium">
            Publication
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="publication"
                name="publication"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.publication}
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="amount" className="mb-2 block text-sm font-medium">
            Summary
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="summary"
                name="summary"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.summary}
              />
            </div>
          </div>
        </div>


      </div>
      <div className="mt-6 flex justify-end gap-4">
        <Link
          href="/dashboard/activities"
          className="flex h-10 items-center rounded-lg bg-gray-100 px-4 text-sm font-medium text-gray-600 transition-colors hover:bg-gray-200"
        >
          Cancel
        </Link>
        <Button type="submit">Create Activity</Button>
      </div>
    </form>
  );
}
