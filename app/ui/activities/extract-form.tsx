import Link from 'next/link';
import { Button } from '@/app/ui/button';
import { extractActivity } from '@/app/lib/callai';

export default function ExtractForm() {
  return (
    <form action={extractActivity}> 
      <div className="rounded-md bg-gray-50 p-4 md:p-6">


<div className="mb-4">
  <label htmlFor="rawInput" className="mb-2 block text-sm font-medium">
    Input
  </label>
  <div className="relative mt-2 rounded-md">
    <div className="relative">
      <textarea
        id="rawInput"
        name="rawInput"
        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm h-64 resize-y"
        placeholder="What scholarly activity would you like to record - provide as much details as you like - <PERSON> will populate the fields on the right for you."
        defaultValue=""
        required
      ></textarea>
    </div>
  </div>
</div>

      </div>
      <div className="mt-6 flex justify-end gap-4">
        <Button type="submit">Populate Activity</Button>
      </div>
    </form>
  );
}
