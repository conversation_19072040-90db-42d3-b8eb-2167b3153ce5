'use client';

import EditableField from './editable-field';
import { saveEngRecordsEdit } from '@/app/lib/engrecords-actions';

interface License {
  auto_nbr: number;
  lcnse_name: string;
  lcnse_nbr: string;
  lcnse_province_name: string;
  lcnse_regn_dt: string;
  lcnse_ppe_dt: string;
  lcnse_status: string;
  lcnse_regn_status: string;
  lcnse_notes: string;
}

export default function EditableLicensing({ licenses }: { licenses: License[] }) {
  return (
    <div className="rounded-lg bg-gray-50 p-6">
      <h2 className="text-lg font-semibold mb-4">Licensing</h2>
      {licenses.length === 0 ? (
        <p className="text-gray-500">No licensing information available.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="text-left text-sm font-normal">
                <th className="px-4 py-2">License Name</th>
                <th className="px-4 py-2">License Number</th>
                <th className="px-4 py-2">Province</th>
                <th className="px-4 py-2">Registration Date</th>
                <th className="px-4 py-2">PPE Date</th>
                <th className="px-4 py-2">Status</th>
                <th className="px-4 py-2">Registration Status</th>
                <th className="px-4 py-2">Notes</th>
              </tr>
            </thead>
            <tbody>
              {licenses.map((license, index) => (
                <tr key={index} className="border-b">
                  <td className="px-4 py-2">
                    <EditableField
                      value={license.lcnse_name || ''}
                      fieldName="lcnse_name"
                      recordId={license.auto_nbr.toString()}
                      tableName="licensing"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={license.lcnse_nbr || ''}
                      fieldName="lcnse_nbr"
                      recordId={license.auto_nbr.toString()}
                      tableName="licensing"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={license.lcnse_province_name || ''}
                      fieldName="lcnse_province_name"
                      recordId={license.auto_nbr.toString()}
                      tableName="licensing"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    {license.lcnse_regn_dt && license.lcnse_regn_dt !== '0000-00-00' ?
                      new Date(license.lcnse_regn_dt).toISOString().split('T')[0] : 'N/A'}
                  </td>
                  <td className="px-4 py-2">
                    {license.lcnse_ppe_dt && license.lcnse_ppe_dt !== '0000-00-00' ?
                      new Date(license.lcnse_ppe_dt).toISOString().split('T')[0] : 'N/A'}
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={license.lcnse_status || ''}
                      fieldName="lcnse_status"
                      recordId={license.auto_nbr.toString()}
                      tableName="licensing"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={license.lcnse_regn_status || ''}
                      fieldName="lcnse_regn_status"
                      recordId={license.auto_nbr.toString()}
                      tableName="licensing"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={license.lcnse_notes || ''}
                      fieldName="lcnse_notes"
                      recordId={license.auto_nbr.toString()}
                      tableName="licensing"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
