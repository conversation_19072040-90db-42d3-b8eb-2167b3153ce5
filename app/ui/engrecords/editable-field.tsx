'use client';

import { useState } from 'react';
import { PencilIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useSession } from 'next-auth/react';

interface EditableFieldProps {
  value: string | number | null;
  displayValue?: string;
  fieldName: string;
  recordId: string;
  tableName: string;
  onSave: (value: string, fieldName: string, recordId: string, tableName: string) => Promise<any>;
}

export default function EditableField({
  value,
  displayValue,
  fieldName,
  recordId,
  tableName,
  onSave,
}: EditableFieldProps) {
  const { data: session } = useSession();
  const isSystemAdmin = session?.user?.roles?.includes('system_admin');

  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value?.toString() || '');
  const [isLoading, setIsLoading] = useState(false);
  // Format currency values for initial display
  const formatInitialValue = () => {
    if (displayValue) return displayValue;

    // Format currency fields
    if ((fieldName === 'admin_stipend' || fieldName === 'admin_research_supp') && value !== null && value !== '') {
      return new Intl.NumberFormat('en-CA', {
        style: 'currency',
        currency: 'CAD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(typeof value === 'string' ? parseFloat(value) : value as number);
    }

    return value;
  };

  const [internalDisplayValue, setInternalDisplayValue] = useState(formatInitialValue());

  if (!isSystemAdmin) {
    return <span>{internalDisplayValue}</span>;
  }

  const handleEdit = () => {
    // Always use the actual value for editing, not the display value
    setEditValue(value?.toString() || '');
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      const updatedData = await onSave(editValue, fieldName, recordId, tableName);
      // Update the displayed value with the new value
      const newValue = updatedData[fieldName];
      // If we have a custom display format, we need to update the internal display value
      if (displayValue) {
        // For currency fields, we need to format the new value
        if (fieldName === 'admin_stipend' || fieldName === 'admin_research_supp') {
          setInternalDisplayValue(new Intl.NumberFormat('en-CA', {
            style: 'currency',
            currency: 'CAD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          }).format(parseFloat(newValue)));
        } else {
          setInternalDisplayValue(newValue);
        }
      } else {
        setInternalDisplayValue(newValue);
      }
      // Also update the edit value so it's correct next time we edit
      setEditValue(newValue?.toString() || '');
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving change:', error);
      // Could add error handling UI here
    } finally {
      setIsLoading(false);
    }
  };

  if (isEditing) {
    return (
      <span className="inline-flex items-center gap-2">
        <input
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          className="px-2 py-1 border rounded-md text-sm"
          disabled={isLoading}
        />
        <button
          onClick={handleSave}
          disabled={isLoading}
          className="p-1 rounded-md hover:bg-green-100"
          aria-label="Save"
        >
          <CheckIcon className="w-4 h-4 text-green-600" />
        </button>
        <button
          onClick={handleCancel}
          disabled={isLoading}
          className="p-1 rounded-md hover:bg-red-100"
          aria-label="Cancel"
        >
          <XMarkIcon className="w-4 h-4 text-red-600" />
        </button>
      </span>
    );
  }

  return (
    <span className="group inline-flex items-center gap-1">
      <span>{internalDisplayValue}</span>
      <button
        onClick={handleEdit}
        className="p-1 rounded-md opacity-0 group-hover:opacity-100 hover:bg-gray-100 transition-opacity"
        aria-label="Edit"
      >
        <PencilIcon className="w-4 h-4 text-gray-500" />
      </button>
    </span>
  );
}
