'use client';

import EditableField from './editable-field';
import { saveEngRecordsEdit } from '@/app/lib/engrecords-actions';

interface Degree {
  auto_nbr: number;
  deg_type: string;
  deg_yr: string;
  deg_name: string;
  deg_institution: string;
  deg_province_name: string;
}

export default function EditableDegrees({ degrees }: { degrees: Degree[] }) {
  return (
    <div className="rounded-lg bg-gray-50 p-6">
      <h2 className="text-lg font-semibold mb-4">Degrees</h2>
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="text-left text-sm font-normal">
              <th className="px-4 py-2">Degree</th>
              <th className="px-4 py-2">Type</th>
              <th className="px-4 py-2">Institution</th>
              <th className="px-4 py-2">Year</th>
              <th className="px-4 py-2">Province</th>
            </tr>
          </thead>
          <tbody>
            {degrees.map((degree, index) => (
              <tr key={index} className="border-b">
                <td className="px-4 py-2">
                  <EditableField
                    value={degree.deg_name}
                    fieldName="deg_name"
                    recordId={degree.auto_nbr.toString()}
                    tableName="degrees"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  <EditableField
                    value={degree.deg_type}
                    fieldName="deg_type"
                    recordId={degree.auto_nbr.toString()}
                    tableName="degrees"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  <EditableField
                    value={degree.deg_institution}
                    fieldName="deg_institution"
                    recordId={degree.auto_nbr.toString()}
                    tableName="degrees"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  <EditableField
                    value={degree.deg_yr}
                    fieldName="deg_yr"
                    recordId={degree.auto_nbr.toString()}
                    tableName="degrees"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  <EditableField
                    value={degree.deg_province_name}
                    fieldName="deg_province_name"
                    recordId={degree.auto_nbr.toString()}
                    tableName="degrees"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
