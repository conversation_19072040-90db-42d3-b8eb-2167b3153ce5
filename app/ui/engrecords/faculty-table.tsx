import { lusitana } from '@/app/ui/fonts';
import { EyeIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { sql } from '@/app/lib/db';

interface Faculty {
  nexus: string;
  first_name: string;
  last_name: string;
  appt_title: string;
  descr: string;
  fac_org_unit: string;
  unit_full_name: string;
  fac_group: string;
  fac_rank_desc: string;
  work_email: string;
}

export default async function FacultyTable({
  query,
  currentPage,
  facGroup = '',
  orgUnit = '',
}: {
  query: string;
  currentPage: number;
  facGroup?: string;
  orgUnit?: string;
}) {
  const ITEMS_PER_PAGE = 10;
  const offset = (currentPage - 1) * ITEMS_PER_PAGE;
  let faculty: Faculty[] = [];

  try {
    // First, get the faculty data with the latest rank from appts_fac
    const facultyResult = await sql<{ nexus: string; first_name: string; last_name: string; appt_title: string; descr: string; fac_org_unit: string; fac_group: string; fac_rank_desc: string; work_email: string; }[]>`
      WITH latest_ranks AS (
        SELECT
          fac_nexus,
          fac_rank_desc,
          ROW_NUMBER() OVER (PARTITION BY fac_nexus ORDER BY fac_eff_from_dt DESC) as rn
        FROM engrecords.appts_fac
      )
      SELECT
        f.nexus,
        f.first_name,
        f.last_name,
        f.appt_title,
        f.descr,
        f.fac_org_unit,
        f.fac_group,
        lr.fac_rank_desc,
        uw.work_email
      FROM engrecords.eng_fac f
      LEFT JOIN latest_ranks lr ON f.nexus = lr.fac_nexus AND lr.rn = 1
      LEFT JOIN uw.faculty uw ON LOWER(f.nexus) = LOWER(uw.sso_id)
      WHERE f.active = 'Y'
        ${query ? sql`AND (
          f.first_name ILIKE ${`%${query}%`} OR
          f.last_name ILIKE ${`%${query}%`} OR
          f.nexus ILIKE ${`%${query}%`} OR
          CONCAT(f.first_name, ' ', f.last_name) ILIKE ${`%${query}%`}
        )` : sql``}
        ${facGroup ? sql`AND f.fac_group = ${facGroup}` : sql``}
        ${orgUnit ? sql`AND f.fac_org_unit = ${orgUnit}` : sql``}
      ORDER BY f.last_name, f.first_name
      LIMIT ${ITEMS_PER_PAGE}
      OFFSET ${offset}
    `;

    // Get the unit IDs from the faculty data
    const unitIds = facultyResult.map(f => f.fac_org_unit).filter(Boolean);

    // If there are unit IDs, fetch the unit names using sql
    let unitMap: Record<string, string> = {};
    if (unitIds.length > 0) {
      const unitResults = await sql`
        SELECT unit_id, full_name
        FROM uw.unit
        WHERE unit_id IN ${sql(unitIds)}
      `;

      // Create a map of unit_id to full_name
      unitMap = unitResults.reduce((acc: Record<string, string>, unit: any) => {
        acc[unit.unit_id] = unit.full_name;
        return acc;
      }, {});
    }

    // Combine the faculty data with the unit names
    faculty = facultyResult.map(f => ({
      ...f,
      unit_full_name: unitMap[f.fac_org_unit] || ''
    }));
  } catch (error) {
    console.error('Error fetching faculty:', error);
    // Return empty array if there's an error
    faculty = [];
  }

  if (faculty.length === 0) {
    return (
      <div className="mt-6 flow-root">
        <div className="inline-block min-w-full align-middle">
          <div className="rounded-lg bg-gray-50 p-2 md:pt-0">
            <div className="text-center py-4">
              <p className="text-gray-500">No faculty members found.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-6 flow-root">
      <div className="inline-block min-w-full align-middle">
        <div className="rounded-lg bg-gray-50 p-2 md:pt-0 overflow-x-auto">
          <div className="md:hidden">
            {faculty.map((fac) => (
              <div
                key={fac.nexus}
                className="mb-2 w-full rounded-md bg-white p-4"
              >
                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <div className="mb-2 flex items-center">
                      <p>{fac.first_name} {fac.last_name}</p>
                    </div>
                    {fac.work_email && (
                      <p className="text-sm text-gray-500 mb-2">{fac.work_email}</p>
                    )}
                    <p className="text-sm text-gray-600">{fac.appt_title}</p>
                    <p className="text-sm text-gray-500">
                      {['Part-time Faculty', 'NonFaculty', 'Faculty'].includes(fac.fac_group) ? fac.fac_group : ''}
                    </p>
                    <p className="text-sm text-gray-500">{fac.fac_rank_desc || ''}</p>
                    <p className="text-sm text-gray-500">{fac.unit_full_name || fac.fac_org_unit}</p>
                  </div>
                </div>
                <div className="flex w-full items-center justify-between pt-4">
                  <div className="flex justify-end gap-2">
                    <Link
                      href={`/dashboard/engrecords/faculty/${fac.nexus}`}
                      className="rounded-md border p-2 hover:bg-gray-100"
                    >
                      <EyeIcon className="w-5" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <table className="hidden min-w-full text-gray-900 md:table">
            <thead className="rounded-lg text-left text-sm font-normal">
              <tr>
                <th scope="col" className="px-4 py-5 font-medium sm:pl-6">
                  Name
                </th>
                <th scope="col" className="px-3 py-5 font-medium">
                  Title & Details
                </th>
                <th scope="col" className="px-3 py-5 font-medium">
                  Organization Unit
                </th>
                <th scope="col" className="relative py-3 pl-6 pr-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {faculty.map((fac) => (
                <tr
                  key={fac.nexus}
                  className="w-full border-b py-3 text-sm last-of-type:border-none [&:first-child>td:first-child]:rounded-tl-lg [&:first-child>td:last-child]:rounded-tr-lg [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg"
                >
                  <td className="whitespace-nowrap py-3 pl-6 pr-3">
                    <div className="flex items-center gap-3">
                      <div>
                        <p>{fac.first_name} {fac.last_name}</p>
                        {fac.work_email && (
                          <p className="text-sm text-gray-500">{fac.work_email}</p>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-3 py-3">
                    <div>
                      <div>{fac.appt_title}</div>
                      <div className="text-xs text-gray-500">
                        {['Part-time Faculty', 'NonFaculty', 'Faculty'].includes(fac.fac_group) ? fac.fac_group : ''}
                        {fac.fac_rank_desc && fac.fac_group ? ' • ' : ''}
                        {fac.fac_rank_desc || ''}
                      </div>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-3 py-3">
                    {fac.unit_full_name || fac.fac_org_unit}
                  </td>
                  <td className="whitespace-nowrap py-3 pl-6 pr-3">
                    <div className="flex justify-end gap-3">
                      <Link
                        href={`/dashboard/engrecords/faculty/${fac.nexus}`}
                        className="rounded-md border p-2 hover:bg-gray-100"
                      >
                        <EyeIcon className="w-5" />
                      </Link>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}