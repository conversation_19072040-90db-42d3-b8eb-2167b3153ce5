'use client';

import Editable<PERSON>ield from './editable-field';
import { saveEngRecordsEdit } from '@/app/lib/engrecords-actions';

interface Leave {
  auto_nbr: number;
  leave_type: string;
  leave_eff_from_dt: string;
  leave_eff_to_dt: string;
  leave_salary_perc: string;
  leave_board_approved: string;
  leave_notes: string;
}

function formatSalaryPercentage(value: string): string {
  if (!value) return '';
  
  // If value already contains %, return as is
  if (value.includes('%')) return value;
  
  // Try to parse as number
  const num = parseFloat(value);
  if (isNaN(num)) return value;
  
  // If number is less than 1, multiply by 100 and add %
  if (num < 1) {
    return `${Math.round(num * 100)}%`;
  }
  
  // Otherwise just add % to the original value
  return `${value}%`;
}

export default function EditableLeaves({ leaves }: { leaves: Leave[] }) {
  return (
    <div className="rounded-lg bg-gray-50 p-6">
      <h2 className="text-lg font-semibold mb-4">Leaves</h2>
      {leaves.length === 0 ? (
        <p className="text-gray-500">No leave information available.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="text-left text-sm font-normal">
                <th className="px-4 py-2">Type</th>
                <th className="px-4 py-2">Start Date</th>
                <th className="px-4 py-2">End Date</th>
                <th className="px-4 py-2">Salary %</th>
                <th className="px-4 py-2">Board Approved</th>
                <th className="px-4 py-2">Notes</th>
              </tr>
            </thead>
            <tbody>
              {leaves.map((leave, index) => (
                <tr key={index} className="border-b">
                  <td className="px-4 py-2">
                    <EditableField
                      value={leave.leave_type || ''}
                      fieldName="leave_type"
                      recordId={leave.auto_nbr.toString()}
                      tableName="leaves"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    {leave.leave_eff_from_dt && leave.leave_eff_from_dt !== '0000-00-00' ? 
                      new Date(leave.leave_eff_from_dt).toISOString().split('T')[0] : 'N/A'}
                  </td>
                  <td className="px-4 py-2">
                    {leave.leave_eff_to_dt && leave.leave_eff_to_dt !== '0000-00-00' ? 
                      new Date(leave.leave_eff_to_dt).toISOString().split('T')[0] : 'N/A'}
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={formatSalaryPercentage(leave.leave_salary_perc || '')}
                      fieldName="leave_salary_perc"
                      recordId={leave.auto_nbr.toString()}
                      tableName="leaves"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={leave.leave_board_approved || ''}
                      fieldName="leave_board_approved"
                      recordId={leave.auto_nbr.toString()}
                      tableName="leaves"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                  <td className="px-4 py-2">
                    <EditableField
                      value={leave.leave_notes || ''}
                      fieldName="leave_notes"
                      recordId={leave.auto_nbr.toString()}
                      tableName="leaves"
                      onSave={saveEngRecordsEdit}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
