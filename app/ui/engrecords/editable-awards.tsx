'use client';

import EditableField from './editable-field';
import { saveEngRecordsEdit } from '@/app/lib/engrecords-actions';

// Helper function to format dates consistently between server and client
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
}

interface Award {
  auto_nbr: number;
  award_category: string;
  award_name: string;
  award_nom_dt: string;
  award_status: string;
  award_yr: string;
}

export default function EditableAwards({ awards }: { awards: Award[] }) {
  return (
    <div className="rounded-lg bg-gray-50 p-6">
      <h2 className="text-lg font-semibold mb-4">Awards</h2>
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="text-left text-sm font-normal">
              <th className="px-4 py-2">Category</th>
              <th className="px-4 py-2">Name</th>
              <th className="px-4 py-2">Year</th>
              <th className="px-4 py-2">Status</th>
              <th className="px-4 py-2">Nomination Date</th>
            </tr>
          </thead>
          <tbody>
            {awards.map((award, index) => (
              <tr key={index} className="border-b">
                <td className="px-4 py-2">
                  <EditableField
                    value={award.award_category}
                    fieldName="award_category"
                    recordId={award.auto_nbr.toString()}
                    tableName="awards"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  <EditableField
                    value={award.award_name}
                    fieldName="award_name"
                    recordId={award.auto_nbr.toString()}
                    tableName="awards"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  <EditableField
                    value={award.award_yr}
                    fieldName="award_yr"
                    recordId={award.auto_nbr.toString()}
                    tableName="awards"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  <EditableField
                    value={award.award_status}
                    fieldName="award_status"
                    recordId={award.auto_nbr.toString()}
                    tableName="awards"
                    onSave={saveEngRecordsEdit}
                  />
                </td>
                <td className="px-4 py-2">
                  {formatDate(award.award_nom_dt)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
