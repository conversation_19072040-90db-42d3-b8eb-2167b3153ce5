'use client';

import { useState } from 'react';
import EditableField from './editable-field';
import { saveEngRecordsEdit } from '@/app/lib/engrecords-actions';

// Helper function to format dates consistently between server and client
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
}



interface FacultyAppointment {
  auto_nbr: number;
  fac_category: string;
  fac_stage: string;
  fac_type: string;
  fac_rank_desc: string;
  fac_eff_from_dt: string;
  fac_eff_to_dt: string | null;
  fac_teaching_perc: number;
  fac_research_perc: number;
  fac_service_perc: number;
  fac_merit_cycle: string;
  fac_notes: string | null;
}

interface AdminAppointment {
  auto_nbr: number;
  admin_title_prefix: string | null;
  admin_title: string;
  admin_portfolio: string | null;
  admin_program: string | null;
  admin_eff_from_dt: string;
  admin_eff_to_dt: string | null;
  admin_stipend_duration: string;
  admin_stipend: number;
  admin_teaching_release: number;
  admin_research_supp: number;
  admin_teaching_perc: number;
  admin_research_perc: number;
  admin_service_perc: number;
  admin_notes: string | null;
}

interface CrossAppointment {
  auto_nbr: number;
  cross_eff_from_dt: string;
  cross_eff_to_dt: string | null;
  cross_other_org_unit: string;
  unit_full_name?: string | null;
  cross_notes: string | null;
}

interface JointAppointment {
  auto_nbr: number;
  joint_eff_from_dt: string;
  joint_eff_to_dt: string | null;
  joint_home_org_unit: string;
  joint_home_dept_fte: number;
  joint_org_unit_1: string;
  joint_dept_fte_1: number;
  joint_org_unit_2: string | null;
  joint_dept_fte_2: number;
  joint_org_unit_3: string | null;
  joint_dept_fte_3: number;
  joint_notes: string | null;
}

interface OverloadAppointment {
  auto_nbr: number;
  over_eff_from_dt: string;
  over_eff_to_dt: string | null;
  over_notes: string | null;
}

interface AppointmentsTabsProps {
  facultyAppointments: FacultyAppointment[];
  adminAppointments: AdminAppointment[];
  crossAppointments: CrossAppointment[];
  jointAppointments: JointAppointment[];
  overloadAppointments: OverloadAppointment[];
}

export default function AppointmentsTabs({
  facultyAppointments,
  adminAppointments,
  crossAppointments,
  jointAppointments,
  overloadAppointments,
}: AppointmentsTabsProps) {
  const [activeTab, setActiveTab] = useState('faculty');

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <div className="rounded-lg bg-gray-50 p-6">
      <h2 className="text-lg font-semibold mb-4">Appointments</h2>
      <div className="mb-4">
        <div className="sm:hidden">
          <label htmlFor="tabs" className="sr-only">
            Select a tab
          </label>
          <select
            id="tabs"
            name="tabs"
            className="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
            value={activeTab}
            onChange={(e) => handleTabChange(e.target.value)}
          >
            {facultyAppointments.length > 0 && (
              <option value="faculty">Faculty</option>
            )}
            {adminAppointments.length > 0 && (
              <option value="admin">Administrative</option>
            )}
            {crossAppointments.length > 0 && (
              <option value="cross">Cross</option>
            )}
            {jointAppointments.length > 0 && (
              <option value="joint">Joint</option>
            )}
            {overloadAppointments.length > 0 && (
              <option value="overload">Overload</option>
            )}
          </select>
        </div>
        <div className="hidden sm:block">
          <nav className="flex space-x-4" aria-label="Tabs">
            {facultyAppointments.length > 0 && (
              <button
                onClick={() => handleTabChange('faculty')}
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  activeTab === 'faculty'
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Faculty
              </button>
            )}
            {adminAppointments.length > 0 && (
              <button
                onClick={() => handleTabChange('admin')}
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  activeTab === 'admin'
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Administrative
              </button>
            )}
            {crossAppointments.length > 0 && (
              <button
                onClick={() => handleTabChange('cross')}
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  activeTab === 'cross'
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Cross
              </button>
            )}
            {jointAppointments.length > 0 && (
              <button
                onClick={() => handleTabChange('joint')}
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  activeTab === 'joint'
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Joint
              </button>
            )}
            {overloadAppointments.length > 0 && (
              <button
                onClick={() => handleTabChange('overload')}
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  activeTab === 'overload'
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Overload
              </button>
            )}
          </nav>
        </div>
      </div>

      <div className="overflow-x-auto">
        {facultyAppointments.length > 0 && (
          <div className={`tab-content ${activeTab !== 'faculty' ? 'hidden' : ''}`}>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="text-left text-sm font-normal">
                    <th className="px-4 py-2">Category</th>
                    <th className="px-4 py-2">Stage</th>
                    <th className="px-4 py-2">Type</th>
                    <th className="px-4 py-2">Rank</th>
                    <th className="px-4 py-2">Period</th>
                    <th className="px-4 py-2">Teaching</th>
                    <th className="px-4 py-2">Research</th>
                    <th className="px-4 py-2">Service</th>
                    <th className="px-4 py-2">Merit Cycle</th>
                    <th className="px-4 py-2">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {facultyAppointments.map((appt, index) => (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_category}
                          fieldName="fac_category"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_stage}
                          fieldName="fac_stage"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_type}
                          fieldName="fac_type"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_rank_desc}
                          fieldName="fac_rank_desc"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        {formatDate(appt.fac_eff_from_dt)} to{' '}
                        {appt.fac_eff_to_dt ? formatDate(appt.fac_eff_to_dt) : 'Present'}
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_teaching_perc}
                          fieldName="fac_teaching_perc"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_research_perc}
                          fieldName="fac_research_perc"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_service_perc}
                          fieldName="fac_service_perc"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_merit_cycle}
                          fieldName="fac_merit_cycle"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.fac_notes}
                          fieldName="fac_notes"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_fac"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {adminAppointments.length > 0 && (
          <div className={`tab-content ${activeTab !== 'admin' ? 'hidden' : ''}`}>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="text-left text-sm font-normal">
                    <th className="px-4 py-2">Title</th>
                    <th className="px-4 py-2">Portfolio</th>
                    <th className="px-4 py-2">Program</th>
                    <th className="px-4 py-2">Period</th>
                    <th className="px-4 py-2">Stipend</th>
                    <th className="px-4 py-2">Teaching Release</th>
                    <th className="px-4 py-2">Research Support</th>
                    <th className="px-4 py-2">Teaching</th>
                    <th className="px-4 py-2">Research</th>
                    <th className="px-4 py-2">Service</th>
                    <th className="px-4 py-2">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {adminAppointments.map((appt, index) => (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_title}
                          fieldName="admin_title"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_portfolio}
                          fieldName="admin_portfolio"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_program}
                          fieldName="admin_program"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        {formatDate(appt.admin_eff_from_dt)} to{' '}
                        {appt.admin_eff_to_dt ? formatDate(appt.admin_eff_to_dt) : 'Present'}
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_stipend}
                          fieldName="admin_stipend"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_teaching_release}
                          fieldName="admin_teaching_release"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_research_supp}
                          fieldName="admin_research_supp"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_teaching_perc}
                          fieldName="admin_teaching_perc"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_research_perc}
                          fieldName="admin_research_perc"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_service_perc}
                          fieldName="admin_service_perc"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.admin_notes}
                          fieldName="admin_notes"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_admin"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {crossAppointments.length > 0 && (
          <div className={`tab-content ${activeTab !== 'cross' ? 'hidden' : ''}`}>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="text-left text-sm font-normal">
                    <th className="px-4 py-2">Period</th>
                    <th className="px-4 py-2">Other Organization Unit</th>
                    <th className="px-4 py-2">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {crossAppointments.map((appt, index) => (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-2">
                        {formatDate(appt.cross_eff_from_dt)} to{' '}
                        {appt.cross_eff_to_dt ? formatDate(appt.cross_eff_to_dt) : 'Present'}
                      </td>
                      <td className="px-4 py-2">
                        {appt.unit_full_name ? (
                          <div>
                            <div>{appt.unit_full_name}</div>
                            <div className="text-xs text-gray-500">(ID: {appt.cross_other_org_unit})</div>
                          </div>
                        ) : (
                          <EditableField
                            value={appt.cross_other_org_unit}
                            fieldName="cross_other_org_unit"
                            recordId={appt.auto_nbr.toString()}
                            tableName="appts_cross"
                            onSave={saveEngRecordsEdit}
                          />
                        )}
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.cross_notes}
                          fieldName="cross_notes"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_cross"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {jointAppointments.length > 0 && (
          <div className={`tab-content ${activeTab !== 'joint' ? 'hidden' : ''}`}>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="text-left text-sm font-normal">
                    <th className="px-4 py-2">Period</th>
                    <th className="px-4 py-2">Home Unit</th>
                    <th className="px-4 py-2">Home FTE</th>
                    <th className="px-4 py-2">Unit 1</th>
                    <th className="px-4 py-2">FTE 1</th>
                    <th className="px-4 py-2">Unit 2</th>
                    <th className="px-4 py-2">FTE 2</th>
                    <th className="px-4 py-2">Unit 3</th>
                    <th className="px-4 py-2">FTE 3</th>
                    <th className="px-4 py-2">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {jointAppointments.map((appt, index) => (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-2">
                        {formatDate(appt.joint_eff_from_dt)} to{' '}
                        {appt.joint_eff_to_dt ? formatDate(appt.joint_eff_to_dt) : 'Present'}
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_home_org_unit}
                          fieldName="joint_home_org_unit"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_home_dept_fte}
                          fieldName="joint_home_dept_fte"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_org_unit_1}
                          fieldName="joint_org_unit_1"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_dept_fte_1}
                          fieldName="joint_dept_fte_1"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_org_unit_2}
                          fieldName="joint_org_unit_2"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_dept_fte_2}
                          fieldName="joint_dept_fte_2"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_org_unit_3}
                          fieldName="joint_org_unit_3"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_dept_fte_3}
                          fieldName="joint_dept_fte_3"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.joint_notes}
                          fieldName="joint_notes"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_joint"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {overloadAppointments.length > 0 && (
          <div className={`tab-content ${activeTab !== 'overload' ? 'hidden' : ''}`}>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="text-left text-sm font-normal">
                    <th className="px-4 py-2">Period</th>
                    <th className="px-4 py-2">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {overloadAppointments.map((appt, index) => (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-2">
                        {formatDate(appt.over_eff_from_dt)} to{' '}
                        {appt.over_eff_to_dt ? formatDate(appt.over_eff_to_dt) : 'Present'}
                      </td>
                      <td className="px-4 py-2">
                        <EditableField
                          value={appt.over_notes}
                          fieldName="over_notes"
                          recordId={appt.auto_nbr.toString()}
                          tableName="appts_over"
                          onSave={saveEngRecordsEdit}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}