'use client';

import { useState } from 'react';
import { ClockIcon } from '@heroicons/react/24/outline';
import ChangeHistoryModal from './change-history-modal';
import { useSession } from 'next-auth/react';

interface ChangelogEntry {
  changelog_id: number;
  timestamp: string;
  user_email: string;
  user_name: string;
  table_name: string;
  record_id: string;
  sql_query: string;
}

export default function ChangeHistoryButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [changelogEntries, setChangelogEntries] = useState<ChangelogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { data: session } = useSession();

  // Only system_admin users can see change history
  const isSystemAdmin = session?.user?.roles?.includes('system_admin');

  if (!isSystemAdmin) {
    return null;
  }

  const handleOpenModal = async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      // Get the current page URL
      const pageUrl = window.location.pathname;

      // Fetch change history for this page
      const response = await fetch(`/api/engrecords/changelog?pageUrl=${encodeURIComponent(pageUrl)}`);

      if (!response.ok) {
        throw new Error('Failed to fetch change history');
      }

      const data = await response.json();
      setChangelogEntries(data.changelogEntries || []);
      setIsModalOpen(true);
    } catch (error) {
      console.error('Error fetching change history:', error);
      // Could add error handling UI here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <button
        onClick={handleOpenModal}
        className="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
        disabled={isLoading}
      >
        <ClockIcon className="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400" aria-hidden="true" />
        {isLoading ? 'Loading...' : 'Change History'}
      </button>

      <ChangeHistoryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        changelogEntries={changelogEntries}
      />
    </>
  );
}
