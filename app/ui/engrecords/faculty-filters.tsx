'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

interface FilterOption {
  value: string;
  label: string;
}

interface FacultyFiltersProps {
  defaultFacGroup?: string;
}

export default function FacultyFilters({ defaultFacGroup = 'Faculty' }: FacultyFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [facGroups, setFacGroups] = useState<FilterOption[]>([]);
  const [orgUnits, setOrgUnits] = useState<FilterOption[]>([]);
  const [loading, setLoading] = useState(true);

  // Get selected filters from URL
  const query = searchParams.get('query') || '';
  const selectedFacGroup = searchParams.get('facGroup') || '';
  const selectedOrgUnit = searchParams.get('orgUnit') || '';

  // Track if user has explicitly selected "all" for facGroup
  const [userSelectedAll, setUserSelectedAll] = useState(false);

  // Set default filter if no facGroup is selected and user hasn't explicitly chosen "all"
  useEffect(() => {
    if (!selectedFacGroup && !userSelectedAll && defaultFacGroup && facGroups.length > 0) {
      const hasDefaultGroup = facGroups.some(group => group.value === defaultFacGroup);
      if (hasDefaultGroup) {
        updateFilter('facGroup', defaultFacGroup);
      }
    }
  }, [facGroups, defaultFacGroup, selectedFacGroup, userSelectedAll]);

  // Fetch filter options
  useEffect(() => {
    const fetchFilters = async () => {
      try {
        const response = await fetch('/api/engrecords/faculty/filters');
        if (!response.ok) throw new Error('Failed to fetch filters');

        const data = await response.json();
        // Modify the display label for "Faculty" to "Regular Faculty"
        const modifiedFacGroups = (data.facGroups || []).map((group: FilterOption) => {
          if (group.value === 'Faculty') {
            return { ...group, label: 'Regular Faculty' };
          }
          return group;
        });
        setFacGroups(modifiedFacGroups);
        setOrgUnits(data.orgUnits || []);
      } catch (error) {
        console.error('Error fetching filters:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFilters();
  }, []);

  // Update URL with filter
  const updateFilter = (filterType: 'facGroup' | 'orgUnit', value: string) => {
    const params = new URLSearchParams(searchParams);

    // Reset to page 1 when filters change
    params.set('page', '1');

    // Update the specific filter
    if (value && value !== 'all') {
      params.set(filterType, value);
    } else {
      params.delete(filterType);
    }

    // Preserve query parameter if it exists
    if (query) {
      params.set('query', query);
    }

    router.push(`${pathname}?${params.toString()}`);
  };

  // Handle selection change
  const handleFacGroupChange = (value: string) => {
    if (value === 'all') {
      setUserSelectedAll(true);
    }
    updateFilter('facGroup', value);
  };

  const handleOrgUnitChange = (value: string) => {
    updateFilter('orgUnit', value);
  };

  if (loading) {
    return <div className="flex gap-2 animate-pulse">
      <div className="h-10 w-40 bg-gray-200 rounded"></div>
      <div className="h-10 w-40 bg-gray-200 rounded"></div>
    </div>;
  }

  return (
    <div className="flex gap-2">
      <Select value={selectedFacGroup || 'all'} onValueChange={handleFacGroupChange}>
        <SelectTrigger id="fac-group-filter" className="w-40">
          <SelectValue placeholder="Faculty Group" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Groups</SelectItem>
          {facGroups.map((group) => (
            <SelectItem key={group.value} value={group.value}>
              {group.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={selectedOrgUnit || 'all'} onValueChange={handleOrgUnitChange}>
        <SelectTrigger
          id="org-unit-filter"
          className="w-[32rem] [&>span]:!line-clamp-none [&>span]:whitespace-nowrap [&>span]:overflow-hidden [&>span]:text-ellipsis"
          title={selectedOrgUnit ? orgUnits.find(u => u.value === selectedOrgUnit)?.label || '' : ''}
        >
          <SelectValue placeholder="Organization Unit" />
        </SelectTrigger>
        <SelectContent className="min-w-[32rem]">
          <SelectItem value="all">All Units</SelectItem>
          {orgUnits.map((unit) => (
            <SelectItem
              key={unit.value}
              value={unit.value}
              title={unit.label}
              className="whitespace-nowrap"
            >
              {unit.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
