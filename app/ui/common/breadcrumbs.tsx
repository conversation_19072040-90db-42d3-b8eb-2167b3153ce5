
import { clsx } from 'clsx';
import Link from 'next/link';
import { lusitana } from '@/app/ui/fonts';

interface Breadcrumb {
  label: string;
  href: string;
  active?: boolean;
}

export default function Breadcrumbs({
  breadcrumbs,
}: {
  breadcrumbs: Breadcrumb[];
}) {
  // Ensure breadcrumbs is always an array
  const validBreadcrumbs = Array.isArray(breadcrumbs) ? breadcrumbs : [];

  // If no valid breadcrumbs, don't render anything
  if (validBreadcrumbs.length === 0) {
    return null;
  }

  return (
    <nav aria-label="Breadcrumb" className="mb-6 block">
      <ol className={clsx(lusitana.className, 'flex text-xl md:text-2xl')}>
        {validBreadcrumbs.map((breadcrumb, index) => {
          // Ensure each breadcrumb has the required properties
          if (!breadcrumb || !breadcrumb.href || !breadcrumb.label) {
            return null;
          }

          // Create a unique key using both href and index to avoid duplicate keys
          const uniqueKey = `${breadcrumb.href}-${index}`;

          return (
            <li
              key={uniqueKey}
              aria-current={breadcrumb.active}
              className={clsx(
                breadcrumb.active ? 'text-gray-900' : 'text-gray-500',
              )}
            >
              <Link href={breadcrumb.href}>{breadcrumb.label}</Link>
              {index < validBreadcrumbs.length - 1 ? (
                <span className="mx-3 inline-block">/</span>
              ) : null}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}
