// app/ui/activities/Message.tsx
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface MessageProps {
  message: string;
  status: string;
  page: string;
}

export default function Message({ message, status, page }: MessageProps) {
  const [isVisible, setIsVisible] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (!message) return;

    const timer = setTimeout(() => {
      setIsVisible(false);
      // Update the URL to remove the message query params without reloading
      router.replace(`/dashboard/${page}/create`);
    }, 3000); // 5 seconds

    return () => clearTimeout(timer); // Cleanup on unmount
  }, [message, router]);

  if (!isVisible || !message) return null;

  return (
    <div
      className={`p-4 rounded-md transition-opacity duration-500 ${
        status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      } ${isVisible ? 'opacity-100' : 'opacity-0'}`}
    >
      {message}
    </div>
  );
}
