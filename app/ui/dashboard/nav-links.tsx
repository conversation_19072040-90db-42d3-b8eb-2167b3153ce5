"use client"

import {
  UserI<PERSON>,
  AcademicCapIcon,
  DocumentTextIcon,
  BookOpenIcon,
  BriefcaseIcon,
  ClipboardDocumentListIcon,
  PresentationChartLineIcon,
  UserGroupIcon,
  DocumentDuplicateIcon,
  CalendarIcon,
  ArrowUpTrayIcon,
  HomeIcon,
  BuildingOffice2Icon,
  ClockIcon,
  StarIcon,
} from '@heroicons/react/24/outline';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import clsx from 'clsx'
import { useSession } from 'next-auth/react';

import { Upload, Building2, EditIcon } from "lucide-react";

import { useState, useEffect } from "react";

import { cn } from "@/lib/utils";
import {
  Users,
  FileSpreadsheet,
  Settings,
  Database,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  GraduationCap,
  LayoutDashboard,
} from "lucide-react";

interface SubLink {
  name: string;
  href: string;
}

interface NavLink {
  name: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  subLinks?: SubLink[] | ((session: any) => Promise<SubLink[]>);
  roles?: string[];
  subItems?: { name: string; href: string }[];
  customCheck?: (session: any) => Promise<boolean>;
}

// Map of links to display in the side navigation.
// Depending on the size of the application, this would be stored in a database.
const links: NavLink[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
  },
  {
    name: "Faculty Insights",
    href: "/dashboard/sys_admin/faculty-insights",
    icon: BarChart3,
    roles: ['system_admin', 'faculty_admin'],
  },
  {
    name: 'EngRecords',
    href: '/dashboard/engrecords/faculty',
    icon: BuildingOffice2Icon,
    roles: ['system_admin', 'faculty_admin'],
  },
  {
    name: 'Merit Review',
    href: '/dashboard/merit-review',
    icon: StarIcon,
    subLinks: async (session) => {
      const baseLinks = [
        { name: 'Overview', href: '/dashboard/merit-review' },
        { name: 'Course Evaluations', href: '/dashboard/merit-review/course-evaluations' },
      ];

      // Add 'Submissions' link for admin roles
      if (session?.user?.roles?.includes('system_admin') ||
          session?.user?.roles?.includes('faculty_admin') ||
          session?.user?.roles?.includes('department_admin')) {
        baseLinks.push({ name: 'Submissions', href: '/dashboard/merit-review/submissions' });
      }

      // Add 'Committee' link for department_admin roles
      if (session?.user?.roles?.includes('department_admin') ||
          session?.user?.roles?.includes('system_admin')) {
        baseLinks.push({ name: 'Committee', href: '/dashboard/merit-review?tab=committee' });
      }

      // Add 'Committee Ratings' link for committee members
      try {
        const committeeResponse = await fetch('/api/merit-review/committee/check-membership');
        if (committeeResponse.ok) {
          const data = await committeeResponse.json();
          if (data.isMember) {
            console.log('User is a committee member for unit:', data.unitId);
            // Now that we've fixed the middleware, we can use the original page
            baseLinks.push({
              name: 'Committee Ratings',
              href: '/dashboard/merit-review/committee/ratings'
            });
          } else {
            console.log('User is not a committee member');
          }
        } else {
          console.log('Error checking committee membership, response status:', committeeResponse.status);
        }
      } catch (error) {
        console.error('Error checking committee membership:', error);
      }

      // Add 'Preliminary Ratings' link for department_approver and department_admin
      if (session?.user?.roles?.includes('department_approver') ||
          session?.user?.roles?.includes('department_admin') ||
          session?.user?.roles?.includes('faculty_approver')) {
        baseLinks.push({ name: 'Preliminary Ratings', href: '/dashboard/merit-review/preliminary-ratings' });
      }

      // Add 'My Submission' link only for Regular Faculty
      if (session?.user?.email) {
        try {
          // Check if user is Regular Faculty
          const response = await fetch('/api/profile/faculty');
          if (response.ok) {
            const data = await response.json();
            if (data?.job_family === 'Regular Faculty') {
              baseLinks.push({ name: 'My Submission', href: '/dashboard/merit-review/submission/v2' });
            }
          }
        } catch (error) {
          console.error('Error checking faculty status:', error);
        }
      }

      return baseLinks;
    },
    // Show for system_admin, faculty_admin, department_admin, or regular users with job_family = 'Regular Faculty'
    customCheck: async (session) => {
      if (session?.user?.roles?.includes('system_admin') ||
          session?.user?.roles?.includes('faculty_admin') ||
          session?.user?.roles?.includes('department_admin')) return true;

      // For regular users, check if they have job_family = 'Regular Faculty'
      if (session?.user?.email) {
        try {
          const response = await fetch('/api/profile/faculty');
          if (response.ok) {
            const data = await response.json();
            return data?.job_family === 'Regular Faculty';
          }
        } catch (error) {
          console.error('Error checking faculty status:', error);
        }
      }
      return false;
    }
  },
  {
    name: 'Activity',
    href: '/dashboard/activity',
    icon: ClipboardDocumentListIcon,
    subLinks: [
      { name: 'Teaching Load', href: '/dashboard/activity/teaching-load' },
      { name: 'Supervision', href: '/dashboard/activity/supervision' },
      { name: 'Committee Service', href: '/dashboard/activity/committee-service' },
      { name: 'Professional Service', href: '/dashboard/activity/professional-service' },
      { name: 'Community Service', href: '/dashboard/activity/community-service' },
      { name: 'Honors & Awards', href: '/dashboard/activity/honors-awards' },
      { name: 'Publications', href: '/dashboard/activity/publications' },
    ]
  },
  {
    name: 'Grant',
    href: '/dashboard/grant',
    icon: PresentationChartLineIcon,
    subLinks: [
      { name: 'Proposals', href: '/dashboard/grant/proposals' },
      { name: 'Awards', href: '/dashboard/grant/awards' },
      { name: 'Collaborators', href: '/dashboard/grant/collaborators' },
    ]
  },
  {
    name: 'Scholarship',
    href: '/dashboard/scholarship',
    icon: BookOpenIcon,
    subLinks: [
      { name: 'Journal Articles', href: '/dashboard/scholarship/journal-articles' },
      { name: 'Books', href: '/dashboard/scholarship/books' },
      { name: 'Book Chapters', href: '/dashboard/scholarship/book-chapters' },
      { name: 'Conference Proceedings', href: '/dashboard/scholarship/conference-proceedings' },
      { name: 'Presentations', href: '/dashboard/scholarship/presentations' },
      { name: 'Patents', href: '/dashboard/scholarship/patents' },
      { name: 'Datasets', href: '/dashboard/scholarship/datasets' },
    ]
  },
  {
    name: 'Sabbatical',
    href: '/dashboard/sabbatical',
    icon: EditIcon,

    subLinks: async (session) => {
      const baseLinks = [
        { name: 'Show Pending Request', href: '/dashboard/sabbatical/view-pending-list' },
        { name: 'Show Approved Request', href: '/dashboard/sabbatical/view-approved-list' },
      ];
      
      // Add 'My Submission' link only for Regular Faculty
      if (session?.user?.email) {
        try {
          // Check if user is Regular Faculty
          const response = await fetch('/api/profile/faculty');
          if (response.ok) {
            const data = await response.json();
            if (data?.job_family === 'Regular Faculty') {
              baseLinks.push({ name: 'New Request', href: '/dashboard/sabbatical/sabbatical-step1' });
            }
          }
        } catch (error) {
          console.error('Error checking faculty status:', error);
        }
      }

      return baseLinks;
    },
    
  },
  {
    name: 'Upload',
    href: '/dashboard/upload',
    icon: Upload,
  },
];

// Admin-specific links
const adminLinks: NavLink[] = [
  {
    name: "Manage User",
    href: "/dashboard/sys_admin/users",
    icon: Users,
    roles: ['system_admin', 'faculty_admin'],
    subLinks: [
      { name: 'List Users', href: '/dashboard/sys_admin/users' },
      { name: 'Create User', href: '/dashboard/sys_admin/users/create' },
    ]
  },
  {
    name: "View Data",
    href: "/dashboard/sys_admin",
    icon: BarChart3,
    subItems: [
      {
        name: "Faculty",
        href: "/dashboard/sys_admin/faculty",
      },
      {
        name: "Units",
        href: "/dashboard/sys_admin/units",
      },
      {
        name: "Positions",
        href: "/dashboard/sys_admin/positions",
      },
      {
        name: "Committees",
        href: "/dashboard/sys_admin/committees",
      },
    ],
  },
  {
    name: "Position Control",
    href: "/dashboard/position-control",
    icon: ClipboardDocumentListIcon,
    roles: ['system_admin', 'faculty_admin', 'department_admin', 'department_support', 'faculty_support'],
    subItems: [
      {
        name: "Faculty Search",
        href: "/dashboard/position-control/faculty-search",
      },
      {
        name: "Position Management",
        href: "/dashboard/position-control",
      },
    ],
  },
  {
    name: "Import Data",
    href: "/dashboard/sys_admin/import",
    icon: FileSpreadsheet,
  },
  {
    name: "Change Log",
    href: "/dashboard/sys_admin/changelog",
    icon: ClockIcon,
    roles: ['system_admin', 'faculty_admin'],
  },
  {
    name: "Settings",
    href: "/dashboard/sys_admin/settings",
    icon: Settings,
  },
];

interface NavProps extends React.HTMLAttributes<HTMLDivElement> {
  isCollapsed: boolean;
  closeSidebar?: () => void;
}

export function Nav({ className, isCollapsed, closeSidebar, ...props }: NavProps) {
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const isAdmin = session?.user?.roles?.includes('system_admin') || session?.user?.roles?.includes('institution_admin') || session?.user?.roles?.includes('faculty_admin');
  const [visibleLinks, setVisibleLinks] = useState<NavLink[]>([]);
  const [loading, setLoading] = useState(true);

  // Helper function to handle sidebar closing only on mobile
  const handleLinkClick = () => {
    if (closeSidebar && window.innerWidth < 768) {
      closeSidebar();
    }
  };

  // Initialize expanded items based on current path
  const [expandedItems, setExpandedItems] = useState<Set<string>>(() => {
    const expanded = new Set<string>();

    // Auto-expand sections if on a sub-page
    if (pathname?.startsWith('/dashboard/activity/')) {
      expanded.add('Activity');
    } else if (pathname?.startsWith('/dashboard/grant/')) {
      expanded.add('Grant');
    } else if (pathname?.startsWith('/dashboard/scholarship/')) {
      expanded.add('Scholarship');
    } else if (pathname?.startsWith('/dashboard/merit-review/')) {
      expanded.add('Merit Review');
    } else if (pathname?.startsWith('/dashboard/position-control/')) {
      expanded.add('Position Control');
    } else if (pathname?.startsWith('/dashboard/sys_admin/')) {
      // Expand admin sections
      if (pathname.includes('/users')) {
        expanded.add('Manage User');
      } else if (pathname.includes('/faculty') || pathname.includes('/units') ||
                pathname.includes('/positions') || pathname.includes('/committees')) {
        expanded.add('View Data');
      }
    }

    return expanded;
  });

  // Debug information (commented out to prevent console spam)
  // console.log('Session status:', status);
  // console.log('Session data:', session);
  // console.log('Current pathname:', pathname);

  // Update expanded items when pathname changes
  useEffect(() => {
    // Auto-expand sections if on a sub-page
    if (pathname?.startsWith('/dashboard/activity/')) {
      setExpandedItems(prev => {
        const next = new Set(prev);
        next.add('Activity');
        return next;
      });
    } else if (pathname?.startsWith('/dashboard/grant/')) {
      setExpandedItems(prev => {
        const next = new Set(prev);
        next.add('Grant');
        return next;
      });
    } else if (pathname?.startsWith('/dashboard/scholarship/')) {
      setExpandedItems(prev => {
        const next = new Set(prev);
        next.add('Scholarship');
        return next;
      });
    } else if (pathname?.startsWith('/dashboard/merit-review/')) {
      setExpandedItems(prev => {
        const next = new Set(prev);
        next.add('Merit Review');
        return next;
      });
    } else if (pathname?.startsWith('/dashboard/position-control/')) {
      setExpandedItems(prev => {
        const next = new Set(prev);
        next.add('Position Control');
        return next;
      });
    }
  }, [pathname]);

  // No unused state

  // Filter links based on customCheck and roles
  useEffect(() => {
    const filterLinks = async () => {
      if (status !== 'authenticated') {
        setVisibleLinks([]);
        setLoading(false);
        return;
      }

      const allLinks = isAdmin ? [...links, ...adminLinks] : links;
      const filtered = [];

      for (const link of allLinks) {
        // Skip if roles don't match
        if (link.roles && !link.roles.some(role => session?.user?.roles?.includes(role))) {
          continue;
        }

        // Check customCheck if it exists
        if (link.customCheck) {
          try {
            const isVisible = await link.customCheck(session);
            if (isVisible) {
              // Process dynamic subLinks if they exist
              if (typeof link.subLinks === 'function') {
                const processedLink = { ...link };
                try {
                  processedLink.subLinks = await link.subLinks(session);
                } catch (error) {
                  console.error(`Error processing dynamic subLinks for ${link.name}:`, error);
                  processedLink.subLinks = [];
                }
                filtered.push(processedLink);
              } else {
                filtered.push(link);
              }
            }
          } catch (error) {
            console.error(`Error in customCheck for ${link.name}:`, error);
          }
        } else {
          // No customCheck, include by default
          // Process dynamic subLinks if they exist
          if (typeof link.subLinks === 'function') {
            const processedLink = { ...link };
            try {
              processedLink.subLinks = await link.subLinks(session);
            } catch (error) {
              console.error(`Error processing dynamic subLinks for ${link.name}:`, error);
              processedLink.subLinks = [];
            }
            filtered.push(processedLink);
          } else {
            filtered.push(link);
          }
        }
      }

      setVisibleLinks(filtered);
      setLoading(false);

    };

    filterLinks();
  }, [status, session, isAdmin]);

  const toggleItem = (name: string) => {
    setExpandedItems(prev => {
      const next = new Set(prev);
      if (next.has(name)) {
        next.delete(name);
      } else {
        next.add(name);
      }
      return next;
    });
  };

  return (
    <div className={cn("group px-3 py-2", className)} {...props}>
      <div className="flex flex-col gap-4 p-2 pb-20">
        {loading ? (
          <div className="flex items-center justify-center py-4">
            <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-gray-900"></div>
            <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
          </div>
        ) : (
          visibleLinks.map((link) => {
          const LinkIcon = link.icon;
          const isActive = pathname.startsWith(link.href);
          const hasSubItems = link.subItems && link.subItems.length > 0;
          const hasSubLinks = link.subLinks && link.subLinks.length > 0;
          const isExpanded = expandedItems.has(link.name);

          // Debug information for each link (commented out to prevent console spam)
          // console.log(`Link: ${link.name}, href: ${link.href}, isActive: ${isActive}`);

          // Skip rendering admin-only links for non-admin users
          if (link.roles && !isAdmin) {
            return null;
          }

          return (
            <div key={link.name}>
              {hasSubItems || hasSubLinks ? (
                <div>
                  <button
                    onClick={() => toggleItem(link.name)}
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                      isActive && "bg-muted text-foreground"
                    )}
                    aria-expanded={isExpanded}
                    aria-controls={`submenu-${link.name.toLowerCase().replace(/\s+/g, '-')}`}
                    aria-label={`${link.name} menu`}
                  >
                    <LinkIcon className="h-4 w-4" aria-hidden="true" />
                    <span className="whitespace-nowrap">{link.name}</span>
                    {isExpanded ? (
                      <ChevronDown className="ml-auto h-4 w-4" aria-hidden="true" />
                    ) : (
                      <ChevronRight className="ml-auto h-4 w-4" aria-hidden="true" />
                    )}
                  </button>
                  {isExpanded && hasSubItems && (
                    <div
                      className="ml-6 mt-2 space-y-1"
                      id={`submenu-${link.name.toLowerCase().replace(/\s+/g, '-')}`}
                      role="menu"
                      aria-label={`${link.name} submenu`}
                    >
                      {link.subItems!.map((subItem) => (
                        <Link
                          key={subItem.name}
                          href={subItem.href}
                          onClick={handleLinkClick}
                          className={cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-muted-foreground transition-all hover:text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                            pathname === subItem.href && "bg-muted text-foreground"
                          )}
                          role="menuitem"
                          aria-current={pathname === subItem.href ? "page" : undefined}
                        >
                          <span className="whitespace-nowrap">{subItem.name}</span>
                        </Link>
                      ))}
                    </div>
                  )}
                  {isExpanded && hasSubLinks && (
                    <div
                      className="ml-6 mt-2 space-y-1"
                      id={`submenu-${link.name.toLowerCase().replace(/\s+/g, '-')}`}
                      role="menu"
                      aria-label={`${link.name} submenu`}
                    >
                      {Array.isArray(link.subLinks) && link.subLinks.map((subLink: SubLink) => (
                        <Link
                          key={subLink.name}
                          href={subLink.href}
                          onClick={handleLinkClick}
                          className={cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-muted-foreground transition-all hover:text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                            pathname === subLink.href && "bg-muted text-foreground"
                          )}
                          role="menuitem"
                          aria-current={pathname === subLink.href ? "page" : undefined}
                        >
                          <span className="whitespace-nowrap">{subLink.name}</span>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  href={link.href}
                  onClick={handleLinkClick}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                    isActive && "bg-muted text-foreground"
                  )}
                  aria-current={isActive ? "page" : undefined}
                >
                  <LinkIcon className="h-4 w-4" aria-hidden="true" />
                  <span className="whitespace-nowrap">{link.name}</span>
                </Link>
              )}
            </div>
          );
          })
        )}
      </div>
    </div>
  );
}
