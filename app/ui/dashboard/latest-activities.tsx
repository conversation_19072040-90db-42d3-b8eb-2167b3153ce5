import { fetchLatestActivities } from '@/app/lib/data';
import { formatDateToLocal } from '@/app/lib/utils';

export default async function LatestActivities() {
  const activities = await fetchLatestActivities();

  return (
    <div className="rounded-lg bg-white p-4 shadow">
      <h2 className="text-lg font-medium">Latest Activities</h2>
      <ul className="mt-4 space-y-4">
        {activities.map((activity) => (
          <li key={activity.id} className="flex justify-between">
            <div>
              <p className="font-medium">{activity.summary}</p>
              <p className="text-sm text-gray-500">{activity.attendee}</p>
              <p className="text-sm text-gray-500">{activity.venue}</p>
            </div>
            <p className="text-sm text-gray-500">
              {formatDateToLocal(activity.date.toString())}
            </p>
          </li>
        ))}
      </ul>
    </div>
  );
}