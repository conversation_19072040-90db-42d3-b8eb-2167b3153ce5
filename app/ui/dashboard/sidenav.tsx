"use client";

import Link from "next/link";
import { Nav } from "@/app/ui/dashboard/nav-links";
import <PERSON><PERSON><PERSON> from "@/app/ui/amelia-logo";

export default function SideNav({ closeSidebar }: { closeSidebar: () => void }) {
  return (
    <div id="main-navigation" className="flex h-full flex-col bg-background border-r overflow-hidden">
      <Link
        className="flex h-16 items-center justify-center p-4 md:h-20 shrink-0 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        href="/"
        aria-label="Amelia home"
      >
        <div className="w-32 md:w-40">
          <AmeliaLogo />
        </div>
      </Link>
      <div className="flex flex-col space-y-2 p-2 overflow-y-auto h-[calc(100vh-4rem)]" role="navigation" aria-label="Main navigation">
        <Nav className="flex-1" isCollapsed={false} closeSidebar={closeSidebar} />
        <div className="hidden h-auto w-full grow rounded-md bg-muted md:block" aria-hidden="true"></div>
      </div>
    </div>
  );
}