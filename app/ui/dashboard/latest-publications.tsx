import { fetchLatestPublications } from '@/app/lib/data';
import { formatDateToLocal } from '@/app/lib/utils';

export default async function LatestPublications() {
  const publications = await fetchLatestPublications();

  return (
    <div className="rounded-lg bg-white p-4 shadow">
      <h2 className="text-lg font-medium">Latest Publications</h2>
      <ul className="mt-4 space-y-4">
        {publications.map((publication) => (
          <li key={publication.id} className="flex justify-between">
            <div>
              <p className="font-medium">{publication.title}</p>
              <p className="text-sm text-gray-500">{publication.authors}</p>
              <p className="text-sm text-gray-500">{publication.venue}</p>
            </div>
            <p className="text-sm text-gray-500">
              {formatDateToLocal(publication.created_at)}
            </p>
          </li>
        ))}
      </ul>
    </div>
  );
}