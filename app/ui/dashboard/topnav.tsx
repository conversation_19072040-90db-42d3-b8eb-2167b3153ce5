"use client";

import { PowerIcon } from "@heroicons/react/24/outline";
import { <PERSON><PERSON><PERSON>, ChevronLeft, Menu } from "lucide-react";
import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { CustomAvatar, CustomAvatarFallback, CustomAvatarImage } from "@/components/ui/custom-avatar";

interface ScholarProfile {
  scholar_id: string;
  name: string;
  affiliation: string;
  areas_of_interest: string;
  citations_all: number;
  h_index_all: number;
  profile_url: string;
  profile_image_url: string;
}

interface TopNavProps {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  isMobile: boolean;
}

export default function TopNav({ isSidebarOpen, toggleSidebar, isMobile }: TopNavProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [scholarProfile, setScholarProfile] = useState<ScholarProfile | null>(null);
  const [displayName, setDisplayName] = useState<string>('');
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch display name if user is logged in
        if (session?.user?.id) {
          const displayNameResponse = await fetch(`/api/profile/display-name?userId=${session.user.id}`);
          if (displayNameResponse.ok) {
            const { displayName } = await displayNameResponse.json();
            setDisplayName(displayName);
          }
        }

        // Fetch Google Scholar profile
        const scholarResponse = await fetch('/api/profile/scholar');
        if (scholarResponse.ok) {
          const scholarData = await scholarResponse.json();
          if (scholarData) {
            setScholarProfile(scholarData);
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    if (session?.user) {
      fetchData();
    }
  }, [session]);

  const handleSignOut = async () => {
    await signOut({ redirect: false }); // Sign out without immediate redirect
    router.push("/login"); // Manually redirect to login page
  };

  // Get user initials (max 2 characters)
  const getUserInitials = () => {
    if (displayName) {
      const names = displayName.split(' ');
      if (names.length === 1) return names[0][0].toUpperCase();
      return (names[0][0] + names[names.length - 1][0]).toUpperCase();
    }
    if (session?.user?.name) {
      const names = session.user.name.split(' ');
      if (names.length === 1) return names[0][0].toUpperCase();
      return (names[0][0] + names[names.length - 1][0]).toUpperCase();
    }
    return session?.user?.email?.[0]?.toUpperCase() || '?';
  };

  return (
    <div className="flex h-16 items-center justify-between gap-4 px-4 bg-white border-b border-gray-200">
      {/* Left side - Toggle button and App name */}
      <div className="flex items-center space-x-3">
        {/* Toggle sidebar button */}
        <button
          className={`p-2 rounded-full hover:bg-gray-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          onClick={toggleSidebar}
          aria-label={isSidebarOpen ? "Close sidebar" : "Open sidebar"}
          aria-expanded={isSidebarOpen}
          aria-controls="main-navigation"
        >
          {isSidebarOpen ? (
            <ChevronLeft className="w-5 h-5" aria-hidden="true" />
          ) : (
            <Menu className="w-5 h-5" aria-hidden="true" />
          )}
        </button>

        <Link href="/dashboard" className="text-lg font-semibold text-blue-600">
          Amelia
        </Link>
      </div>

      {/* Right side - User profile */}
      {session?.user && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-2">
              {/* User Avatar with Scholar Profile Image */}
              <CustomAvatar size="md">
                {scholarProfile && scholarProfile.profile_image_url && !imageError ? (
                  <CustomAvatarImage
                    src={scholarProfile.profile_image_url}
                    alt={displayName || session.user.name || 'Profile'}
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <CustomAvatarFallback className="bg-blue-500 text-white">
                    {getUserInitials()}
                  </CustomAvatarFallback>
                )}
              </CustomAvatar>

              {/* Display user name next to avatar */}
              <span className="hidden md:inline-block text-sm font-medium">
                {displayName || session.user.name || session.user.email}
              </span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem asChild>
              <Link href="/dashboard/profile" className="flex items-center">
                <CustomAvatar size="sm" className="mr-2">
                  {scholarProfile && scholarProfile.profile_image_url && !imageError ? (
                    <CustomAvatarImage
                      src={scholarProfile.profile_image_url}
                      alt={displayName || session.user.name || 'Profile'}
                    />
                  ) : (
                    <CustomAvatarFallback className="bg-blue-500 text-white text-xs">
                      {getUserInitials()}
                    </CustomAvatarFallback>
                  )}
                </CustomAvatar>
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/dashboard/settings" className="flex items-center">
                <Settings className="w-4 mr-2" />
                Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
              <PowerIcon className="w-4 mr-2" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}