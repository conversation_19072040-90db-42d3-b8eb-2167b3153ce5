import { fetchFilteredBiographies } from '@/app/lib/data';
import { formatDateToLocal } from '@/app/lib/utils';

export default async function BiographyTable({
  query,
  currentPage,
}: {
  query: string;
  currentPage: number;
}) {
  const biographies = await fetchFilteredBiographies(query, currentPage);

  return (
    <div className="mt-6 flow-root">
      <div className="inline-block min-w-full align-middle">
        <div className="rounded-lg bg-gray-50 p-2 md:pt-0">
          {/* Mobile View */}
          <div className="md:hidden">
            {biographies?.map((biography) => (
              <div key={biography.biography_id} className="mb-2 w-full rounded-md bg-white p-4">
                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <div className="mb-2">
                      <p className="text-sm text-gray-500">Last Updated: {formatDateToLocal(biography.date_of_last_update)}</p>
                    </div>
                  </div>
                </div>
                <div className="flex w-full items-center justify-between pt-4">
                  <div>
                    <p className="text-sm break-words">{biography.biography}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Desktop View */}
          <div className="overflow-x-auto">
            <table className="hidden w-full text-gray-900 md:table">
              <thead className="rounded-lg text-left text-sm font-normal">
                <tr>
                  <th scope="col" className="px-4 py-5 font-medium sm:pl-6">
                    Last Updated
                  </th>
                  <th scope="col" className="px-3 py-5 font-medium">
                    Biography
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {biographies?.map((biography) => (
                  <tr
                    key={biography.biography_id}
                    className="w-full border-b py-3 text-sm last-of-type:border-none [&:first-child>td:first-child]:rounded-tl-lg [&:first-child>td:last-child]:rounded-tr-lg [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg"
                  >
                    <td className="whitespace-nowrap py-3 pl-6 pr-3">
                      {formatDateToLocal(biography.date_of_last_update)}
                    </td>
                    <td className="px-3 py-3 break-words">
                      {biography.biography}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
} 