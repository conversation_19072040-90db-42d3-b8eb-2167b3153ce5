import Link from 'next/link';

import { Button } from '@/app/ui/button';
import { createPublication } from '@/app/lib/actions';


// Define the type for initialData
interface InitialData {
  authors?: string;
  title?: string;
  journal?: string;
  editors?: string;
  year?: string;
  pages?: string;
}

export default function Form({ initialData = {} }: { initialData?: InitialData }) {
  return (
    <form action={createPublication}> 
      <div className="rounded-md bg-gray-50 p-4 md:p-6">



        <div className="mb-4">
          <label htmlFor="authors" className="mb-2 block text-sm font-medium">
            Authors
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="authors"
                name="authors"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.authors}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="title" className="mb-2 block text-sm font-medium">
            Title
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="title"
                name="title"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.title}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="journal" className="mb-2 block text-sm font-medium">
            Journal
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="journal"
                name="journal"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.journal || ""}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="editors" className="mb-2 block text-sm font-medium">
            Editors
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="editors"
                name="editors"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.editors || ""}
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="year" className="mb-2 block text-sm font-medium">
            Year
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="year"
                name="year"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.year || ""}
                required
                />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="pages" className="mb-2 block text-sm font-medium">
            Pages
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="pages"
                name="pages"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={initialData.pages || ""}
              />
            </div>
          </div>
        </div>


      </div>
      <div className="mt-6 flex justify-end gap-4">
        <Link
          href="/dashboard/publications"
          className="flex h-10 items-center rounded-lg bg-gray-100 px-4 text-sm font-medium text-gray-600 transition-colors hover:bg-gray-200"
        >
          Cancel
        </Link>
        <Button type="submit">Create Publication</Button>
      </div>
    </form>
  );
}
