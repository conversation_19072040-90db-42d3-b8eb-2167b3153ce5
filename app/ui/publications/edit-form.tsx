'use client';


import Link from 'next/link';
import { Button } from '@/app/ui/button';
import { PublicationForm } from '@/app/lib/definitions';
import { updatePublication } from '@/app/lib/actions';

export default function EditPublicationForm({ publication }: { publication: PublicationForm }

) {
  const updateActivityWithId = updatePublication.bind(null, publication.id);

  return (
    <form action={updateActivityWithId}> 
      <div className="rounded-md bg-gray-50 p-4 md:p-6">

      <div className="mb-4">
          <label htmlFor="authors" className="mb-2 block text-sm font-medium">
            Authors
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="authors"
                name="authors"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={publication.authors}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="title" className="mb-2 block text-sm font-medium">
            Title
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="title"
                name="title"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={publication.title}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="journal" className="mb-2 block text-sm font-medium">
            Journal
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="journal"
                name="journal"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={publication.journal || ""}
                required
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="editors" className="mb-2 block text-sm font-medium">
            Editors
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="editors"
                name="editors"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={publication.editors || ""}
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="year" className="mb-2 block text-sm font-medium">
            Year
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="year"
                name="year"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={publication.year || ""}
                required
                />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="pages" className="mb-2 block text-sm font-medium">
            Pages
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="pages"
                name="pages"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                defaultValue={publication.pages || ""}
              />
            </div>
          </div>
        </div>



      </div>
      <div className="mt-6 flex justify-end gap-4">
        <Link
          href="/dashboard/activities"
          className="flex h-10 items-center rounded-lg bg-gray-100 px-4 text-sm font-medium text-gray-600 transition-colors hover:bg-gray-200"
        >
          Cancel
        </Link>
        <Button type="submit">Save Activity</Button>
      </div>
    </form>
  );
}
