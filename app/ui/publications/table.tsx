import Image from 'next/image';
import { fetchFilteredPublications } from '@/app/lib/data';
import { formatDateToLocal } from '@/app/lib/utils';

export default async function PublicationsTable({
  query,
  currentPage,
}: {
  query: string;
  currentPage: number;
}) {
  const publications = await fetchFilteredPublications(query, currentPage);

  return (
    <div className="mt-6 flow-root">
      <div className="inline-block min-w-full align-middle">
        <div className="rounded-lg bg-gray-50 p-2 md:pt-0">
          {/* Mobile View (unchanged) */}
          <div className="md:hidden">
            {publications?.map((publication) => (
              <div key={publication.id} className="mb-2 w-full rounded-md bg-white p-4">
                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <div className="mb-2">
                      <p className="font-medium">{publication.authors}</p>
                      <p className="text-sm text-gray-500">{publication.title}</p>
                    </div>
                    <p className="text-sm text-gray-500">{publication.journal}</p>
                    <p className="text-sm text-gray-500">{publication.editors}</p>
                  </div>
                </div>
                <div className="flex w-full items-center justify-between pt-4">
                  <div>
                    <p className="text-sm">{publication.year}</p>
                    <p className="text-sm"> {publication.pages}</p>
                    {/* <p>{formatDateToLocal(publication.date)}</p> */}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Desktop View - Responsive Table */}
          <div className="overflow-x-auto">
            <table className="hidden w-full text-gray-900 md:table">
              <thead className="rounded-lg text-left text-sm font-normal">
                <tr>
                  <th scope="col" className="px-4 py-5 font-medium sm:pl-6 max-w-[200px]">
                  Authors
                  </th>
                  <th scope="col" className="px-3 py-5 font-medium max-w-[150px]">
                    Title
                  </th>
                  <th scope="col" className="px-3 py-5 font-medium max-w-[250px]">
                    Journal
                  </th>
                  <th scope="col" className="px-3 py-5 font-medium max-w-[100px]">
                    Editors
                  </th>
                  <th scope="col" className="px-3 py-5 font-medium max-w-[120px]">
                    Year
                  </th>
                  <th scope="col" className="px-3 py-5 font-medium max-w-[120px]">
                    Pages
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {publications?.map((publication) => (
                  <tr
                    key={publication.id}
                    className="w-full border-b py-3 text-sm last-of-type:border-none [&:first-child>td:first-child]:rounded-tl-lg [&:first-child>td:last-child]:rounded-tr-lg [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg"
                  >
                    <td className="py-3 pl-6 pr-3 max-w-[200px] break-words">
                      <p>{publication.authors}</p>
                    </td>
                    <td className="px-3 py-3 max-w-[150px] break-words">
                      {publication.title}
                    </td>
                    <td className="px-3 py-3 max-w-[250px] break-words">
                      {publication.journal}
                    </td>
                    <td className="px-3 py-3 max-w-[100px] break-words">
                      {publication.editors}
                    </td>
                    <td className="px-3 py-3 max-w-[120px] break-words">
                      {/* {formatDateToLocal(publication.date)} */}
                      {publication.year}
                    </td>
                    <td className="px-3 py-3 max-w-[100px] break-words">
                      {publication.pages}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}