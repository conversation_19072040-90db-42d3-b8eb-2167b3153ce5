import "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      roles?: string[];
      facultySsoId?: string | null;
      facultyId?: number | null;
    };
  }

  interface JWT {
    id?: string;
    roles?: string[];
    facultySsoId?: string | null;
    facultyId?: number | null;
  }
}