'use server';
 
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import postgres from 'postgres';
import { create } from 'domain';
import { pages } from 'next/dist/build/templates/app-page';

const sql = postgres(process.env.POSTGRES_URL!, { ssl: 'require' });

 
const FormSchema = z.object({
  id: z.string(),
  customerId: z.string(),
  amount: z.coerce.number(),
  status: z.enum(['pending', 'paid']),
  date: z.string(),
});
 
const CreateInvoice = FormSchema.omit({ id: true, date: true });
 
// ...
export async function createInvoice(formData: FormData) {
    const { customerId, amount, status } = CreateInvoice.parse({
      customerId: formData.get('customerId'),
      amount: formData.get('amount'),
      status: formData.get('status'),
    });
    const amountInCents = amount * 100;
    const date = new Date().toISOString().split('T')[0];

    await sql`
    INSERT INTO invoices (customer_id, amount, status, date)
    VALUES (${customerId}, ${amountInCents}, ${status}, ${date})
  `;

    revalidatePath('/dashboard/invoices');
    redirect('/dashboard/invoices');

  }



  const FormSchemaActivity = z.object({
    id: z.string(),
    date: z.string(),
    venue: z.string(),
    attendee: z.string(),
    publication: z.string(),
    summary: z.string(),
    created_at: z.string(),
    });
   
  const CreateActivity = FormSchemaActivity.omit({ id: true, created_at: true });
   
  export async function createActivity(formData: FormData) {
    let success = false;

    try {
      const { date, venue, attendee, publication, summary } = CreateActivity.parse({
          date: formData.get('date'),
          venue: formData.get('venue'),
          attendee: formData.get('attendee'),
          publication: formData.get('publication'),
          summary: formData.get('summary'),
        });

      await sql`
        INSERT INTO activities (date, venue, attendee, publication, summary)
        VALUES (${date}, ${venue}, ${attendee}, ${publication}, ${summary})
      `;

      success = true;

    } catch (error) {
      console.error('Error creating activity:', error);
    }

    if (success) {
      redirect('/dashboard/activities/create?message=Activity+created+successfully&status=success');
    } else {
      redirect('/dashboard/activities/create?message=Failed+to+create+activity&status=error');
    }
  }

 /*
  export async function requestform(formData: FormData) {
    const raw = Object.fromEntries(formData.entries());
  
    try {
      // Parse the leaves array from the form data
      const parsedLeaves = JSON.parse(raw.leaves as string);
  
      // Validate the data using the schema
      const result = FormSchema.parse({
        name: raw.name,
        leaves: parsedLeaves,
      });
  
      // Insert into SQL in one go (insert the entire array of leaves)
      await sql`
        INSERT INTO leave_requests (name, leaves)
        VALUES (${result.name}, ${JSON.stringify(result.leaves)})
      `;
  
      return { success: true, redirectUrl: "/dashboard" };
    } catch (error) {
      console.error("Submission failed:", error);
      return { success: false, error: error instanceof Error ? error.message : "Invalid data" };
    }
  }
*/

export async function updateActivity(id: string, formData: FormData) {
  const { date, venue, attendee, publication, summary } = CreateActivity.parse({
    date: formData.get('date'),
    venue: formData.get('venue'),
    attendee: formData.get('attendee'),
    publication: formData.get('publication'),
    summary: formData.get('summary'),
  });
 
   await sql`
    UPDATE activities
    SET date = ${date}, venue = ${venue}, attendee = ${attendee}, publication = ${publication}, summary = ${summary}
    WHERE id = ${id}
  `;
 
  revalidatePath('/dashboard/activities');
  redirect('/dashboard/activities');
}


export async function deleteActivity(id: string) {
  await sql`DELETE FROM activities WHERE id = ${id}`;
  revalidatePath('/dashboard/activities');
}


export async function deletePublication(id: string) {
  await sql`DELETE FROM publications WHERE id = ${id}`;
  revalidatePath('/dashboard/publications');
}



const FormSchemaPublication = z.object({
  id: z.string(),
  authors: z.string(),
  title: z.string(),
  journal: z.string(),
  editors: z.string(),
  year: z.string(),
  pages: z.string(),
  created_at: z.string(),
  });
 
const CreatePublication = FormSchemaPublication.omit({ id: true, created_at: true });

export async function createPublication(formData: FormData) {
  let success = false;

  try {
    const { authors, title, journal, editors, year, pages  } = CreatePublication.parse({
      authors: formData.get('authors'),
      title: formData.get('title'),
      journal: formData.get('journal'),
      editors: formData.get('editors'),
      year: formData.get('year'),
      pages: formData.get('pages'),      
      });

    await sql`
      INSERT INTO publications (authors, emails, title, journal, editors, year, pages)
      VALUES (${authors}, '', ${title}, ${journal}, ${editors}, ${year}, ${pages})
    `;

    success = true;

  } catch (error) {
    console.error('Error creating activity:', error);
  }

  if (success) {
    redirect('/dashboard/publications/create?message=Publication+created+successfully&status=success');
  } else {
    redirect('/dashboard/publications/create?message=Failed+to+create+publication&status=error');
  }
}


export async function updatePublication(id: string, formData: FormData) {
  const { authors, title, journal, editors, year, pages  } = CreatePublication.parse({
    authors: formData.get('authors'),
    title: formData.get('title'),
    journal: formData.get('journal'),
    editors: formData.get('editors'),
    year: formData.get('year'),
    pages: formData.get('pages'),      
    });
 
   await sql`
    UPDATE publications
    SET authors = ${authors}, title = ${title}, journal = ${journal}, editors = ${editors}, year = ${year}, pages = ${pages} 
    WHERE id = ${id}
  `;
 
  revalidatePath('/dashboard/publications');
  redirect('/dashboard/publications');
}