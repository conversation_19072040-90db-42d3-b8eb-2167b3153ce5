import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { sql } from "@/app/lib/db";
import bcrypt from "bcrypt";

declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    roles?: string[];
    facultySsoId?: string | null;
    facultyId?: number | null;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID!,
      clientSecret: process.env.AUTH_GOOGLE_SECRET!,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const { email, password } = credentials as { email: string; password: string };
        console.log("Authorizing:", { email });

        try {
          // Step 1: Check if user exists in common.user table
          const users = await sql`
            SELECT u.user_id, p.password_hash
            FROM common.user u
            JOIN common.password p ON u.user_id = p.user_id
            WHERE u.email = ${email} AND u.deleted_at IS NULL AND p.deleted_at IS NULL
          `;
          const user = users[0];

          if (!user || !(await bcrypt.compare(password, user.password_hash))) {
            console.log("Authorize failed: Invalid credentials for", email);
            throw new Error("Invalid credentials");
          }

          // Step 5 & 6: Check faculty table and job family
          const facultyCheck = await sql`
            SELECT faculty_id, job_family FROM uw.faculty
            WHERE work_email = ${email} AND is_deleted = FALSE
          `;

          if (facultyCheck.length > 0) {
            const faculty = facultyCheck[0];

            // Step 6 & 7: Check if job family is 'Regular Faculty'
            if (faculty.job_family === 'Regular Faculty') {
              const [regularRole] = await sql`
                SELECT role_id FROM uw.institution_role
                WHERE role_name = 'regular_user' AND is_deleted = FALSE
              `;

              if (regularRole) {
                // Check if role is already assigned
                const existingRole = await sql`
                  SELECT 1 FROM uw.faculty_institution_role
                  WHERE faculty_id = ${faculty.faculty_id}
                  AND institution_role_id = ${regularRole.role_id}
                  AND is_deleted = FALSE
                `;

                if (!existingRole.length) {
                  await sql`
                    INSERT INTO uw.faculty_institution_role (faculty_id, institution_role_id)
                    VALUES (${faculty.faculty_id}, ${regularRole.role_id})
                  `;
                }
              }
            }
          }

          console.log("Authorize succeeded:", { id: user.user_id, email });
          return { id: user.user_id, email };
        } catch (error) {
          console.error("Authorization error:", error);

          // Check if it's a database connection error
          if (error && typeof error === 'object' && 'code' in error) {
            const dbError = error as { code: string, errno: string };
            // Handle various database connection errors
            if (
              dbError.code === 'CONNECT_TIMEOUT' ||
              dbError.errno === 'CONNECT_TIMEOUT' ||
              dbError.code === 'CONNECTION_CLOSED' ||
              dbError.code === 'CONNECTION_DESTROYED' ||
              dbError.code === 'CONNECTION_ERROR' ||
              dbError.code === 'ECONNREFUSED'
            ) {
              throw new Error("System is not available right now. Please try again later.");
            }
          }

          throw error;
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      const email = user.email!;
      if (!account) return false;

      const provider = account.provider;
      const providerId = account.providerAccountId || profile?.sub;

      try {
        await sql.begin(async (sql) => {
          // Step 1: Check if user exists in common.user table
          const existingUser = await sql`
            SELECT user_id FROM common.user WHERE email = ${email} AND deleted_at IS NULL
          `;
          let userId;

          if (!existingUser.length) {
            // Step 2: Register new user
            const [newUser] = await sql`
              INSERT INTO common.user (email, display_name)
              VALUES (${email}, ${user.name || email.split("@")[0]})
              RETURNING user_id
            `;
            userId = newUser.user_id;

            // Step 3: Assign public_user role
            const [publicRole] = await sql`
              SELECT role_id FROM common.role WHERE name = 'public_user' AND deleted_at IS NULL
            `;
            if (!publicRole) throw new Error('public_user role not found');

            await sql`
              INSERT INTO common.user_role (user_id, role_id)
              VALUES (${userId}, ${publicRole.role_id})
            `;
          } else {
            userId = existingUser[0].user_id;
          }

          // Step 5 & 6: Check faculty table and job family
          const facultyCheck = await sql`
            SELECT faculty_id, job_family FROM uw.faculty
            WHERE work_email = ${email} AND is_deleted = FALSE
          `;

          if (facultyCheck.length > 0) {
            const faculty = facultyCheck[0];

            // Step 6 & 7: Check if job family is 'Regular Faculty'
            if (faculty.job_family === 'Regular Faculty') {
              const [regularRole] = await sql`
                SELECT role_id FROM uw.institution_role
                WHERE role_name = 'regular_user' AND is_deleted = FALSE
              `;

              if (regularRole) {
                // Check if role is already assigned
                const existingRole = await sql`
                  SELECT 1 FROM uw.faculty_institution_role
                  WHERE faculty_id = ${faculty.faculty_id}
                  AND institution_role_id = ${regularRole.role_id}
                  AND is_deleted = FALSE
                `;

                if (!existingRole.length) {
                  await sql`
                    INSERT INTO uw.faculty_institution_role (faculty_id, institution_role_id)
                    VALUES (${faculty.faculty_id}, ${regularRole.role_id})
                  `;
                }
              }
            }
          }

          // Store auth method
          await sql`
            INSERT INTO common.auth_method (auth_method_id, user_id, provider, provider_id)
            VALUES (gen_random_uuid(), ${userId}, ${provider || ''}, ${providerId || ''})
            ON CONFLICT (provider, provider_id) DO NOTHING
          `;
        });
        return true;
      } catch (error) {
        console.error("Sign-in error:", error);
        return false;
      }
    },
    async jwt({ token, user, account }) {
      if (user && account) {
        const [authMethod] = await sql`
          SELECT user_id
          FROM common.auth_method
          WHERE provider = ${account.provider} AND provider_id = ${account.providerAccountId} AND deleted_at IS NULL
        `;
        if (authMethod) {
          token.id = authMethod.user_id;
        }
      }

      if (token.id) {
        // Get common roles
        const commonRoles = await sql`
          SELECT r.name
          FROM common.user_role ur
          JOIN common.role r ON ur.role_id = r.role_id
          WHERE ur.user_id = ${token.id} AND ur.deleted_at IS NULL AND r.deleted_at IS NULL
        `;

        // Get institution roles through faculty_institution_role table
        const institutionRoles = await sql`
          SELECT ir.role_name
          FROM uw.faculty f
          JOIN uw.faculty_institution_role fr ON f.faculty_id = fr.faculty_id
          JOIN uw.institution_role ir ON fr.institution_role_id = ir.role_id
          JOIN common.user u ON u.email = f.work_email
          WHERE u.user_id = ${token.id} AND fr.is_deleted = FALSE AND ir.is_deleted = FALSE
        `;

        // Get faculty information if the user is a faculty member
        const facultyResult = await sql`
          SELECT LOWER(f.sso_id) as sso_id, f.faculty_id
          FROM uw.faculty f
          JOIN common.user u ON u.email = f.work_email
          WHERE u.user_id = ${token.id} AND f.is_deleted = FALSE
          LIMIT 1
        `;

        if (facultyResult.length > 0) {
          token.facultySsoId = facultyResult[0].sso_id;
          token.facultyId = facultyResult[0].faculty_id;
        } else {
          token.facultySsoId = null;
          token.facultyId = null;
        }

        // Combine both role sets
        const commonRoleNames = commonRoles.map(row => row.name);
        const institutionRoleNames = institutionRoles.map(row => row.role_name);
        token.roles = [...commonRoleNames, ...institutionRoleNames];
      }

      // console.log("JWT callback:", token);
      return token;
    },
    async session({ session, token }) {
      // console.log("Session callback - Token:", token);
      if (token?.id) {
        session.user.id = token.id;
        session.user.roles = token.roles;
        session.user.facultySsoId = token.facultySsoId;
        session.user.facultyId = token.facultyId;
      }
      // console.log("Session updated:", session);
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/login",
  },
};