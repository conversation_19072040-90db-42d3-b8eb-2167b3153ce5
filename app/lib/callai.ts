// @/app/lib/actions.js
"use server";

import { redirect } from "next/navigation";
import OpenAI from "openai";

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

// New action for extracting data from OpenAI
export async function extractActivity(formData: FormData) {
  const rawInput = formData.get("rawInput");

  if (!rawInput) {
    throw new Error("No raw input provided");
  }
  let extractedData;

  try {
    const prompt = `
      Extract the following fields from this text: date, venue, attendee, publication, summary.
      Return the result as a JSON object. If a field is not found, leave it as an empty string.
      make sure any date is in the format YYYY-MM-DD. 
      today is ${new Date().toISOString().split("T")[0]}. if no date is mentioned, use today's date.
      if today or yesterday or any other date related workding is mentioned, calculate the date accordingly.
      Text: "${rawInput}"
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      max_tokens: 200,
    });


    const content = response?.choices?.[0]?.message?.content?.trim() || "";

    if (!content) {
      throw new Error("No content returned from OpenAI");
    }

    try {
      extractedData = JSON.parse(content);
    } catch (parseError) {
      // If parsing fails, attempt to extract JSON from potential markdown or text
      const jsonMatch = content.match(/{[\s\S]*}/);
      if (jsonMatch) {
        extractedData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error("Failed to parse OpenAI response as JSON");
      }
    }


  } catch (error) {
    console.error("Error extracting data from OpenAI:", error);
    redirect("/dashboard/activities/create");
  }

  console.log(extractedData);

  // Redirect back to the create page with query parameters
  const queryString = new URLSearchParams({
    date: extractedData.date || "",
    venue: extractedData.venue || "",
    attendee: extractedData.attendee || "",
    publication: extractedData.publication || "",
    summary: extractedData.summary || "",
  }).toString();

  redirect(`/dashboard/activities/create?${queryString}`);

}


export async function extractPublication(formData: FormData) {
    const rawInput = formData.get("rawInput");
  
    if (!rawInput) {
      throw new Error("No raw input provided");
    }
    let extractedData;
  
    try {
      const prompt = `
       extract the following fields from this text: authors, title, journal, editors, year, pages. 
      Return the result as a JSON object. If a field is not found, leave it as an empty string.
        Text: "${rawInput}"
      `;
  
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 200,
      });
  
  
      const content = response?.choices?.[0]?.message?.content?.trim() || "";
  
      if (!content) {
        throw new Error("No content returned from OpenAI");
      }
  
      try {
        extractedData = JSON.parse(content);
      } catch (parseError) {
        // If parsing fails, attempt to extract JSON from potential markdown or text
        const jsonMatch = content.match(/{[\s\S]*}/);
        if (jsonMatch) {
          extractedData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("Failed to parse OpenAI response as JSON");
        }
      }
  
  
    } catch (error) {
      console.error("Error extracting data from OpenAI:", error);
      redirect("/dashboard/publications/create");
    }
  
    console.log(extractedData);
  
    // Redirect back to the create page with query parameters
    const queryString = new URLSearchParams({
      authors: extractedData.authors || "",
      title: extractedData.title || "",
        journal: extractedData.journal || "",
        editors: extractedData.editors || "",
        year: extractedData.year || "",
        pages: extractedData.pages || "",
    }).toString();
  
    redirect(`/dashboard/publications/create?${queryString}`);
  
  }