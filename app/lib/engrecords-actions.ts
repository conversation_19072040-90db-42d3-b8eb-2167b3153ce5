'use client';

/**
 * Save an edit to an EngRecords field
 * @returns The updated record data
 */
export async function saveEngRecordsEdit(
  value: string,
  fieldName: string,
  recordId: string,
  tableName: string
): Promise<any> {
  // Get the current page URL
  const pageUrl = window.location.pathname;

  const response = await fetch('/api/engrecords/edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      value,
      fieldName,
      recordId,
      tableName,
      pageUrl,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to save changes');
  }

  // Return the updated data
  const data = await response.json();
  return data.data;
}
