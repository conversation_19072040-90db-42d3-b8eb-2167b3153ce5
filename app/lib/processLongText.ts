"use server";

import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Maximum token length for each chunk to send to OpenAI
const MAX_CHUNK_SIZE = 4000; // Adjust based on the model's context window

export interface RawExtraction {
  rawText: string; // The whole line of text for the publication or activity
}

export interface ProcessingResult {
  publications: RawExtraction[];
  activities: RawExtraction[];
  error?: string;
}

interface CombinedExtraction {
  publications: RawExtraction[];
  activities: RawExtraction[];
}

/**
 * Splits a long text into manageable chunks for processing by the AI
 */
function splitTextIntoChunks(text: string): string[] {
  const words = text.split(/\s+/);
  const chunks: string[] = [];
  let currentChunk: string[] = [];
  let currentChunkSize = 0;

  for (const word of words) {
    // Approximate token count (words are roughly equivalent to tokens)
    const wordTokens = word.length / 4 + 1;
    
    if (currentChunkSize + wordTokens > MAX_CHUNK_SIZE) {
      chunks.push(currentChunk.join(' '));
      currentChunk = [word];
      currentChunkSize = wordTokens;
    } else {
      currentChunk.push(word);
      currentChunkSize += wordTokens;
    }
  }

  if (currentChunk.length > 0) {
    chunks.push(currentChunk.join(' '));
  }

  return chunks;
}

/**
 * Process a large text to extract both publications and activities
 * This function is designed to be called from an API route
 */
export async function processLongText(text: string): Promise<ProcessingResult> {
  if (!text) {
    return {
      publications: [],
      activities: [],
      error: "No text provided"
    };
  }

  try {
    const chunks = splitTextIntoChunks(text);
    
    // Extract publications and activities from each chunk
    const publications: RawExtraction[] = [];
    const activities: RawExtraction[] = [];
    
    const today = new Date().toISOString().split("T")[0]; // Format: YYYY-MM-DD
    
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      
      // Extract both publications and activities with a single API call
      const extractionResult = await extractFromChunk(chunk, today);
      
      publications.push(...extractionResult.publications);
      activities.push(...extractionResult.activities);
    }

    // Deduplicate results (in case the same item appears in multiple chunks)
    const uniquePublications = deduplicateItems(publications);
    const uniqueActivities = deduplicateItems(activities);

    return {
      publications: uniquePublications,
      activities: uniqueActivities
    };
    
  } catch (error) {
    console.error("Error processing long text:", error);
    return {
      publications: [],
      activities: [],
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

/**
 * Extract both publications and activities from a chunk of text with a single API call
 */
async function extractFromChunk(chunk: string, today: string): Promise<CombinedExtraction> {
  try {
    const prompt = `
      Analyze the following text and extract two types of information:
      
      1. PUBLICATIONS: Identify and extract complete lines or paragraphs that describe academic publications.
         These typically include authors, title, journal, etc., but extract the ENTIRE line without breaking it down.
      
      2. ACTIVITIES: Identify and extract complete lines or paragraphs that describe activities or events.
         These might include position hold, organization, date period, etc., but extract the ENTIRE line without breaking it down.
      
      Return the result as a JSON object with two properties:
      - "publications": an array of objects, each with a single "rawText" property containing the full publication text
      - "activities": an array of objects, each with a single "rawText" property containing the full activity text
      
      If no publications or activities are found, return an empty array for that category.
      
      Example output format:
      {
        "publications": [
          { "rawText": "Smith, J., & Jones, L. (2022). Title of the paper. Journal Name, 45(2), 123-145." },
          { "rawText": "Another complete publication reference..." }
        ],
        "activities": [
          { "rawText": "Councilor, Canadian Operational Research Society (CORS), June 2022 – June 2024." },
          { "rawText": "Another complete activity description..." }
        ]
      }
      
      Text: "${chunk}"
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      max_tokens: 4000, // Increased token limit to accommodate both types of data
    });

    const content = response?.choices?.[0]?.message?.content?.trim() || "";

    if (!content) {
      return { publications: [], activities: [] };
    }

    try {
      let extractedData;
      try {
        extractedData = JSON.parse(content);
      } catch (parseError) {
        // If parsing fails, attempt to extract JSON from potential markdown or text
        const jsonMatch = content.match(/{[\s\S]*}/);
        if (jsonMatch) {
          extractedData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("Failed to parse OpenAI response as JSON");
        }
      }
      console.log(extractedData);

      return {
        publications: Array.isArray(extractedData.publications) ? extractedData.publications : [],
        activities: Array.isArray(extractedData.activities) ? extractedData.activities : []
      };
    } catch (parseError) {
      console.error("Error parsing JSON response:", parseError);
      
      // If all parsing attempts fail, log the response content for debugging
      console.error("Could not parse response. Raw content:", content);
      return { publications: [], activities: [] };
    }
  } catch (error) {
    console.error("Error calling OpenAI API:", error);
    return { publications: [], activities: [] };
  }
}

/**
 * Remove duplicate items based on text similarity
 */
function deduplicateItems(items: RawExtraction[]): RawExtraction[] {
  const uniqueItems: RawExtraction[] = [];
  const seenTexts = new Set<string>();

  for (const item of items) {
    // Normalize text for comparison (lowercase, remove extra spaces)
    const normalizedText = item.rawText.toLowerCase().trim().replace(/\s+/g, ' ');
    
    if (normalizedText && !seenTexts.has(normalizedText)) {
      seenTexts.add(normalizedText);
      uniqueItems.push(item);
    }
  }

  return uniqueItems;
} 