import { sql } from './db';
import {
  CustomerField,
  CustomersTableType,
  InvoiceForm,
  InvoicesTable,
  ActivityForm,
  PublicationForm,
} from './definitions';
import { formatCurrency } from './utils';
import { PublicationRepository, ActivityRepository } from '@/lib/repositories';

export async function fetchFilteredPublications(query: string, currentPage: number) {
  const publicationRepo = new PublicationRepository();
  return publicationRepo.getFilteredPublications(query, currentPage);
}





export async function fetchCardData() {
  try {
    // You can probably combine these into a single SQL query
    // However, we are intentionally splitting them to demonstrate
    // how to initialize multiple queries in parallel with JS.
    const invoiceCountPromise = sql`SELECT COUNT(*) FROM invoices`;
    const customerCountPromise = sql`SELECT COUNT(*) FROM customers`;
    const invoiceStatusPromise = sql`SELECT
         SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) AS "paid",
         SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) AS "pending"
         FROM invoices`;

    const data = await Promise.all([
      invoiceCountPromise,
      customerCountPromise,
      invoiceStatusPromise,
    ]);

    const numberOfInvoices = Number(data[0][0].count ?? '0');
    const numberOfCustomers = Number(data[1][0].count ?? '0');
    const totalPaidInvoices = formatCurrency(data[2][0].paid ?? '0');
    const totalPendingInvoices = formatCurrency(data[2][0].pending ?? '0');

    return {
      numberOfCustomers,
      numberOfInvoices,
      totalPaidInvoices,
      totalPendingInvoices,
    };
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch card data.');
  }
}

const ITEMS_PER_PAGE = 6;
export async function fetchFilteredInvoices(
  query: string,
  currentPage: number,
) {
  const offset = (currentPage - 1) * ITEMS_PER_PAGE;

  try {
    const invoices = await sql<InvoicesTable[]>`
      SELECT
        invoices.id,
        invoices.amount,
        invoices.date,
        invoices.status,
        customers.name,
        customers.email,
        customers.image_url
      FROM invoices
      JOIN customers ON invoices.customer_id = customers.id
      WHERE
        customers.name ILIKE ${`%${query}%`} OR
        customers.email ILIKE ${`%${query}%`} OR
        invoices.amount::text ILIKE ${`%${query}%`} OR
        invoices.date::text ILIKE ${`%${query}%`} OR
        invoices.status ILIKE ${`%${query}%`}
      ORDER BY invoices.date DESC
      LIMIT ${ITEMS_PER_PAGE} OFFSET ${offset}
    `;

    return invoices;
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch invoices.');
  }
}

export async function fetchInvoicesPages(query: string) {
  try {
    const data = await sql`SELECT COUNT(*)
    FROM invoices
    JOIN customers ON invoices.customer_id = customers.id
    WHERE
      customers.name ILIKE ${`%${query}%`} OR
      customers.email ILIKE ${`%${query}%`} OR
      invoices.amount::text ILIKE ${`%${query}%`} OR
      invoices.date::text ILIKE ${`%${query}%`} OR
      invoices.status ILIKE ${`%${query}%`}
  `;

    const totalPages = Math.ceil(Number(data[0].count) / ITEMS_PER_PAGE);
    return totalPages;
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch total number of invoices.');
  }
}

export async function fetchInvoiceById(id: string) {
  try {
    const data = await sql<InvoiceForm[]>`
      SELECT
        invoices.id,
        invoices.customer_id,
        invoices.amount,
        invoices.status
      FROM invoices
      WHERE invoices.id = ${id};
    `;

    const invoice = data.map((invoice) => ({
      ...invoice,
      // Convert amount from cents to dollars
      amount: invoice.amount / 100,
    }));

    return invoice[0];
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch invoice.');
  }
}

export async function fetchCustomers() {
  try {
    const customers = await sql<CustomerField[]>`
      SELECT
        id,
        name
      FROM customers
      ORDER BY name ASC
    `;

    return customers;
  } catch (err) {
    console.error('Database Error:', err);
    throw new Error('Failed to fetch all customers.');
  }
}

export async function fetchFilteredCustomers(query: string) {
  try {
    const data = await sql<CustomersTableType[]>`
		SELECT
		  customers.id,
		  customers.name,
		  customers.email,
		  customers.image_url,
		  COUNT(invoices.id) AS total_invoices,
		  SUM(CASE WHEN invoices.status = 'pending' THEN invoices.amount ELSE 0 END) AS total_pending,
		  SUM(CASE WHEN invoices.status = 'paid' THEN invoices.amount ELSE 0 END) AS total_paid
		FROM customers
		LEFT JOIN invoices ON customers.id = invoices.customer_id
		WHERE
		  customers.name ILIKE ${`%${query}%`} OR
        customers.email ILIKE ${`%${query}%`}
		GROUP BY customers.id, customers.name, customers.email, customers.image_url
		ORDER BY customers.name ASC
	  `;

    const customers = data.map((customer) => ({
      ...customer,
      total_pending: formatCurrency(customer.total_pending),
      total_paid: formatCurrency(customer.total_paid),
    }));

    return customers;
  } catch (err) {
    console.error('Database Error:', err);
    throw new Error('Failed to fetch customer table.');
  }
}

export async function fetchFilteredActivities(query: string, currentPage: number) {
  const pageSize = 10;
  const offset = (currentPage - 1) * pageSize;

  const activities = await sql`
    SELECT id, date, venue, attendee, publication, summary, created_at
    FROM activities
    WHERE attendee ILIKE ${'%' + query + '%'}
    ORDER BY date DESC
    LIMIT ${pageSize}
    OFFSET ${offset}
  `;

  return activities;
}

export async function fetchActivitiesPages(query: string) {
  const countResult = await sql`
    SELECT COUNT(*)
    FROM activities
    WHERE summary ILIKE ${'%' + query + '%'}
  `;

  const totalCount = countResult[0].count;
  const pageSize = 10;
  const totalPages = Math.ceil(totalCount / pageSize);

  return totalPages;
}

export async function fetchLatestActivities() {
  const activityRepo = new ActivityRepository();
  return activityRepo.getLatestActivities();
}

export async function fetchLatestPublications() {
  const publicationRepo = new PublicationRepository();
  return publicationRepo.getLatestPublications();
}

export async function fetchActivityById(id: string) {
  try {
    const data = await sql<ActivityForm[]>`
      SELECT
        activities.id,
        activities.date,
        activities.venue,
        activities.attendee,
        activities.publication,
        activities.summary
      FROM activities
      WHERE activities.id = ${id};
    `;

    return data[0];
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch activity.');
  }
}


export async function fetchPublicationById(id: string) {
  try {
    const data = await sql<PublicationForm[]>`
      SELECT
        publications.id,
        publications.authors,
        publications.title,
        publications.journal,
        publications.editors,
        publications.year,
        publications.pages,
      FROM publications
      WHERE publications.id = ${id};
    `;

    return data[0];
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch publication.');
  }
}

export async function fetchFilteredBiographies(query: string, currentPage: number) {
  const pageSize = 10;
  const offset = (currentPage - 1) * pageSize;

  const biographies = await sql`
    SELECT
      biography_id,
      date_of_last_update,
      biography
    FROM uw.biography
    WHERE biography ILIKE ${'%' + query + '%'}
    ORDER BY date_of_last_update DESC
    LIMIT ${pageSize}
    OFFSET ${offset}
  `;

  return biographies;
}

export async function fetchBiographyPages(query: string) {
  const count = await sql`
    SELECT COUNT(*)
    FROM uw.biography
    WHERE biography ILIKE ${'%' + query + '%'}
  `;

  const totalPages = Math.ceil(Number(count[0].count) / 10);
  return totalPages;
}