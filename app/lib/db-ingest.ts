import postgres from "postgres";

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */

// Define the global type for TypeScript
declare global {
  var sqlIngest: ReturnType<typeof postgres> | undefined;
}

let sqlIngest: ReturnType<typeof postgres>;

if (!process.env.POSTGRES_URL_ingest) {
  throw new Error('Missing POSTGRES_URL_ingest environment variable');
}

const isProduction = process.env.NODE_ENV === 'production';

if (isProduction) {
  // In production, use a regular connection
  sqlIngest = postgres(process.env.POSTGRES_URL_ingest, {
    max: 20, // Max connections in the pool
    idle_timeout: 30, // Close idle connections after 30s
    ssl: 'require',
    connect_timeout: 30, // Increase connection timeout to 30 seconds
    max_lifetime: 60 * 30, // Maximum lifetime of a connection in seconds
    connection: {
      application_name: 'amelia-ingest',
    },
    onnotice: () => {}, // Suppress notices
    onparameter: () => {}, // Suppress parameter changes
    debug: false, // Disable debug logging in production
  });
} else {
  // In development, use a global variable to avoid multiple connections
  if (!global.sqlIngest) {
    global.sqlIngest = postgres(process.env.POSTGRES_URL_ingest, {
      max: 10, // Fewer connections for development
      idle_timeout: 30,
      ssl: 'require',
      connect_timeout: 30, // Increase connection timeout to 30 seconds
      max_lifetime: 60 * 30, // Maximum lifetime of a connection in seconds
      connection: {
        application_name: 'amelia-ingest-dev',
      },
      onnotice: () => {}, // Suppress notices
      onparameter: () => {}, // Suppress parameter changes
      debug: true, // Enable debug logging in development
    });
  }
  sqlIngest = global.sqlIngest;
}

// Ensure the connection is closed on process exit
process.on("SIGINT", async () => {
  await sqlIngest.end();
  process.exit(0);
});

export { sqlIngest }; 