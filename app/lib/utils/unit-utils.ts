import { sql } from "@/app/lib/db";

/**
 * Get the level 4 unit ID for a faculty member
 * 
 * If the faculty's primary unit is already at level 4, returns it directly
 * If the faculty's primary unit is at a higher level (1-3), finds the first child unit at level 4
 * If the faculty's primary unit is at a lower level (5+), finds the parent unit at level 4
 * 
 * @param facultyId The faculty ID
 * @returns The level 4 unit ID or null if not found
 */
export async function getLevel4UnitIdForFaculty(facultyId: number): Promise<number | null> {
  try {
    // First, get the faculty's primary unit and its level
    const [facultyUnit] = await sql`
      SELECT f.primary_unit_id, u.level_number
      FROM uw.faculty f
      JOIN uw.unit u ON f.primary_unit_id = u.unit_id
      WHERE f.faculty_id = ${facultyId}
      AND f.is_deleted = FALSE
      AND u.is_deleted = FALSE
    `;

    if (!facultyUnit) {
      return null;
    }

    // If the primary unit is already at level 4, return it
    if (facultyUnit.level_number === 4) {
      return facultyUnit.primary_unit_id;
    }

    // If the primary unit is at a higher level (1-3), find the first child unit at level 4
    if (facultyUnit.level_number < 4) {
      const [childUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the faculty's primary unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${facultyUnit.primary_unit_id}
          AND is_deleted = FALSE

          UNION ALL

          -- Get all child units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT unit_id
        FROM unit_hierarchy
        WHERE level_number = 4
        LIMIT 1
      `;

      if (childUnit) {
        return childUnit.unit_id;
      }
    }

    // If the primary unit is at a lower level (5+), find the parent unit at level 4
    if (facultyUnit.level_number > 4) {
      const [parentUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the faculty's primary unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${facultyUnit.primary_unit_id}
          AND is_deleted = FALSE

          UNION ALL

          -- Get all parent units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT unit_id
        FROM unit_hierarchy
        WHERE level_number = 4
        LIMIT 1
      `;

      if (parentUnit) {
        return parentUnit.unit_id;
      }
    }

    // If no level 4 unit was found, return null
    return null;
  } catch (error) {
    console.error("Error getting level 4 unit for faculty:", error);
    return null;
  }
}
