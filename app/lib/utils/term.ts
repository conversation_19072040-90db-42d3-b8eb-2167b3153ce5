/**
 * Utility functions for handling university term codes
 * 
 * Term codes are 4 digits like 1225, 1251, 1249 etc.
 * - Last digit: 1 = January (Winter), 5 = May (Spring), 9 = September (Fall)
 * - 2nd and 3rd digits: year (e.g., 27 = 2027)
 * - 1st digit: century (0 = 1900s, 1 = 2000s)
 * 
 * Examples:
 * - 1271 = Winter 2027
 * - 0939 = Fall 1993
 * - 1011 = Winter 2001
 */

/**
 * Convert a term code to a readable term year string
 * @param termId - The term code (e.g., 1231, 0939)
 * @returns A readable term year string (e.g., "Winter 2023", "Fall 1993")
 */
export function formatTerm(termId: number | string): string {
  const termIdStr = termId.toString();
  
  // Extract century, year, and term
  const century = termIdStr.length >= 4 ? termIdStr.charAt(0) : '1';
  const year = termIdStr.length >= 4 ? termIdStr.substring(1, 3) : termIdStr.substring(0, 2);
  const term = termIdStr.length >= 4 ? termIdStr.charAt(3) : termIdStr.charAt(2);
  
  // Convert to full year (e.g., 12 -> 2012 or 2112 depending on century)
  const fullYear = (century === '0' ? '19' : '20') + year;
  
  // Convert term digit to season
  let season = '';
  if (term === '1') {
    season = 'Winter';
  } else if (term === '5') {
    season = 'Spring';
  } else if (term === '9') {
    season = 'Fall';
  } else {
    season = 'Unknown';
  }
  
  return `${season} ${fullYear}`;
}

/**
 * Get the current term code
 * @returns The current term code
 */
export function getCurrentTerm(): string {
  const now = new Date();
  const year = now.getFullYear().toString().substring(2); // Get last 2 digits of year
  const month = now.getMonth() + 1; // 0-indexed, so add 1
  
  // Determine term based on month
  let term = '';
  if (month >= 1 && month <= 4) {
    term = '1'; // Winter term (January)
  } else if (month >= 5 && month <= 8) {
    term = '5'; // Spring term (May)
  } else {
    term = '9'; // Fall term (September)
  }
  
  // First digit is 1 for 2000s
  return `1${year}${term}`;
}

/**
 * Get a list of recent terms
 * @param count - Number of terms to return
 * @param startTerm - Term to start from (defaults to current term)
 * @returns Array of term codes
 */
export function getRecentTerms(count: number = 10, startTerm?: string): string[] {
  const terms: string[] = [];
  let currentTerm = startTerm || getCurrentTerm();
  
  for (let i = 0; i < count; i++) {
    terms.push(currentTerm);
    currentTerm = getPreviousTerm(currentTerm);
  }
  
  return terms;
}

/**
 * Get the previous term
 * @param termId - The current term code
 * @returns The previous term code
 */
function getPreviousTerm(termId: string): string {
  const termIdStr = termId.toString();
  
  // Extract century, year, and term
  const century = termIdStr.charAt(0);
  let year = parseInt(termIdStr.substring(1, 3));
  let term = termIdStr.charAt(3);
  
  // Calculate previous term
  if (term === '1') {
    // Winter -> Fall of previous year
    term = '9';
    year -= 1;
  } else if (term === '5') {
    // Spring -> Winter of same year
    term = '1';
  } else if (term === '9') {
    // Fall -> Spring of same year
    term = '5';
  }
  
  // Handle year rollover (e.g., 00 -> 99)
  if (year < 0) {
    year = 99;
    // Also handle century rollover (e.g., 1999 -> 0999)
    const newCentury = century === '1' ? '0' : '0';
    return `${newCentury}${year.toString().padStart(2, '0')}${term}`;
  }
  
  return `${century}${year.toString().padStart(2, '0')}${term}`;
}
