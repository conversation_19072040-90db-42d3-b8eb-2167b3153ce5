import { differenceInMonths } from 'date-fns';

/**
 * Calculates sabbatical salary arrangement based on past leave history.
 */
export function calculateArrangement(pastLeaves: any[], currentFrom: string, currentTo: string, nonTeachTerm: any[], firstDate: string, tenure: string){

  if (!currentFrom || !currentTo ) return '';

  const currentFromDate = new Date(currentFrom);
  const currentToDate = new Date(currentTo);
  const joinDate = new Date(firstDate);



  if ( isNaN(currentFromDate.getTime()) || isNaN(currentToDate.getTime()) || isNaN(joinDate.getTime()) ) return '';

  let baseDate = joinDate;

  if (Array.isArray(pastLeaves) && pastLeaves.length > 0) {
    // Find most recent past leave
    const sorted = [...pastLeaves].sort(
      (a, b) => new Date(b.to).getTime() - new Date(a.to).getTime()
    );
    const recent = sorted[0];
    const recentToDate = new Date(recent.to);
    if (!isNaN(recentToDate.getTime())) {
      baseDate = recentToDate;
    }
  }

  const sortedNonTech = [...nonTeachTerm].sort((a, b) =>
    new Date(b.to).getTime() - new Date(a.to).getTime()
  );

  const nonTech = sortedNonTech[0];

  if (isNaN(currentFromDate.getTime()) || isNaN(joinDate.getTime())) return '';

  const monthsOfService = differenceInMonths(currentFromDate, baseDate);

  const monthsOfLeave = differenceInMonths(currentToDate, currentFromDate);
  let percent = 0;
  let bonus = 0;
  console.log(monthsOfService+'===');
  console.log(monthsOfLeave);
  console.log(tenure);
  if(tenure !== 'Tenured') return 'Not eligible';

  // --- Rule: Early Sabbatical (after 36 months service) ---
  if (monthsOfService >= 36 && monthsOfService <= 72 && monthsOfLeave === 6) {
    return '85';
  }

  // --- Rule: Regular Sabbatical (after 72 months) ---
  if (monthsOfService >= 72) {
  
    console.log("inside >=72");

      if (monthsOfLeave == 6) {
        console.log("inside === 6 ");
        return '100';
      } else if (monthsOfLeave >= 12) {

        
        const bonusMonths = Math.min(monthsOfService - 72, 36); // max 3-year credit
        const bonusPercent = bonusMonths * 0.7;
        console.log(bonusPercent);
        const finalPay = Math.min(100, 85 + bonusPercent);
        return `${finalPay.toFixed(1)}`;

      }
       console.log("outside >=72");
  }

  // Not eligible (under 36 months or unsupported leave duration)
  return 'No Eligible';

};