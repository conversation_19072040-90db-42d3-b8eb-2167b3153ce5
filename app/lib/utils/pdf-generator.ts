/**
 * PDF Generator for Merit Review Reports
 *
 * This utility generates a PDF file based on the merit review template
 * and fills it with data from the merit review submission.
 */

// Note: You need to install jspdf with: pnpm add jspdf
import jsPDF from 'jspdf';

interface MeritReportData {
  // Faculty information
  faculty_name?: string;
  department?: string;
  unit_name?: string;
  rank?: string;
  administrative_role?: string;
  research_chair?: string;

  // Report metadata
  report_type?: string; // 'APR' or 'BPR'
  report_year?: number;
  status?: string;

  // Ratings and weights
  teaching_rating?: number;
  research_rating?: number;
  service_rating?: number;
  teaching_weight?: number;
  research_weight?: number;
  service_weight?: number;

  // Previous ratings and weights
  previous_teaching_rating?: number;
  previous_research_rating?: number;
  previous_service_rating?: number;
  previous_teaching_weight?: number;
  previous_research_weight?: number;
  previous_service_weight?: number;

  // Teaching data
  teaching?: {
    teaching_weight?: number;
    research_weight?: number;
    service_weight?: number;
    previous_teaching_rating?: number;
    previous_research_rating?: number;
    previous_service_rating?: number;
    previous_teaching_weight?: number;
    previous_research_weight?: number;
    previous_service_weight?: number;
    course_design?: string;
    teaching_implementation?: string;
    teaching_development?: string;
    educational_leadership?: string;
    accomplishments?: string;
    goals?: string;
  };

  // Teaching courses
  teaching_courses?: {
    term_year: string;
    course_number: string;
    course_title: string;
    scp_q1_q3: string;
    scp_q1_q3_std_dev: string;
    scp_q4_q6: string;
    scp_q4_q6_std_dev: string;
    num_students: number;
    response_percentage: number;
  }[];

  // Teaching development activities
  teaching_development_activities?: {
    activity: string;
    date: string;
    duration: string;
    organizer: string;
  }[];

  // Student supervision
  student_supervision?: {
    undergrad_supervised?: number;
    undergrad_cosupervised?: number;
    masters_supervised?: number;
    masters_cosupervised?: number;
    phd_supervised?: number;
    phd_cosupervised?: number;
    pdf_supervised?: number;
    pdf_cosupervised?: number;
    visitors_supervised?: number;
    visitors_cosupervised?: number;
  };

  // Undergraduate supervision details
  undergrad_supervision?: {
    name: string;
    work_supervised: string;
    supervisory_period: string;
    research_title: string;
  }[];

  // Graduate supervision details
  grad_supervision_completed?: {
    name: string;
    co_supervisor?: string;
    degree: string;
    supervisory_period: string;
    thesis_title: string;
  }[];

  // Graduate supervision in progress
  grad_supervision_in_progress?: {
    name: string;
    co_supervisor?: string;
    degree: string;
    supervisory_period: string;
    research_title: string;
  }[];

  // Course-based masters supervision
  course_masters_supervision?: {
    name: string;
    co_supervisor?: string;
    degree: string;
    supervisory_period: string;
    project_title: string;
  }[];

  // Postdoc/visiting scholar supervision
  postdoc_supervision?: {
    name: string;
    co_supervisor?: string;
    status: string;
    supervisory_period: string;
    research_title: string;
  }[];

  // Research data
  research?: {
    accomplishments?: string;
    goals?: string;
  };

  // Research publications
  research_publications?: {
    publication_type: string;
    authors: string;
    title: string;
    venue: string;
    year: number;
    status: string;
    details?: string;
  }[];

  // Research grants
  research_grants?: {
    grant_type: string;
    pi_name: string;
    collaborators?: string;
    title: string;
    agency: string;
    amount: number;
    installment_year?: number;
    share_percentage?: number;
    status: string;
    submission_date?: string;
  }[];

  // Service data
  service?: {
    departmental?: string;
    faculty?: string;
    university?: string;
    accomplishments?: string;
    goals?: string;
  };

  // Service activities
  service_activities?: {
    service_type: string;
    activity_name: string;
    organization: string;
    role: string;
    start_date: string;
    end_date?: string;
    time_commitment: string;
    description?: string;
  }[];

  // Professional registrations
  professional_registrations?: {
    designation: string;
    registration_year: number;
    province: string;
    requirements: string;
  }[];

  // Awards
  awards?: {
    award_name: string;
    award_type: string;
    awarding_body: string;
    year: number;
    description?: string;
  }[];

  // Additional comments
  additionalComments?: {
    comments?: string;
  };
}

/**
 * Generate a PDF file from merit review data
 *
 * @param data The merit review data to include in the PDF
 * @returns A Blob containing the PDF file
 */
export function generateMeritReviewPDF(data: MeritReportData): Blob {
  // Create a new PDF document
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.getWidth();
  let y = 20; // Starting y position

  // Helper function to add text with automatic line breaks and better spacing
  const addText = (text: string | undefined, x: number, y: number, options: any = {}) => {
    if (!text) return y;

    const fontSize = options.fontSize || 12;
    const maxWidth = options.maxWidth || pageWidth - 40;
    const lineSpacing = options.lineSpacing || 1.2; // Increased line spacing for better readability

    doc.setFontSize(fontSize);
    if (options.bold) doc.setFont('helvetica', 'bold');
    else doc.setFont('helvetica', 'normal');

    // Set text color
    if (options.color) {
      doc.setTextColor(options.color);
    } else {
      doc.setTextColor(0, 0, 0); // Default black
    }

    const lines = doc.splitTextToSize(text, maxWidth);
    doc.text(lines, x, y);

    // Add underline if specified
    if (options.underline) {
      const textWidth = doc.getStringUnitWidth(lines[0]) * fontSize / doc.internal.scaleFactor;
      doc.setDrawColor(0);
      doc.setLineWidth(0.5);
      doc.line(x, y + 1, x + textWidth, y + 1);
    }

    return y + (lines.length * fontSize * lineSpacing);
  };

  // Helper function to add page numbers
  const addPageNumbers = () => {
    const pageCount = doc.internal.pages.length - 1;
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      const pageSize = doc.internal.pageSize;
      const pageWidth = pageSize.getWidth();
      const pageHeight = pageSize.getHeight();

      doc.setFontSize(8);
      doc.setTextColor(100, 100, 100);
      doc.text(`Page ${i} of ${pageCount}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
    }
  };

  // Helper function to add a properly formatted table with borders and alignment
  const addTable = (headers: string[], rows: string[][], x: number, y: number, colWidths: number[], options: any = {}) => {
    const fontSize = options.fontSize || 9;
    const lineHeight = fontSize * 1.5;
    const cellPadding = 3;
    // Calculate absolute column positions
    const colPositions = colWidths.map((_, index) =>
      x + colWidths.slice(0, index).reduce((sum, width) => sum + width, 0)
    );

    // Calculate table width
    const tableWidth = colWidths.reduce((sum, width) => sum + width, 0);

    // Draw table header background
    doc.setFillColor(240, 240, 240); // Light gray background for header
    doc.rect(x, y, tableWidth, lineHeight, 'F');

    // Draw header text
    doc.setFontSize(fontSize);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);

    headers.forEach((header, i) => {
      // Center the text in the column
      const textWidth = doc.getStringUnitWidth(header) * fontSize / doc.internal.scaleFactor;
      const textX = colPositions[i] + (colWidths[i] - textWidth) / 2;
      doc.text(header, textX, y + lineHeight/2 + fontSize/3);
    });

    // Draw header bottom border
    doc.setDrawColor(204, 204, 204); // Light gray for borders
    doc.setLineWidth(0.5);
    doc.line(x, y + lineHeight, x + tableWidth, y + lineHeight);

    // Move to first row
    y += lineHeight;

    // Draw rows
    doc.setFont('helvetica', 'normal');

    rows.forEach((row, rowIndex) => {
      const rowY = y + rowIndex * lineHeight;

      // Draw row background (alternating colors for better readability)
      if (rowIndex % 2 === 1) {
        doc.setFillColor(248, 248, 248);
        doc.rect(x, rowY, tableWidth, lineHeight, 'F');
      }

      // Draw cell text
      row.forEach((cell, i) => {
        const cellText = cell || '';
        const lines = doc.splitTextToSize(cellText, colWidths[i] - (2 * cellPadding));

        // Calculate vertical position to center text if it's a single line
        const cellY = lines.length > 1
          ? rowY + fontSize + cellPadding
          : rowY + lineHeight/2 + fontSize/3;

        doc.text(lines, colPositions[i] + cellPadding, cellY);
      });

      // Draw horizontal line after each row
      doc.line(x, rowY + lineHeight, x + tableWidth, rowY + lineHeight);
    });

    // Draw vertical lines
    let accumulatedWidth = x;
    colWidths.forEach(width => {
      accumulatedWidth += width;
      doc.line(accumulatedWidth, y - lineHeight, accumulatedWidth, y + (rows.length * lineHeight));
    });

    // Draw left and right borders
    doc.line(x, y - lineHeight, x, y + (rows.length * lineHeight));
    doc.line(x + tableWidth, y - lineHeight, x + tableWidth, y + (rows.length * lineHeight));

    return y + (rows.length * lineHeight) + 5;
  };

  // Helper function to add a header to each page
  const addHeader = (pageNum: number) => {
    // Skip header on first page as it already has the title
    if (pageNum === 1) return;

    doc.setFillColor(240, 240, 240);
    doc.rect(0, 0, pageWidth, 20, 'F');

    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(80, 80, 80);

    // Add faculty name and report type/year
    const headerText = `${data.faculty_name || 'Faculty Member'} - ${data.report_type || ''} ${data.report_year || ''}`;
    doc.text(headerText, 20, 13);
  };

  // Helper function to check if we need a new page
  const checkNewPage = (currentY: number, neededSpace: number = 50) => {
    const pageHeight = doc.internal.pageSize.getHeight();
    if (currentY + neededSpace > pageHeight - 20) {
      doc.addPage();
      const newPageNum = doc.internal.pages.length - 1;
      addHeader(newPageNum);
      return 30; // Reset Y to top of new page with some margin for the header
    }
    return currentY;
  };

  // Add header
  y = addText(`${data.faculty_name || 'Faculty Name'}, ${data.department || data.unit_name || 'Department'}`, 20, y, { fontSize: 16, bold: true });

  if (data.rank || data.administrative_role) {
    y += 5;
    y = addText(`${data.rank || ''} ${data.administrative_role ? `/ ${data.administrative_role}` : ''}`, 20, y, { fontSize: 14 });
  }

  y += 10;
  y = addText(`ANNUAL/BIENNIAL REPORT SUMMARY, ${data.report_year || new Date().getFullYear()}`, 20, y, { fontSize: 14, bold: true });

  y += 5;
  const reportTypeText = data.report_type === 'APR'
    ? `Probationary/Definite APR for ${data.report_year}`
    : `Tenured/Permanent BPR for ${data.report_year ? (data.report_year - 1) : ''}-${data.report_year}`;
  y = addText(reportTypeText, 20, y, { fontSize: 12, bold: true });

  // Add ratings and weights section
  y += 15;
  y = addText('Ratings and Weights (To be completed by Departmental Merit Committee).', 20, y, { fontSize: 12, bold: true });

  // Create ratings table using the improved table function
  y += 10;
  const currentRatingHeaders = ['', 'Teaching (T)', 'Research (R)', 'Service (S)'];
  const currentRatingRows = [
    ['Merit Rating', '', '', ''],
    ['Weights', '', '', '']
  ];
  y = addTable(currentRatingHeaders, currentRatingRows, 20, y, [30, 50, 50, 50]);

  // Add previous ratings section
  y += 10;
  y = addText(`${data.report_year ? data.report_year - 1 : ''} (APR) ${data.report_year ? data.report_year - 2 : ''} (BPR) Ratings and Weights (To be filled in by the candidate)`, 20, y, { fontSize: 12, bold: true });

  // Get previous ratings data
  const teaching = data.teaching || {};

  // Create previous ratings table using the improved table function
  y += 10;
  const prevRatingHeaders = ['', 'Teaching (T)', 'Research (R)', 'Service (S)'];
  const prevRatingRows = [
    ['Merit Rating', teaching.previous_teaching_rating?.toString() || '', teaching.previous_research_rating?.toString() || '', teaching.previous_service_rating?.toString() || ''],
    ['Weights', teaching.previous_teaching_weight?.toString() || '', teaching.previous_research_weight?.toString() || '', teaching.previous_service_weight?.toString() || '']
  ];
  y = addTable(prevRatingHeaders, prevRatingRows, 20, y, [30, 50, 50, 50]);

  // Add teaching section
  y += 15;
  y = checkNewPage(y, 100);
  y = addText('TEACHING (3 YEARS)', 20, y, { fontSize: 14, bold: true });

  // Add teaching courses if available
  if (data.teaching_courses && data.teaching_courses.length > 0) {
    y += 10;
    y = addText('Undergraduate and Graduate Courses with Student Course Perception (SCP) survey scores.', 20, y, { fontSize: 12, bold: true });

    y += 10;
    const courseHeaders = ['Term', 'Course', 'Title', 'SCP Q1-3', 'SCP Q4-6', '# Students', '% Response'];
    const courseRows = data.teaching_courses.map(course => [
      course.term_year || '',
      course.course_number || '',
      course.course_title || '',
      course.scp_q1_q3 || '',
      course.scp_q4_q6 || '',
      course.num_students?.toString() || '',
      course.response_percentage?.toString() || ''
    ]);

    y = addTable(courseHeaders, courseRows, 20, y, [30, 30, 50, 30, 30, 30, 30]);
  }

  // Add course design if available
  if (teaching.course_design) {
    y = checkNewPage(y, 50);
    y += 10;
    y = addText('Course Design', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(teaching.course_design, 20, y, { fontSize: 10 });
  }

  // Add teaching implementation if available
  if (teaching.teaching_implementation) {
    y = checkNewPage(y, 50);
    y += 10;
    y = addText('Teaching Implementation', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(teaching.teaching_implementation, 20, y, { fontSize: 10 });
  }

  // Add student supervision section
  y = checkNewPage(y, 50);
  y += 15;
  y = addText('Student Supervision, include # supervised (# co-supervised.) (2 YEARS)', 20, y, { fontSize: 12, bold: true });

  // Create supervision table using the improved table function
  y += 10;
  const supervision = data.student_supervision || {};

  const supervisionHeaders = ['Undergrad', 'Masters', 'PhD', 'PDF', 'Visitors'];
  const supervisionRows = [
    [
      `${supervision.undergrad_supervised || 0} (${supervision.undergrad_cosupervised || 0})`,
      `${supervision.masters_supervised || 0} (${supervision.masters_cosupervised || 0})`,
      `${supervision.phd_supervised || 0} (${supervision.phd_cosupervised || 0})`,
      `${supervision.pdf_supervised || 0} (${supervision.pdf_cosupervised || 0})`,
      `${supervision.visitors_supervised || 0} (${supervision.visitors_cosupervised || 0})`
    ]
  ];

  y = addTable(supervisionHeaders, supervisionRows, 20, y, [34, 34, 34, 34, 34]);

  // Add teaching summary if available
  if (teaching.accomplishments || teaching.goals) {
    y = checkNewPage(y, 50);
    y += 15;
    y = addText('Summary of teaching accomplishments', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(teaching.accomplishments, 20, y, { fontSize: 10 });

    y += 10;
    y = addText('Goals/next steps as an educator', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(teaching.goals, 20, y, { fontSize: 10 });
  }

  // Add research section
  y = checkNewPage(y, 50);
  y += 15;
  y = addText('SCHOLARSHIP AND RESEARCH (3 YEARS)', 20, y, { fontSize: 14, bold: true });

  // Add research publications if available
  if (data.research_publications && data.research_publications.length > 0) {
    y += 10;
    y = addText('Contributions accepted:', 20, y, { fontSize: 12, bold: true });

    // Group publications by type
    const publicationsByType: Record<string, any[]> = {};
    data.research_publications.forEach(pub => {
      if (!publicationsByType[pub.publication_type]) {
        publicationsByType[pub.publication_type] = [];
      }
      publicationsByType[pub.publication_type].push(pub);
    });

    // Add each publication type
    Object.entries(publicationsByType).forEach(([type, pubs]) => {
      y = checkNewPage(y, 30);
      y += 10;
      y = addText(type + ':', 20, y, { fontSize: 11, bold: true });

      pubs.forEach(pub => {
        y += 5;
        const pubText = `${pub.authors}. "${pub.title}". ${pub.venue}. ${pub.year}.`;
        y = addText(pubText, 25, y, { fontSize: 10, maxWidth: pageWidth - 50 });
      });
    });
  }

  // Add research grants if available
  if (data.research_grants && data.research_grants.length > 0) {
    y = checkNewPage(y, 50);
    y += 15;
    y = addText('Research grants and contracts:', 20, y, { fontSize: 12, bold: true });

    y += 10;
    const grantHeaders = ['PI (Collaborators)', 'Title & Agency', 'Amount', 'Year', 'Share %'];
    const grantRows = data.research_grants.map(grant => [
      grant.pi_name + (grant.collaborators ? ` (${grant.collaborators})` : ''),
      `${grant.title}, ${grant.agency}`,
      grant.amount?.toString() || '',
      grant.installment_year?.toString() || '',
      grant.share_percentage?.toString() || ''
    ]);

    y = addTable(grantHeaders, grantRows, 20, y, [40, 60, 30, 20, 20]);
  }

  // Add research summary if available
  const research = data.research || {};
  if (research.accomplishments || research.goals) {
    y = checkNewPage(y, 50);
    y += 15;
    y = addText('Summary of scholarship/research accomplishments:', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(research.accomplishments, 20, y, { fontSize: 10 });

    y += 10;
    y = addText('Goals/next steps as a scholar/researcher:', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(research.goals, 20, y, { fontSize: 10 });
  }

  // Add service section
  y = checkNewPage(y, 50);
  y += 15;
  y = addText('SERVICE (3 YEARS):', 20, y, { fontSize: 14, bold: true });

  // Add service activities if available
  const service = data.service || {};

  if (service.departmental) {
    y += 10;
    y = addText('Departmental committees served on (and other activities):', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(service.departmental, 20, y, { fontSize: 10 });
  }

  if (service.faculty) {
    y = checkNewPage(y, 30);
    y += 10;
    y = addText('Faculty committees served on (and other activities):', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(service.faculty, 20, y, { fontSize: 10 });
  }

  if (service.university) {
    y = checkNewPage(y, 30);
    y += 10;
    y = addText('University committees served on (and other activities):', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(service.university, 20, y, { fontSize: 10 });
  }

  // Add professional registrations if available
  if (data.professional_registrations && data.professional_registrations.length > 0) {
    y = checkNewPage(y, 50);
    y += 15;
    y = addText('Professional Registration and Professional Training:', 20, y, { fontSize: 12, bold: true });

    y += 10;
    const regHeaders = ['Designation', 'Year', 'Province', 'Requirements'];
    const regRows = data.professional_registrations.map(reg => [
      reg.designation || '',
      reg.registration_year?.toString() || '',
      reg.province || '',
      reg.requirements || ''
    ]);

    y = addTable(regHeaders, regRows, 20, y, [30, 20, 30, 80]);
  }

  // Add service summary if available
  if (service.accomplishments || service.goals) {
    y = checkNewPage(y, 50);
    y += 15;
    y = addText('Summary of service accomplishments:', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(service.accomplishments, 20, y, { fontSize: 10 });

    y += 10;
    y = addText('Goals/next steps as a service contributor:', 20, y, { fontSize: 12, bold: true });
    y += 5;
    y = addText(service.goals, 20, y, { fontSize: 10 });
  }

  // Add awards section if available
  if (data.awards && data.awards.length > 0) {
    y = checkNewPage(y, 50);
    y += 15;
    y = addText('AWARDS/HONOURS (INTERNAL AND EXTERNAL)', 20, y, { fontSize: 14, bold: true });

    y += 10;
    const awardHeaders = ['Award Name', 'Type', 'Awarding Body', 'Year', 'Description'];
    const awardRows = data.awards.map(award => [
      award.award_name || '',
      award.award_type || '',
      award.awarding_body || '',
      award.year?.toString() || '',
      award.description || ''
    ]);

    y = addTable(awardHeaders, awardRows, 20, y, [40, 30, 40, 20, 40]);
  }

  // Add additional comments if available
  if (data.additionalComments?.comments) {
    y = checkNewPage(y, 30);
    y += 15;
    y = addText('ADDITIONAL COMMENTS', 20, y, { fontSize: 14, bold: true });
    y += 5;
    y = addText(data.additionalComments.comments, 20, y, { fontSize: 10 });
  }

  // Add page numbers to all pages
  addPageNumbers();

  // Add a styled footer to the first page
  doc.setPage(1);
  const pageHeight = doc.internal.pageSize.getHeight();
  doc.setFillColor(240, 240, 240);
  doc.rect(0, pageHeight - 25, pageWidth, 25, 'F');

  doc.setFontSize(10);
  doc.setFont('helvetica', 'italic');
  doc.setTextColor(80, 80, 80);
  doc.text('This document was automatically generated from the Merit Review submission data.', 20, pageHeight - 15);
  doc.text('Generated on: ' + new Date().toLocaleDateString(), 20, pageHeight - 10);

  // Convert the PDF to a Blob
  const pdfBlob = doc.output('blob');
  return pdfBlob;
}

/**
 * Generate and download a PDF file from merit review data
 *
 * @param data The merit review data to include in the PDF
 * @param filename The name of the file to download (default: merit-review-report.pdf)
 */
export function downloadMeritReviewPDF(data: MeritReportData, filename: string = 'merit-review-report.pdf'): void {
  const pdfBlob = generateMeritReviewPDF(data);
  const url = URL.createObjectURL(pdfBlob);

  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();

  // Clean up
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 100);
}
