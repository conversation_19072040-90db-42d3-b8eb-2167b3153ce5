import postgres from "postgres";

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */

// Define the global type for TypeScript
declare global {
  var sql: ReturnType<typeof postgres> | undefined;
}

let sql: ReturnType<typeof postgres>;

if (!process.env.POSTGRES_URL_new) {
  throw new Error('Missing POSTGRES_URL_new environment variable');
}

if (process.env.NODE_ENV === 'production') {
  // In production, use a regular connection
  sql = postgres(process.env.POSTGRES_URL_new, {
    max: 20, // Max connections in the pool
    idle_timeout: 30, // Close idle connections after 30s
    ssl: 'require',
    connect_timeout: 10, // 10 seconds to connect
  });
} else {
  // In development, use a global variable to avoid multiple connections
  if (!global.sql) {
    global.sql = postgres(process.env.POSTGRES_URL_new, {
      max: 10, // Fewer connections for development
      idle_timeout: 30,
      ssl: 'require',
      connect_timeout: 10,
    });
  }
  sql = global.sql;
}

// Ensure the connection is closed on process exit
process.on("SIGINT", async () => {
  await sql.end();
  process.exit(0);
});

export { sql };
