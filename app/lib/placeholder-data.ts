
const publications = [
  {
    id: '1a2b3c4d-5678-90ab-cdef-1234567890ab',
    authors: '<PERSON>, <PERSON>, <PERSON><PERSON>',
    emails: '<EMAIL>, alex.kriz<PERSON><PERSON>@cs.toronto.edu, <EMAIL>',
    title: 'ImageNet Classification with Deep Convolutional Neural Networks',
    abstract: 'We trained a large, deep convolutional neural network to classify the 1.2 million high-resolution images in the ImageNet LSVRC-2010 contest into the 1000 different classes.',
    url: 'https://papers.nips.cc/paper/4824-imagenet-classification-with-deep-convolutional-neural-networks.pdf',
    venue: 'NeurIPS',
    year: 2012,
    status: 'published',
    created_at: '2025-03-11T00:00:00Z',
  },
  {
    id: '2b3c4d5e-6789-01bc-defg-2345678901bc',
    authors: '<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>',
    emails: '<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>',
    title: 'Improving neural networks by preventing co-adaptation of feature detectors',
    abstract: 'We show that a recently-introduced regularization method, called "dropout", improves the performance of neural networks on supervised learning tasks in vision, speech recognition, document classification, and computational biology.',
    url: 'https://arxiv.org/abs/1207.0580',
    venue: 'ArXiv',
    year: 2012,
    status: 'published',
    created_at: '2025-03-11T00:00:00Z',
  },
  {
    id: '3c4d5e6f-7890-12cd-efgh-3456789012cd',
    authors: 'Geoffrey Hinton, Simon Osindero, Yee-Whye Teh',
    emails: '<EMAIL>, <EMAIL>, <EMAIL>',
    title: 'A fast learning algorithm for deep belief nets',
    abstract: 'We show how to use "complementary priors" to eliminate the explaining-away effects that make inference difficult in densely connected belief nets that have many hidden layers.',
    url: 'https://www.cs.toronto.edu/~hinton/absps/ncfast.pdf',
    venue: 'Neural Computation',
    year: 2006,
    status: 'published',
    created_at: '2025-03-11T00:00:00Z',
  },
  {
    id: '4d5e6f7g-8901-23de-fghi-4567890123de',
    authors: 'Geoffrey Hinton, Richard Zemel',
    emails: '<EMAIL>, <EMAIL>',
    title: 'Autoencoders, minimum description length and Helmholtz free energy',
    abstract: 'We show how to use the minimum description length principle to derive a new type of autoencoder that learns to represent data efficiently.',
    url: 'https://www.cs.toronto.edu/~hinton/absps/autoencoders.pdf',
    venue: 'Neural Computation',
    year: 1994,
    status: 'published',
    created_at: '2025-03-11T00:00:00Z',
  },
];

export { publications };
