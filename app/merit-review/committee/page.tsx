"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";

interface CommitteeMember {
  id: number;
  unit_id: number;
  faculty_id: number;
  faculty_name: string;
  unit_name: string;
}

interface Unit {
  unit_id: number;
  full_name: string;
}

interface FacultyMember {
  id: number;
  name: string;
}

export default function CommitteeManagementPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [committeeMembers, setCommitteeMembers] = useState<CommitteeMember[]>([]);
  const [facultyMembers, setFacultyMembers] = useState<FacultyMember[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [selectedUnit, setSelectedUnit] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [selectedFaculty, setSelectedFaculty] = useState("");
  const [userUnitId, setUserUnitId] = useState<number | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (authStatus !== "authenticated") return;

      setLoading(true);

      try {
        // Get the current user's faculty data to determine their unit
        const facultyResponse = await fetch("/api/faculty/current");
        if (!facultyResponse.ok) {
          throw new Error("Failed to fetch faculty data");
        }
        const facultyData = await facultyResponse.json();
        setUserUnitId(facultyData.primary_unit_id);

        // Fetch faculty members
        const facultyListResponse = await fetch("/api/faculty");
        if (!facultyListResponse.ok) {
          throw new Error("Failed to fetch faculty members");
        }
        const facultyListData = await facultyListResponse.json();
        setFacultyMembers(facultyListData);

        // Fetch committee members
        const committeeResponse = await fetch("/api/merit-review/committee");
        if (!committeeResponse.ok) {
          throw new Error("Failed to fetch committee members");
        }
        const committeeData = await committeeResponse.json();
        setCommitteeMembers(committeeData);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to fetch data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [authStatus]);

  const handleAddMember = async () => {
    if (!selectedFaculty || !userUnitId) {
      toast.error("Please select a faculty member");
      return;
    }

    setIsAddingMember(true);

    try {
      const response = await fetch("/api/merit-review/committee", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          unit_id: userUnitId,
          faculty_id: parseInt(selectedFaculty),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to add committee member");
      }

      toast.success("Committee member added successfully");
      // Refresh the committee members list
      const committeeResponse = await fetch("/api/merit-review/committee");
      if (committeeResponse.ok) {
        const committeeData = await committeeResponse.json();
        setCommitteeMembers(committeeData);
      }
    } catch (error) {
      console.error("Error adding committee member:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add committee member");
    } finally {
      setIsAddingMember(false);
      setSelectedFaculty("");
    }
  };

  const handleRemoveMember = async (id: number) => {
    try {
      const response = await fetch(`/api/merit-review/committee?id=${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to remove committee member");
      }

      toast.success("Committee member removed successfully");
      // Refresh the committee members list
      const committeeResponse = await fetch("/api/merit-review/committee");
      if (committeeResponse.ok) {
        const committeeData = await committeeResponse.json();
        setCommitteeMembers(committeeData);
      }
    } catch (error) {
      console.error("Error removing committee member:", error);
      toast.error(error instanceof Error ? error.message : "Failed to remove committee member");
    }
  };

  const filteredMembers = committeeMembers.filter((member) =>
    member.faculty_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.unit_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!session) {
    return <div>Please sign in to access this page.</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Committee Management</h1>
        <Button onClick={() => router.push("/merit-review")}>
          Back to Merit Review
        </Button>
      </div>

      <div className="flex gap-4 mb-6">
        <Select
          value={selectedUnit}
          onValueChange={setSelectedUnit}
        >
          <SelectTrigger className="w-[300px]">
            <SelectValue placeholder="Select a unit" />
          </SelectTrigger>
          <SelectContent>
            {units.map((unit) => (
              <SelectItem key={unit.unit_id} value={unit.unit_id.toString()}>
                {unit.full_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Input
          placeholder="Search by name or unit..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-[300px]"
        />

        <Button
          onClick={handleAddMember}
          disabled={isAddingMember}
        >
          {isAddingMember ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="mr-2 h-4 w-4" />
              Add Member
            </>
          )}
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Unit</TableHead>
              <TableHead>Faculty Member</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMembers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="text-center">
                  No committee members found
                </TableCell>
              </TableRow>
            ) : (
              filteredMembers.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>{member.unit_name}</TableCell>
                  <TableCell>{member.faculty_name}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveMember(member.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      )}
    </div>
  );
} 