import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';
import { sql } from '@/app/lib/db';
import { formatTerm } from '@/app/lib/utils/term';

// Get course evaluations for the current faculty user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if facultySsoId is available in the session
    if (!session.user.facultySsoId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    // Get course evaluations for the faculty member
    let query = sql`
      SELECT
        ce.term_id,
        ce.course_id,
        ce.course_title,
        ce.userid,
        ce.department,
        ce.section,
        ce.q1_responses,
        ce.q1_avg,
        ce.q1_std,
        ce.q2_responses,
        ce.q2_avg,
        ce.q2_std,
        ce.q3_responses,
        ce.q3_avg,
        ce.q3_std,
        ce.q4_responses,
        ce.q4_avg,
        ce.q4_std,
        ce.q5_responses,
        ce.q5_avg,
        ce.q5_std,
        ce.q6_responses,
        ce.q6_avg,
        ce.q6_std,
        ce.class_size
      FROM perceptions.course_evaluations ce
      WHERE LOWER(ce.userid) = ${session.user.facultySsoId}
      ORDER BY ce.term_id DESC
    `;

    if (limit) {
      query = sql`${query} LIMIT ${limit}`;
    }

    const result = await query;

    // Get course titles from uw.course_info table
    const courseTitles = await Promise.all(
      result.map(async (course) => {
        // Remove whitespace between course prefix and number (e.g., "ME 436" -> "ME436")
        const courseId = course.course_id.replace(/\s+/g, '');

        // Query the course title from uw.course_info
        const titleResult = await sql`
          SELECT course_title
          FROM uw.course_info
          WHERE course_section_id LIKE ${`%${courseId}%`}
          LIMIT 1
        `;

        return {
          course_id: course.course_id,
          course_title: titleResult.length > 0 ? titleResult[0].course_title : null
        };
      })
    );

    // Create a map of course_id to course_title for quick lookup
    const courseTitleMap = courseTitles.reduce<Record<string, string | null>>((map, item) => {
      map[item.course_id] = item.course_title;
      return map;
    }, {});

    // Process the results to calculate average scores for Q1-Q3 and Q4-Q6
    const processedResults = result.map(course => {
      // Calculate average for Q1-Q3 (teaching effectiveness) - requirement #4
      const q1q3Avg = ((course.q1_avg || 0) + (course.q2_avg || 0) + (course.q3_avg || 0)) / 3;

      // Calculate average for Q4-Q6 (course quality) - requirement #5
      const q4q6Avg = ((course.q4_avg || 0) + (course.q5_avg || 0) + (course.q6_avg || 0)) / 3;

      // Calculate standard deviation for Q1-Q3 - requirement #8
      const q1q3StdDev = ((course.q1_std || 0) + (course.q2_std || 0) + (course.q3_std || 0)) / 3;

      // Calculate standard deviation for Q4-Q6 - requirement #9
      const q4q6StdDev = ((course.q4_std || 0) + (course.q5_std || 0) + (course.q6_std || 0)) / 3;

      // Get the course title from our map, or use the existing one if available
      const courseTitle = courseTitleMap[course.course_id] || course.course_title;

      // Format term_id to a readable term (e.g., 1231 -> Winter 2023) - requirement #2
      // Use the utility function to format the term
      const termName = formatTerm(course.term_id);

      // Calculate response percentage - requirement #7
      // Average of all response counts divided by class size, multiplied by 100
      // Calculate average of q1_responses through q6_responses
      const avgResponses = (
        (parseInt(course.q1_responses) || 0) +
        (parseInt(course.q2_responses) || 0) +
        (parseInt(course.q3_responses) || 0) +
        (parseInt(course.q4_responses) || 0) +
        (parseInt(course.q5_responses) || 0) +
        (parseInt(course.q6_responses) || 0)
      ) / 6; // Average of all responses

      // Calculate response percentage: average_responses / class_size * 100
      const responsePercentage = course.class_size > 0
        ? (avgResponses / course.class_size) * 100
        : 0;

      // Round to whole number without decimal points
      const normalizedResponsePercentage = Math.round(responsePercentage);

      return {
        ...course,
        term_year: termName,
        course_title: courseTitle, // Use the looked-up course title
        scp_q1_q3: q1q3Avg.toFixed(2),
        scp_q1_q3_std_dev: q1q3StdDev.toFixed(2),
        scp_q4_q6: q4q6Avg.toFixed(2),
        scp_q4_q6_std_dev: q4q6StdDev.toFixed(2),
        response_percentage: normalizedResponsePercentage.toString()
      };
    });

    console.log('Returning course evaluations data:', JSON.stringify(processedResults));
    return NextResponse.json(processedResults);
  } catch (error) {
    console.error('Error fetching faculty course evaluations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch course evaluations' },
      { status: 500 }
    );
  }
}
