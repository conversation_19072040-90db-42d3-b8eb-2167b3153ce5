import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user's unit ID and role
    const [userUnit] = await sql`
      SELECT 
        u.unit_id,
        u.parent_id,
        f.role
      FROM uw.unit u
      JOIN uw.faculty f ON f.primary_unit_id = u.unit_id
      WHERE f.faculty_id = ${session.user.id}
    `;

    if (!userUnit) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // For department_admin, only get submissions from their specific unit
    let submissions;
    if (userUnit.role === 'department_admin') {
      submissions = await sql`
        SELECT 
          mr.id,
          mr.faculty_id,
          f.full_name as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.report_doc,
          mr.create_dt,
          mr.update_dt,
          mr.status,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_ratings mrr
            WHERE mrr.merit_report_id = mr.id
          ) as rating_count
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        WHERE mr.unit_id = ${userUnit.unit_id}
        AND mr.is_deleted = FALSE
        ORDER BY mr.create_dt DESC
      `;
    } else {
      // For other roles (system_admin, faculty_admin), get submissions from their unit and child units
      submissions = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          SELECT unit_id, parent_id
          FROM uw.unit
          WHERE unit_id = ${userUnit.unit_id}
          UNION ALL
          SELECT u.unit_id, u.parent_id
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_id = uh.unit_id
        )
        SELECT 
          mr.id,
          mr.faculty_id,
          f.full_name as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.report_doc,
          mr.create_dt,
          mr.update_dt,
          mr.status,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_ratings mrr
            WHERE mrr.merit_report_id = mr.id
          ) as rating_count
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        JOIN unit_hierarchy uh ON mr.unit_id = uh.unit_id
        WHERE mr.is_deleted = FALSE
        ORDER BY mr.create_dt DESC
      `;
    }

    return NextResponse.json(submissions);
  } catch (error) {
    console.error("Error fetching submissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch submissions" },
      { status: 500 }
    );
  }
} 