import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user's unit ID and role
    const [userUnit] = await sql`
      SELECT 
        u.unit_id,
        u.parent_id,
        f.role
      FROM uw.unit u
      JOIN uw.faculty f ON f.primary_unit_id = u.unit_id
      WHERE f.faculty_id = ${session.user.id}
    `;

    if (!userUnit) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // For department_admin, only get reports from their specific unit
    let reports;
    if (userUnit.role === 'department_admin') {
      reports = await sql`
        SELECT 
          mrr.id,
          mrr.merit_report_id,
          mr.faculty_id,
          f.full_name as faculty_name,
          mrr.report_type,
          mrr.report_doc,
          mrr.create_dt,
          mrr.update_dt,
          mrr.status
        FROM uw.merit_review_reports mrr
        JOIN uw.merit_report mr ON mrr.merit_report_id = mr.id
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        WHERE mr.unit_id = ${userUnit.unit_id}
        AND mrr.is_deleted = FALSE
        ORDER BY mrr.create_dt DESC
      `;
    } else {
      // For other roles (system_admin, faculty_admin), get reports from their unit and child units
      reports = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          SELECT unit_id, parent_id
          FROM uw.unit
          WHERE unit_id = ${userUnit.unit_id}
          UNION ALL
          SELECT u.unit_id, u.parent_id
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_id = uh.unit_id
        )
        SELECT 
          mrr.id,
          mrr.merit_report_id,
          mr.faculty_id,
          f.full_name as faculty_name,
          mrr.report_type,
          mrr.report_doc,
          mrr.create_dt,
          mrr.update_dt,
          mrr.status
        FROM uw.merit_review_reports mrr
        JOIN uw.merit_report mr ON mrr.merit_report_id = mr.id
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN unit_hierarchy uh ON mr.unit_id = uh.unit_id
        WHERE mrr.is_deleted = FALSE
        ORDER BY mrr.create_dt DESC
      `;
    }

    return NextResponse.json(reports);
  } catch (error) {
    console.error("Error fetching reports:", error);
    return NextResponse.json(
      { error: "Failed to fetch reports" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { merit_report_id, report_type, report_doc } = body;

    if (!merit_report_id || !report_type || !report_doc) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the user's unit ID and role
    const [userUnit] = await sql`
      SELECT 
        u.unit_id,
        u.parent_id,
        f.role
      FROM uw.unit u
      JOIN uw.faculty f ON f.primary_unit_id = u.unit_id
      WHERE f.faculty_id = ${session.user.id}
    `;

    if (!userUnit) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify that the merit report belongs to the user's unit
    const [meritReport] = await sql`
      SELECT id, unit_id
      FROM uw.merit_report
      WHERE id = ${merit_report_id}
      AND is_deleted = FALSE
    `;

    if (!meritReport) {
      return NextResponse.json(
        { error: "Merit report not found" },
        { status: 404 }
      );
    }

    // For department_admin, verify the report belongs to their specific unit
    if (userUnit.role === 'department_admin' && meritReport.unit_id !== userUnit.unit_id) {
      return NextResponse.json(
        { error: "Unauthorized to create report for this merit report" },
        { status: 403 }
      );
    }

    // For other roles, verify the report belongs to their unit hierarchy
    if (userUnit.role !== 'department_admin') {
      const [validUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          SELECT unit_id, parent_id
          FROM uw.unit
          WHERE unit_id = ${userUnit.unit_id}
          UNION ALL
          SELECT u.unit_id, u.parent_id
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_id = uh.unit_id
        )
        SELECT 1
        FROM unit_hierarchy
        WHERE unit_id = ${meritReport.unit_id}
      `;

      if (!validUnit) {
        return NextResponse.json(
          { error: "Unauthorized to create report for this merit report" },
          { status: 403 }
        );
      }
    }

    // Create the report
    const [newReport] = await sql`
      INSERT INTO uw.merit_review_reports (
        merit_report_id,
        report_type,
        report_doc,
        status,
        create_dt,
        update_dt
      )
      VALUES (
        ${merit_report_id},
        ${report_type},
        ${report_doc},
        'pending',
        NOW(),
        NOW()
      )
      RETURNING *
    `;

    return NextResponse.json(newReport);
  } catch (error) {
    console.error("Error creating report:", error);
    return NextResponse.json(
      { error: "Failed to create report" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Get the user's unit ID and role
    const [userUnit] = await sql`
      SELECT 
        u.unit_id,
        u.parent_id,
        f.role
      FROM uw.unit u
      JOIN uw.faculty f ON f.primary_unit_id = u.unit_id
      WHERE f.faculty_id = ${session.user.id}
    `;

    if (!userUnit) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get the report and its associated merit report
    const [report] = await sql`
      SELECT 
        mrr.id,
        mr.unit_id
      FROM uw.merit_review_reports mrr
      JOIN uw.merit_report mr ON mrr.merit_report_id = mr.id
      WHERE mrr.id = ${reportId}
      AND mrr.is_deleted = FALSE
    `;

    if (!report) {
      return NextResponse.json(
        { error: "Report not found" },
        { status: 404 }
      );
    }

    // For department_admin, verify the report belongs to their specific unit
    if (userUnit.role === 'department_admin' && report.unit_id !== userUnit.unit_id) {
      return NextResponse.json(
        { error: "Unauthorized to delete this report" },
        { status: 403 }
      );
    }

    // For other roles, verify the report belongs to their unit hierarchy
    if (userUnit.role !== 'department_admin') {
      const [validUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          SELECT unit_id, parent_id
          FROM uw.unit
          WHERE unit_id = ${userUnit.unit_id}
          UNION ALL
          SELECT u.unit_id, u.parent_id
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_id = uh.unit_id
        )
        SELECT 1
        FROM unit_hierarchy
        WHERE unit_id = ${report.unit_id}
      `;

      if (!validUnit) {
        return NextResponse.json(
          { error: "Unauthorized to delete this report" },
          { status: 403 }
        );
      }
    }

    // Soft delete the report
    await sql`
      UPDATE uw.merit_review_reports
      SET is_deleted = TRUE,
          update_dt = NOW()
      WHERE id = ${reportId}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting report:", error);
    return NextResponse.json(
      { error: "Failed to delete report" },
      { status: 500 }
    );
  }
} 