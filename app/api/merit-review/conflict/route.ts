import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get conflicts of interest
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const committeeId = searchParams.get("committee_member_id");
    const facultyId = searchParams.get("faculty_id");

    let query = sql`
      SELECT mcoi.id, mcoi.committee_member_id, mcoi.faculty_id, mcoi.reason, mcoi.created_at,
             cm.first_name as committee_first_name, cm.last_name as committee_last_name,
             f.first_name as faculty_first_name, f.last_name as faculty_last_name
      FROM merit_conflict_of_interest mcoi
      JOIN faculty cm ON mcoi.committee_member_id = cm.faculty_id
      JOIN faculty f ON mcoi.faculty_id = f.faculty_id
      WHERE mcoi.is_deleted = FALSE
    `;

    if (committeeId) {
      query = sql`${query} AND mcoi.committee_member_id = ${parseInt(committeeId)}`;
    }

    if (facultyId) {
      query = sql`${query} AND mcoi.faculty_id = ${parseInt(facultyId)}`;
    }

    query = sql`${query} ORDER BY mcoi.created_at DESC`;

    const conflicts = await query;

    return NextResponse.json(conflicts);
  } catch (error) {
    console.error("Error fetching conflicts of interest:", error);
    return NextResponse.json(
      { error: "Failed to fetch conflicts of interest" },
      { status: 500 }
    );
  }
}

// Add a conflict of interest
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { committee_member_id, faculty_id, reason } = await request.json();

    if (!committee_member_id || !faculty_id || !reason) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if committee member exists
    const [committeeMember] = await sql`
      SELECT faculty_id FROM faculty
      WHERE faculty_id = ${committee_member_id}
      AND is_deleted = FALSE
    `;

    if (!committeeMember) {
      return NextResponse.json(
        { error: "Committee member not found" },
        { status: 404 }
      );
    }

    // Check if faculty member exists
    const [faculty] = await sql`
      SELECT faculty_id FROM faculty
      WHERE faculty_id = ${faculty_id}
      AND is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json(
        { error: "Faculty member not found" },
        { status: 404 }
      );
    }

    // Check if conflict already exists
    const [existingConflict] = await sql`
      SELECT id FROM merit_conflict_of_interest
      WHERE committee_member_id = ${committee_member_id}
      AND faculty_id = ${faculty_id}
      AND is_deleted = FALSE
    `;

    if (existingConflict) {
      return NextResponse.json(
        { error: "Conflict of interest already exists" },
        { status: 409 }
      );
    }

    // Add the conflict of interest
    const [newConflict] = await sql`
      INSERT INTO merit_conflict_of_interest (
        committee_member_id, faculty_id, reason
      ) VALUES (
        ${committee_member_id}, ${faculty_id}, ${reason}
      )
      RETURNING id, committee_member_id, faculty_id, reason, created_at
    `;

    // Get the full details of the new conflict
    const [conflictDetails] = await sql`
      SELECT mcoi.id, mcoi.committee_member_id, mcoi.faculty_id, mcoi.reason, mcoi.created_at,
             cm.first_name as committee_first_name, cm.last_name as committee_last_name,
             f.first_name as faculty_first_name, f.last_name as faculty_last_name
      FROM merit_conflict_of_interest mcoi
      JOIN faculty cm ON mcoi.committee_member_id = cm.faculty_id
      JOIN faculty f ON mcoi.faculty_id = f.faculty_id
      WHERE mcoi.id = ${newConflict.id}
    `;

    return NextResponse.json(conflictDetails, { status: 201 });
  } catch (error) {
    console.error("Error adding conflict of interest:", error);
    return NextResponse.json(
      { error: "Failed to add conflict of interest" },
      { status: 500 }
    );
  }
}
