import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get a specific conflict of interest
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    const [conflict] = await sql`
      SELECT mcoi.id, mcoi.committee_member_id, mcoi.faculty_id, mcoi.reason, mcoi.created_at,
             cm.first_name as committee_first_name, cm.last_name as committee_last_name,
             f.first_name as faculty_first_name, f.last_name as faculty_last_name
      FROM merit_conflict_of_interest mcoi
      JOIN faculty cm ON mcoi.committee_member_id = cm.faculty_id
      JOIN faculty f ON mcoi.faculty_id = f.faculty_id
      WHERE mcoi.id = ${parseInt(id)}
      AND mcoi.is_deleted = FALSE
    `;

    if (!conflict) {
      return NextResponse.json(
        { error: "Conflict of interest not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(conflict);
  } catch (error) {
    console.error("Error fetching conflict of interest:", error);
    return NextResponse.json(
      { error: "Failed to fetch conflict of interest" },
      { status: 500 }
    );
  }
}

// Update a conflict of interest
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];
    const { reason } = await request.json();

    // Check if conflict exists
    const [existingConflict] = await sql`
      SELECT * FROM merit_conflict_of_interest
      WHERE id = ${parseInt(id)}
      AND is_deleted = FALSE
    `;

    if (!existingConflict) {
      return NextResponse.json(
        { error: "Conflict of interest not found" },
        { status: 404 }
      );
    }

    // Update the conflict
    const [updatedConflict] = await sql`
      UPDATE merit_conflict_of_interest
      SET reason = ${reason}
      WHERE id = ${parseInt(id)}
      RETURNING id, committee_member_id, faculty_id, reason, created_at
    `;

    return NextResponse.json(updatedConflict);
  } catch (error) {
    console.error("Error updating conflict of interest:", error);
    return NextResponse.json(
      { error: "Failed to update conflict of interest" },
      { status: 500 }
    );
  }
}

// Remove a conflict of interest (soft delete)
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // Check if conflict exists
    const [existingConflict] = await sql`
      SELECT * FROM merit_conflict_of_interest
      WHERE id = ${parseInt(id)}
      AND is_deleted = FALSE
    `;

    if (!existingConflict) {
      return NextResponse.json(
        { error: "Conflict of interest not found" },
        { status: 404 }
      );
    }

    // Soft delete the conflict
    await sql`
      UPDATE merit_conflict_of_interest
      SET is_deleted = TRUE
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({
      message: "Conflict of interest removed successfully",
    });
  } catch (error) {
    console.error("Error removing conflict of interest:", error);
    return NextResponse.json(
      { error: "Failed to remove conflict of interest" },
      { status: 500 }
    );
  }
}
