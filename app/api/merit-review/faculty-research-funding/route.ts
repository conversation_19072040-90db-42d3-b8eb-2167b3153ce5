import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';
import { sql } from '@/app/lib/db';

// Get research funding data for the current faculty user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if facultyId is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    // Build the query to get research funding data for the faculty
    let query = sql`
      SELECT
        rf.funding_id,
        rf.award_year,
        rf.project_title,
        rf.researcher,
        rf.sponsor_name,
        rf.sponsor_type,
        rf.total_award,
        rf.faculty_id
      FROM infoed.research_funding rf
      WHERE rf.faculty_id = ${session.user.facultyId}
      ORDER BY rf.award_year DESC, rf.total_award DESC
    `;

    if (limit) {
      query = sql`${query} LIMIT ${limit}`;
    }

    const rawResult = await query;

    // Process the results to ensure they fit within database column constraints
    const result = rawResult.map((item: any) => {
      // Truncate strings to fit within database column limits
      const truncateString = (str: string | null | undefined, maxLength: number) => {
        if (!str) return null;
        return str.substring(0, maxLength);
      };

      return {
        ...item,
        // Ensure sponsor_type fits within VARCHAR(50)
        sponsor_type: truncateString(item.sponsor_type, 50),
        // Other fields are either TEXT or have sufficient length
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching faculty research funding:", error);
    return NextResponse.json(
      { error: "Failed to fetch faculty research funding" },
      { status: 500 }
    );
  }
}
