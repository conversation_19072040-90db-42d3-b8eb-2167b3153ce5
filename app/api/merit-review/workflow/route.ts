import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get all workflows
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userUnitId = searchParams.get("user_unit_id");

    const statusFilter = searchParams.get("status");
    const unitFilter = searchParams.get("unit_id");


    // Check if user has appropriate role
    const roles = session.user.roles || [];
    if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    let query;
    if (roles.includes("system_admin")) {
      // System admin can see all workflows
      query = sql`

        SELECT mwc.*, u.full_name as unit_name, u.level_number,
          true as can_edit
        FROM uw.merit_workflow_config mwc
        JOIN uw.unit u ON mwc.unit_id = u.unit_id
        WHERE mwc.is_deleted = FALSE
        ${statusFilter ? sql`AND mwc.status = ${statusFilter}` : sql``}
        ${unitFilter ? sql`AND mwc.unit_id = ${parseInt(unitFilter)}` : sql``}
        ORDER BY mwc.created_at DESC
      `;
    } else if (roles.includes("faculty_admin")) {
      // For faculty_admin, get workflows for their unit and all child units
      query = sql`
        WITH RECURSIVE 
        -- First find the faculty level unit (level 3) by traversing up the hierarchy
        faculty_unit AS (
          -- Start with the user's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${parseInt(userUnitId || "0")}
          AND is_deleted = FALSE
          
          UNION ALL
          
          -- Keep going up until we find level 3
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN faculty_unit fu ON u.unit_id = fu.parent_unit_id
          WHERE u.is_deleted = FALSE
          AND u.level_number = 3
        ),
        -- Get the level 3 unit
        level3_unit AS (
          SELECT unit_id, parent_unit_id, level_number
          FROM faculty_unit
          WHERE level_number = 3
          LIMIT 1
        ),
        -- Then get all child units
        child_units AS (
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN level3_unit lu ON u.parent_unit_id = lu.unit_id
          WHERE u.is_deleted = FALSE
          
          UNION ALL
          
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN child_units cu ON u.parent_unit_id = cu.unit_id
          WHERE u.is_deleted = FALSE
        ),
        -- Combine level 3 unit and child units
        relevant_units AS (
          SELECT unit_id FROM level3_unit
          UNION
          SELECT unit_id FROM child_units
        )
        SELECT DISTINCT mwc.*, u.full_name as unit_name, u.level_number,
          CASE 
            WHEN mwc.unit_id = ${parseInt(userUnitId || "0")} THEN true
            ELSE false
          END as can_edit
        FROM uw.merit_workflow_config mwc
        JOIN uw.unit u ON mwc.unit_id = u.unit_id
        JOIN relevant_units ru ON mwc.unit_id = ru.unit_id
        WHERE mwc.is_deleted = FALSE
        ${statusFilter ? sql`AND mwc.status = ${statusFilter}` : sql``}
        ${unitFilter ? sql`AND mwc.unit_id = ${parseInt(unitFilter)}` : sql``}
        ORDER BY mwc.created_at DESC
      `;
    } else if (roles.includes("department_admin")) {
      // For department_admin, get workflows for their unit and parent units
      query = sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the user's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${parseInt(userUnitId || "0")}
          AND is_deleted = FALSE
          
          UNION ALL
          
          -- Get all parent units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
          WHERE u.is_deleted = FALSE
        )

        SELECT DISTINCT mwc.*, u.full_name as unit_name, u.level_number,

          CASE 
            WHEN mwc.unit_id = ${parseInt(userUnitId || "0")} THEN true
            ELSE false
          END as can_edit
        FROM uw.merit_workflow_config mwc
        JOIN uw.unit u ON mwc.unit_id = u.unit_id
        JOIN unit_hierarchy uh ON mwc.unit_id = uh.unit_id
        WHERE mwc.is_deleted = FALSE

        ${statusFilter ? sql`AND mwc.status = ${statusFilter}` : sql``}
        ${unitFilter ? sql`AND mwc.unit_id = ${parseInt(unitFilter)}` : sql``}

        ORDER BY mwc.created_at DESC
      `;
    }

    const workflows = await query;
    return NextResponse.json(workflows);
  } catch (error) {
    console.error("Error fetching workflows:", error);
    return NextResponse.json(
      { error: "Failed to fetch workflows" },
      { status: 500 }
    );
  }
}

// Create a new workflow
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has faculty_admin or department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { unit_id, start_dt, end_dt, status, description } = await request.json();

    if (!unit_id || !start_dt || !end_dt) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Validate unit level based on role
    const unitQuery = await sql`
      SELECT level_number FROM uw.unit
      WHERE unit_id = ${unit_id}
      AND is_deleted = FALSE
    `;
    
    if (unitQuery.length === 0) {
      return NextResponse.json(
        { error: "Invalid unit" },
        { status: 400 }
      );
    }

    const unitLevel = unitQuery[0].level_number;
    
    if (roles.includes("faculty_admin") && unitLevel !== 3) {
      return NextResponse.json(
        { error: "Faculty admin can only create workflows for faculty level units (level 3)" },
        { status: 403 }
      );
    }

    if (roles.includes("department_admin") && unitLevel !== 4) {
      return NextResponse.json(
        { error: "Department admin can only create workflows for department level units (level 4)" },
        { status: 403 }
      );
    }

    // Validate status
    const validStatuses = ['draft', 'active', 'completed', 'cancelled'];
    const workflowStatus = status && validStatuses.includes(status) ? status : 'draft';

    // If status is active, check if there's already an active workflow for this unit
    if (workflowStatus === 'active') {
      const existingWorkflows = await sql`
        SELECT id FROM uw.merit_workflow_config
        WHERE unit_id = ${unit_id}
        AND status = 'active'
        AND is_deleted = FALSE
      `;

      if (existingWorkflows.length > 0) {
        return NextResponse.json(
          { error: "An active workflow already exists for this unit" },
          { status: 409 }
        );
      }
    }

    // Create the new workflow
    const [newWorkflow] = await sql`
      INSERT INTO uw.merit_workflow_config (
        unit_id, start_dt, end_dt, status, description, created_by
      ) VALUES (
        ${unit_id}, ${start_dt}, ${end_dt}, ${workflowStatus}, ${description || null}, ${session.user.id}
      )
      RETURNING id, unit_id, start_dt, end_dt, status, description, created_at
    `;

    return NextResponse.json(newWorkflow, { status: 201 });
  } catch (error) {
    console.error("Error creating workflow:", error);
    return NextResponse.json(
      { error: "Failed to create workflow" },
      { status: 500 }
    );
  }
}
