import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get a specific workflow
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];
    
    // Validate that id is a valid number
    const workflowId = parseInt(id);
    if (isNaN(workflowId)) {
      return NextResponse.json({ error: "Invalid workflow ID" }, { status: 400 });
    }

    const [workflow] = await sql`
      SELECT mwc.*, u.full_name as unit_name
      FROM uw.merit_workflow_config mwc
      JOIN uw.unit u ON mwc.unit_id = u.unit_id
      WHERE mwc.id = ${workflowId}
      AND mwc.is_deleted = FALSE
    `;

    if (!workflow) {
      return NextResponse.json({ error: "Workflow not found" }, { status: 404 });
    }

    return NextResponse.json(workflow);
  } catch (error) {
    console.error("Error fetching workflow:", error);
    return NextResponse.json(
      { error: "Failed to fetch workflow" },
      { status: 500 }
    );
  }
}

// Update a workflow
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has faculty_admin or department_admin role
    const roles = session.user.roles || [];
    if (
      !roles.includes("faculty_admin") &&
      !roles.includes("department_admin") &&
      !roles.includes("system_admin")
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];
    const { unit_id, start_dt, end_dt, status, description } = await request.json();

    // Check if workflow exists and is in draft status
    const [existingWorkflow] = await sql`
      SELECT * FROM uw.merit_workflow_config
      WHERE id = ${parseInt(id)}
      AND is_deleted = FALSE
    `;

    if (!existingWorkflow) {
      return NextResponse.json({ error: "Workflow not found" }, { status: 404 });
    }

    // Only allow editing workflows in draft status
    if (existingWorkflow.status !== 'draft') {
      return NextResponse.json(
        { error: "Only workflows in draft status can be edited" },
        { status: 403 }
      );
    }

    // Validate status
    const validStatuses = ['draft', 'active', 'completed', 'cancelled'];
    const workflowStatus = status && validStatuses.includes(status) ? status : existingWorkflow.status;

    // If changing to active status, check if there's already an active workflow for this unit
    if (workflowStatus === 'active' && existingWorkflow.status !== 'active') {
      const unitIdToCheck = unit_id ? parseInt(unit_id) : existingWorkflow.unit_id;

      const existingActiveWorkflows = await sql`
        SELECT id FROM uw.merit_workflow_config
        WHERE unit_id = ${unitIdToCheck}
        AND status = 'active'
        AND is_deleted = FALSE
        AND id != ${parseInt(id)}
      `;

      if (existingActiveWorkflows.length > 0) {
        return NextResponse.json(
          { error: "An active workflow already exists for this unit" },
          { status: 409 }
        );
      }
    }

    // Update the workflow
    const [updatedWorkflow] = await sql`
      UPDATE uw.merit_workflow_config
      SET
        unit_id = COALESCE(${unit_id ? parseInt(unit_id) : null}, unit_id),
        start_dt = COALESCE(${start_dt}, start_dt),
        end_dt = COALESCE(${end_dt}, end_dt),
        status = COALESCE(${workflowStatus}, status),
        description = COALESCE(${description}, description),
        updated_at = NOW()
      WHERE id = ${parseInt(id)}
      RETURNING id, unit_id, start_dt, end_dt, status, description, created_at, updated_at
    `;

    return NextResponse.json(updatedWorkflow);
  } catch (error) {
    console.error("Error updating workflow:", error);
    return NextResponse.json(
      { error: "Failed to update workflow" },
      { status: 500 }
    );
  }
}

// Delete a workflow (soft delete)
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has faculty_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("faculty_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // Check if workflow exists
    const [existingWorkflow] = await sql`
      SELECT * FROM uw.merit_workflow_config
      WHERE id = ${parseInt(id)}
      AND is_deleted = FALSE
    `;

    if (!existingWorkflow) {
      return NextResponse.json({ error: "Workflow not found" }, { status: 404 });
    }

    // Soft delete the workflow
    await sql`
      UPDATE uw.merit_workflow_config
      SET is_deleted = TRUE, updated_at = NOW()
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({ message: "Workflow deleted successfully" });
  } catch (error) {
    console.error("Error deleting workflow:", error);
    return NextResponse.json(
      { error: "Failed to delete workflow" },
      { status: 500 }
    );
  }
}
