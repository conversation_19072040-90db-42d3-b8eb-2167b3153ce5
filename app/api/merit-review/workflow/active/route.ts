import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get active workflow for the current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user's primary unit by joining with common.user table
    const [faculty] = await sql`
      SELECT f.primary_unit_id
      FROM uw.faculty f
      JOIN common.user u ON f.work_email = u.email
      WHERE u.user_id = ${session.user.id}
      AND f.is_deleted = FALSE
    `;

    if (!faculty || !faculty.primary_unit_id) {
      return NextResponse.json({ active: null });
    }

    const userUnitId = faculty.primary_unit_id;

    // Find active workflows for the user's unit hierarchy
    // This query finds active workflows in the user's unit and any parent units
    const activeWorkflows = await sql`
      WITH RECURSIVE unit_hierarchy AS (
        -- Start with the user's unit
        SELECT unit_id, parent_unit_id, level_number
        FROM uw.unit
        WHERE unit_id = ${userUnitId}
        AND is_deleted = FALSE

        UNION ALL

        -- Get all parent units
        SELECT u.unit_id, u.parent_unit_id, u.level_number
        FROM uw.unit u
        JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
        WHERE u.is_deleted = FALSE
      )
      SELECT DISTINCT mwc.*, u.full_name as unit_name, u.level_number
      FROM uw.merit_workflow_config mwc
      JOIN uw.unit u ON mwc.unit_id = u.unit_id
      JOIN unit_hierarchy uh ON mwc.unit_id = uh.unit_id
      WHERE mwc.is_deleted = FALSE
      AND mwc.status = 'active'
      ORDER BY mwc.created_at DESC
      LIMIT 1
    `;

    if (activeWorkflows.length > 0) {
      return NextResponse.json({ active: activeWorkflows[0] });
    } else {
      return NextResponse.json({ active: null });
    }
  } catch (error) {
    console.error("Error fetching active workflow:", error);
    return NextResponse.json(
      { error: "Failed to fetch active workflow" },
      { status: 500 }
    );
  }
}
