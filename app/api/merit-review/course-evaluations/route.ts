import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';
import { sql } from '@/app/lib/db';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user email from session
    const userEmail = session.user.email;

    if (!userEmail) {
      return NextResponse.json({ error: 'User email not found' }, { status: 400 });
    }


    // Check if user is system_admin or has faculty record
    const isSystemAdmin = session.user.roles?.includes('system_admin');
    const isFacultyAdmin = session.user.roles?.includes('faculty_admin');
    const isDepartmentAdmin = session.user.roles?.includes('department_admin');
    const isRegularUser = session.user.roles?.includes('regular_user');

    // Determine the highest permission role
    let highestRole = 'regular_user';
    if (isSystemAdmin) {
      highestRole = 'system_admin';
    } else if (isFacultyAdmin) {
      highestRole = 'faculty_admin';
    } else if (isDepartmentAdmin) {
      highestRole = 'department_admin';
    }

    let result;

    if (highestRole === 'system_admin') {
      // For system_admin, get all course evaluations
      result = await sql`
        SELECT
          ce.term_id,
          ce.course_id,
          ce.course_title,
          ce.userid,
          ce.gender,
          ce.first_name,
          ce.last_name,
          ce.faculty as faculty_name,
          ce.department,
          ce.section,
          ce.q1_responses,
          ce.q1_avg,
          ce.q1_std,
          ce.q2_responses,
          ce.q2_avg,
          ce.q2_std,
          ce.q3_responses,
          ce.q3_avg,
          ce.q3_std,
          ce.q4_responses,
          ce.q4_avg,
          ce.q4_std,
          ce.q5_responses,
          ce.q5_avg,
          ce.q5_std,
          ce.q6_responses,
          ce.q6_avg,
          ce.q6_std,
          ce.class_size,
          f.faculty_id,
          f.first_name as faculty_first_name,
          f.last_name as faculty_last_name,
          f.work_email
        FROM perceptions.course_evaluations ce
        LEFT JOIN uw.faculty f ON LOWER(ce.userid) = LOWER(f.sso_id)
        ORDER BY ce.term_id DESC, ce.last_name, ce.first_name
      `;
    } else {
      // For non-system_admin users, first get their faculty record
      const facultyResult = await sql`
        SELECT faculty_id, sso_id, primary_unit_id
        FROM uw.faculty
        WHERE LOWER(work_email) = LOWER(${userEmail})
        AND is_deleted = FALSE
        LIMIT 1
      `;

      if (facultyResult.length === 0) {
        return NextResponse.json({ error: 'No faculty record found' }, { status: 404 });
      }

      const faculty = facultyResult[0];

      if (highestRole === 'regular_user') {
        // For regular users, get only their own course evaluations
        result = await sql`
          SELECT
            ce.term_id,
            ce.course_id,
            ce.course_title,
            ce.userid,
            ce.gender,
            ce.first_name,
            ce.last_name,
            ce.faculty as faculty_name,
            ce.department,
            ce.section,
            ce.q1_responses,
            ce.q1_avg,
            ce.q1_std,
            ce.q2_responses,
            ce.q2_avg,
            ce.q2_std,
            ce.q3_responses,
            ce.q3_avg,
            ce.q3_std,
            ce.q4_responses,
            ce.q4_avg,
            ce.q4_std,
            ce.q5_responses,
            ce.q5_avg,
            ce.q5_std,
            ce.q6_responses,
            ce.q6_avg,
            ce.q6_std,
            ce.class_size,
            f.faculty_id,
            f.first_name as faculty_first_name,
            f.last_name as faculty_last_name,
            f.work_email
          FROM perceptions.course_evaluations ce
          LEFT JOIN uw.faculty f ON LOWER(ce.userid) = LOWER(f.sso_id)
          WHERE LOWER(ce.userid) = LOWER(${faculty.sso_id})
          ORDER BY ce.term_id DESC
        `;
      } else if (highestRole === 'faculty_admin') {
        // For faculty_admin, get course evaluations for their faculty unit and child units
        // First get the faculty level unit (level 3)
        const facultyUnit = await sql`
          WITH RECURSIVE faculty_unit AS (
            -- Start with the user's unit
            SELECT unit_id, parent_unit_id, level_number
            FROM uw.unit
            WHERE unit_id = ${faculty.primary_unit_id}
            AND is_deleted = FALSE
            
            UNION ALL
            
            -- Keep going up until we find level 3
            SELECT u.unit_id, u.parent_unit_id, u.level_number
            FROM uw.unit u
            JOIN faculty_unit fu ON u.unit_id = fu.parent_unit_id
            WHERE u.is_deleted = FALSE
            AND u.level_number = 3
          )
          SELECT unit_id FROM faculty_unit WHERE level_number = 3 LIMIT 1
        `;

        if (facultyUnit.length === 0) {
          return NextResponse.json({ error: 'Faculty unit not found' }, { status: 404 });
        }

        const facultyUnitId = facultyUnit[0].unit_id;

        // Get all child units of the faculty unit
        const childUnits = await sql`
          WITH RECURSIVE child_units AS (
            SELECT unit_id
            FROM uw.unit
            WHERE parent_unit_id = ${facultyUnitId}
            AND is_deleted = FALSE
            
            UNION ALL
            
            SELECT u.unit_id
            FROM uw.unit u
            JOIN child_units cu ON u.parent_unit_id = cu.unit_id
            WHERE u.is_deleted = FALSE
          )
          SELECT unit_id FROM child_units
        `;

        const unitIds = [facultyUnitId, ...childUnits.map(u => u.unit_id)];

        // Get course evaluations for instructors in these units
        result = await sql`
          SELECT
            ce.term_id,
            ce.course_id,
            ce.course_title,
            ce.userid,
            ce.gender,
            ce.first_name,
            ce.last_name,
            ce.faculty as faculty_name,
            ce.department,
            ce.section,
            ce.q1_responses,
            ce.q1_avg,
            ce.q1_std,
            ce.q2_responses,
            ce.q2_avg,
            ce.q2_std,
            ce.q3_responses,
            ce.q3_avg,
            ce.q3_std,
            ce.q4_responses,
            ce.q4_avg,
            ce.q4_std,
            ce.q5_responses,
            ce.q5_avg,
            ce.q5_std,
            ce.q6_responses,
            ce.q6_avg,
            ce.q6_std,
            ce.class_size,
            f.faculty_id,
            f.first_name as faculty_first_name,
            f.last_name as faculty_last_name,
            f.work_email
          FROM perceptions.course_evaluations ce
          LEFT JOIN uw.faculty f ON LOWER(ce.userid) = LOWER(f.sso_id)
          WHERE f.primary_unit_id = ANY(${unitIds})
          ORDER BY ce.term_id DESC, ce.last_name, ce.first_name
        `;
      } else if (highestRole === 'department_admin') {
        // For department_admin, get course evaluations for their department unit
        // Get all child units of the department unit
        const childUnits = await sql`
          WITH RECURSIVE child_units AS (
            SELECT unit_id
            FROM uw.unit
            WHERE unit_id = ${faculty.primary_unit_id}
            AND is_deleted = FALSE
            
            UNION ALL
            
            SELECT u.unit_id
            FROM uw.unit u
            JOIN child_units cu ON u.parent_unit_id = cu.unit_id
            WHERE u.is_deleted = FALSE
          )
          SELECT unit_id FROM child_units
        `;

        const unitIds = childUnits.map(u => u.unit_id);

        // Get course evaluations for instructors in these units
        result = await sql`
          SELECT
            ce.term_id,
            ce.course_id,
            ce.course_title,
            ce.userid,
            ce.gender,
            ce.first_name,
            ce.last_name,
            ce.faculty as faculty_name,
            ce.department,
            ce.section,
            ce.q1_responses,
            ce.q1_avg,
            ce.q1_std,
            ce.q2_responses,
            ce.q2_avg,
            ce.q2_std,
            ce.q3_responses,
            ce.q3_avg,
            ce.q3_std,
            ce.q4_responses,
            ce.q4_avg,
            ce.q4_std,
            ce.q5_responses,
            ce.q5_avg,
            ce.q5_std,
            ce.q6_responses,
            ce.q6_avg,
            ce.q6_std,
            ce.class_size,
            f.faculty_id,
            f.first_name as faculty_first_name,
            f.last_name as faculty_last_name,
            f.work_email
          FROM perceptions.course_evaluations ce
          LEFT JOIN uw.faculty f ON LOWER(ce.userid) = LOWER(f.sso_id)
          WHERE f.primary_unit_id = ANY(${unitIds})
          ORDER BY ce.term_id DESC, ce.last_name, ce.first_name
        `;
      } else {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching course evaluations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch course evaluations' },
      { status: 500 }
    );
  }
}
