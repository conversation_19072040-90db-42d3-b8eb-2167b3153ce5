import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { getLevel4UnitIdForFaculty } from "@/app/lib/utils/unit-utils";

// Get all submissions for the current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const submissions = await sql`
      SELECT mr.*, u.full_name as unit_name
      FROM uw.merit_report mr
      JOIN uw.unit u ON mr.unit_id = u.unit_id
      WHERE mr.faculty_id = ${session.user.facultyId}
      AND mr.is_deleted = FALSE
      ORDER BY mr.create_dt DESC
    `;

    return NextResponse.json(submissions);
  } catch (error) {
    console.error("Error fetching submissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch submissions" },
      { status: 500 }
    );
  }
}

// Create a new submission
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { unit_id, report_doc } = await request.json();

    if (!unit_id || !report_doc) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if there's an active workflow for the unit
    const activeWorkflow = await sql`
      SELECT id FROM uw.merit_workflow_config
      WHERE unit_id = ${unit_id}
      AND status = 'active'
      AND is_deleted = FALSE
    `;

    if (activeWorkflow.length === 0) {
      return NextResponse.json(
        { error: "No active workflow found for this unit" },
        { status: 400 }
      );
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Check if user already has a submission for this workflow
    const existingSubmission = await sql`
      SELECT id FROM uw.merit_report
      WHERE faculty_id = ${session.user.facultyId}
      AND unit_id = ${unit_id}
      AND is_deleted = FALSE
    `;

    if (existingSubmission.length > 0) {
      return NextResponse.json(
        { error: "You already have a submission for this workflow" },
        { status: 409 }
      );
    }

    // Get the faculty's level 4 unit ID
    const level4UnitId = await getLevel4UnitIdForFaculty(session.user.facultyId);

    // Use the level 4 unit ID if available, otherwise fall back to the provided unit_id
    const reportUnitId = level4UnitId || unit_id;

    // Create the new submission
    const [newSubmission] = await sql`
      INSERT INTO uw.merit_report (
        faculty_id, unit_id, report_doc, status
      ) VALUES (
        ${session.user.facultyId}, ${reportUnitId}, ${report_doc}, 'in_progress'
      )
      RETURNING id, faculty_id, unit_id, report_doc, create_dt, update_dt, status
    `;

    return NextResponse.json(newSubmission, { status: 201 });
  } catch (error) {
    console.error("Error creating submission:", error);
    return NextResponse.json(
      { error: "Failed to create submission" },
      { status: 500 }
    );
  }
}

// Update a submission
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, report_doc, status } = await request.json();

    if (!id || !report_doc) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Check if submission exists and belongs to the user
    const existingSubmission = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (existingSubmission.length === 0) {
      return NextResponse.json(
        { error: "Submission not found" },
        { status: 404 }
      );
    }

    // Update the submission
    const [updatedSubmission] = await sql`
      UPDATE uw.merit_report
      SET report_doc = ${report_doc},
          status = ${status || 'in_progress'},
          update_dt = NOW()
      WHERE id = ${id}
      RETURNING id, faculty_id, unit_id, report_doc, create_dt, update_dt, status
    `;

    return NextResponse.json(updatedSubmission);
  } catch (error) {
    console.error("Error updating submission:", error);
    return NextResponse.json(
      { error: "Failed to update submission" },
      { status: 500 }
    );
  }
}

// Delete a submission (soft delete)
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Missing submission ID" },
        { status: 400 }
      );
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Check if submission exists and belongs to the user
    const existingSubmission = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(id)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (existingSubmission.length === 0) {
      return NextResponse.json(
        { error: "Submission not found" },
        { status: 404 }
      );
    }

    // Soft delete the submission
    await sql`
      UPDATE uw.merit_report
      SET is_deleted = TRUE
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting submission:", error);
    return NextResponse.json(
      { error: "Failed to delete submission" },
      { status: 500 }
    );
  }
}