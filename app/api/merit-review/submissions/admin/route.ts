import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get all submissions for admin users
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const isAdmin = session.user.roles?.includes("system_admin") ||
                    session.user.roles?.includes("faculty_admin") ||
                    session.user.roles?.includes("department_admin");

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to access this resource" },
        { status: 403 }
      );
    }

    // Get the current user's faculty data to determine their unit
    const [faculty] = await sql`
      SELECT
        f.faculty_id,
        f.primary_unit_id
      FROM uw.faculty f
      JOIN common.user u ON f.work_email = u.email
      WHERE u.user_id = ${session.user.id}
      AND f.is_deleted = FALSE
      LIMIT 1
    `;

    let submissions;

    // System admin can see all submissions
    if (session.user.roles?.includes("system_admin")) {
      submissions = await sql`
        SELECT
          mr.id,
          mr.faculty_id,
          CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.report_type,
          mr.report_year,
          mr.status,
          mr.create_dt,
          mr.update_dt,
          mr.submit_dt,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
          ) as rating_count,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
            AND mrr.is_submitted = TRUE
          ) as submitted_rating_count,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
            AND mrr.is_submitted = FALSE
          ) as in_progress_rating_count
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        WHERE mr.is_deleted = FALSE
        ORDER BY mr.update_dt DESC
      `;
    }
    // Faculty admin can see submissions for their faculty unit and all child units
    else if (session.user.roles?.includes("faculty_admin") && faculty) {
      // First, find the faculty level unit (level 3)
      const [facultyUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the user's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${faculty.primary_unit_id}
          AND is_deleted = FALSE

          UNION ALL

          -- Get all parent units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT unit_id
        FROM unit_hierarchy
        WHERE level_number = 3
        LIMIT 1
      `;

      if (!facultyUnit) {
        return NextResponse.json(
          { error: "Faculty unit not found" },
          { status: 404 }
        );
      }

      // Get all submissions from the faculty unit and its child units
      submissions = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the faculty unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${facultyUnit.unit_id}
          AND is_deleted = FALSE

          UNION ALL

          -- Get all child units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT
          mr.id,
          mr.faculty_id,
          CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.report_type,
          mr.report_year,
          mr.status,
          mr.create_dt,
          mr.update_dt,
          mr.submit_dt,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
          ) as rating_count,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
            AND mrr.is_submitted = TRUE
          ) as submitted_rating_count,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
            AND mrr.is_submitted = FALSE
          ) as in_progress_rating_count
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        JOIN unit_hierarchy uh ON mr.unit_id = uh.unit_id
        WHERE mr.is_deleted = FALSE
        ORDER BY mr.update_dt DESC
      `;
    }
    // Department admin can see submissions for their department unit only
    else if (session.user.roles?.includes("department_admin") && faculty) {
      submissions = await sql`
        SELECT
          mr.id,
          mr.faculty_id,
          CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.report_type,
          mr.report_year,
          mr.status,
          mr.create_dt,
          mr.update_dt,
          mr.submit_dt,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
          ) as rating_count,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
            AND mrr.is_submitted = TRUE
          ) as submitted_rating_count,
          (
            SELECT COUNT(*)
            FROM uw.merit_review_rating mrr
            WHERE mrr.report_id = mr.id
            AND mrr.is_deleted = FALSE
            AND mrr.is_submitted = FALSE
          ) as in_progress_rating_count
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        WHERE mr.unit_id = ${faculty.primary_unit_id}
        AND mr.is_deleted = FALSE
        ORDER BY mr.update_dt DESC
      `;
    } else {
      return NextResponse.json(
        { error: "Invalid admin role or faculty data not found" },
        { status: 403 }
      );
    }

    // Get count of faculty members who haven't created a report yet
    // This is for the "not_started" status count
    let notStartedCount = 0;

    if (session.user.roles?.includes("system_admin")) {
      // For system admin, count all faculty with job_family='Regular Faculty' who don't have a report
      const [result] = await sql`
        SELECT COUNT(*) as count
        FROM uw.faculty f
        LEFT JOIN uw.merit_report mr ON f.faculty_id = mr.faculty_id AND mr.is_deleted = FALSE
        WHERE f.job_family = 'Regular Faculty'
        AND f.is_deleted = FALSE
        AND mr.id IS NULL
      `;
      notStartedCount = parseInt(result.count);
    } else if (session.user.roles?.includes("faculty_admin") && faculty) {
      // For faculty admin, count faculty in their faculty unit and child units
      const [facultyUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${faculty.primary_unit_id}
          AND is_deleted = FALSE

          UNION ALL

          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT unit_id
        FROM unit_hierarchy
        WHERE level_number = 3
        LIMIT 1
      `;

      if (facultyUnit) {
        const [result] = await sql`
          WITH RECURSIVE unit_hierarchy AS (
            SELECT unit_id, parent_unit_id, level_number
            FROM uw.unit
            WHERE unit_id = ${facultyUnit.unit_id}
            AND is_deleted = FALSE

            UNION ALL

            SELECT u.unit_id, u.parent_unit_id, u.level_number
            FROM uw.unit u
            JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
            WHERE u.is_deleted = FALSE
          )
          SELECT COUNT(*) as count
          FROM uw.faculty f
          LEFT JOIN uw.merit_report mr ON f.faculty_id = mr.faculty_id AND mr.is_deleted = FALSE
          WHERE f.job_family = 'Regular Faculty'
          AND f.is_deleted = FALSE
          AND f.primary_unit_id IN (SELECT unit_id FROM unit_hierarchy)
          AND mr.id IS NULL
        `;
        notStartedCount = parseInt(result.count);
      }
    } else if (session.user.roles?.includes("department_admin") && faculty) {
      // For department admin, count faculty in their department unit
      const [result] = await sql`
        SELECT COUNT(*) as count
        FROM uw.faculty f
        LEFT JOIN uw.merit_report mr ON f.faculty_id = mr.faculty_id AND mr.is_deleted = FALSE
        WHERE f.job_family = 'Regular Faculty'
        AND f.is_deleted = FALSE
        AND f.primary_unit_id = ${faculty.primary_unit_id}
        AND mr.id IS NULL
      `;
      notStartedCount = parseInt(result.count);
    }

    // Get total faculty count
    let totalFacultyCount = 0;

    if (session.user.roles?.includes("system_admin")) {
      // For system admin, count all faculty with job_family='Regular Faculty'
      const [result] = await sql`
        SELECT COUNT(*) as count
        FROM uw.faculty f
        WHERE f.job_family = 'Regular Faculty'
        AND f.is_deleted = FALSE
      `;
      totalFacultyCount = parseInt(result.count);
    } else if (session.user.roles?.includes("faculty_admin") && faculty) {
      // For faculty admin, count faculty in their faculty unit and child units
      const [facultyUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${faculty.primary_unit_id}
          AND is_deleted = FALSE

          UNION ALL

          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT unit_id
        FROM unit_hierarchy
        WHERE level_number = 3
        LIMIT 1
      `;

      if (facultyUnit) {
        const [result] = await sql`
          WITH RECURSIVE unit_hierarchy AS (
            SELECT unit_id, parent_unit_id, level_number
            FROM uw.unit
            WHERE unit_id = ${facultyUnit.unit_id}
            AND is_deleted = FALSE

            UNION ALL

            SELECT u.unit_id, u.parent_unit_id, u.level_number
            FROM uw.unit u
            JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
            WHERE u.is_deleted = FALSE
          )
          SELECT COUNT(*) as count
          FROM uw.faculty f
          WHERE f.job_family = 'Regular Faculty'
          AND f.is_deleted = FALSE
          AND f.primary_unit_id IN (SELECT unit_id FROM unit_hierarchy)
        `;
        totalFacultyCount = parseInt(result.count);
      }
    } else if (session.user.roles?.includes("department_admin") && faculty) {
      // For department admin, count faculty in their department unit
      const [result] = await sql`
        SELECT COUNT(*) as count
        FROM uw.faculty f
        WHERE f.job_family = 'Regular Faculty'
        AND f.is_deleted = FALSE
        AND f.primary_unit_id = ${faculty.primary_unit_id}
      `;
      totalFacultyCount = parseInt(result.count);
    }

    // Return submissions with metadata
    return NextResponse.json({
      submissions: submissions,
      metadata: {
        not_started_count: notStartedCount,
        total_faculty_count: totalFacultyCount
      }
    });
  } catch (error) {
    console.error("Error fetching admin submissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch submissions" },
      { status: 500 }
    );
  }
}
