import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

/**
 * GET - Get all ratings for a specific merit review submission
 * 
 * Required query param: report_id
 * 
 * Returns all ratings for the specified submission if the user is authorized
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the report_id from the query parameters
    const { searchParams } = new URL(request.url);
    const report_id = searchParams.get("report_id");

    if (!report_id) {
      return NextResponse.json(
        { error: "Missing report_id parameter" },
        { status: 400 }
      );
    }

    // Check if the merit report exists and if the user has access to it
    const [report] = await sql`
      SELECT mr.id, mr.unit_id, mr.faculty_id, mr.status,
        CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
        u.full_name as unit_name
      FROM uw.merit_report mr
      JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
      JOIN uw.unit u ON mr.unit_id = u.unit_id
      WHERE mr.id = ${report_id}
      AND mr.is_deleted = FALSE
    `;

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 });
    }

    // Get user information and check authorization
    const [userFaculty] = await sql`
      SELECT faculty_id, primary_unit_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    if (!userFaculty) {
      return NextResponse.json({ error: "Faculty not found" }, { status: 404 });
    }

    // Check if user is authorized (either admin, the faculty member who submitted, or a committee member)
    const isAdmin = session.user.roles?.some(role => 
      ['system_admin', 'faculty_admin', 'department_admin'].includes(role)
    );
    
    const isSubmitter = userFaculty.faculty_id === report.faculty_id;
    
    const [isCommitteeMember] = await sql`
      SELECT 1
      FROM uw.merit_review_committee mrc
      WHERE mrc.unit_id = ${report.unit_id}
      AND mrc.faculty_id = ${userFaculty.faculty_id}
      AND mrc.is_deleted = FALSE
    `;

    if (!isAdmin && !isSubmitter && !isCommitteeMember) {
      return NextResponse.json(
        { error: "You are not authorized to view these ratings" },
        { status: 403 }
      );
    }

    // Get all ratings for this report
    const ratings = await sql`
      SELECT 
        mrr.id,
        mrr.report_id,
        mrr.reviewer_id,
        CONCAT(f.first_name, ' ', f.last_name) as reviewer_name,
        mrr.teaching_rating,
        mrr.research_rating,
        mrr.service_rating,
        mrr.comments,
        mrr.is_submitted,
        mrr.created_at,
        mrr.updated_at
      FROM uw.merit_review_rating mrr
      JOIN uw.faculty f ON mrr.reviewer_id = f.faculty_id
      WHERE mrr.report_id = ${report_id}
      AND mrr.is_deleted = FALSE
      AND mrr.is_submitted = TRUE
      ORDER BY mrr.created_at DESC
    `;

    // Return the report details and all ratings
    return NextResponse.json({
      report: {
        id: report.id,
        faculty_id: report.faculty_id,
        faculty_name: report.faculty_name,
        unit_id: report.unit_id,
        unit_name: report.unit_name,
        status: report.status
      },
      ratings: ratings
    });
  } catch (error) {
    console.error("Error fetching ratings:", error);
    return NextResponse.json(
      { error: "Failed to fetch ratings" },
      { status: 500 }
    );
  }
}