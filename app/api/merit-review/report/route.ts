import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { getLevel4UnitIdForFaculty } from "@/app/lib/utils/unit-utils";

// Get faculty member's own report
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get the faculty details using the faculty_id from the session
    const [faculty] = await sql`
      SELECT
        f.faculty_id,
        f.primary_unit_id,
        un.parent_unit_id
      FROM uw.faculty f
      JOIN uw.unit un ON f.primary_unit_id = un.unit_id
      WHERE f.faculty_id = ${session.user.facultyId}
      AND f.is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json(
        { error: "Faculty information not found" },
        { status: 404 }
      );
    }

    // Get the active workflow for the faculty's unit or parent unit
    const activeWorkflows = await sql`
      SELECT id, unit_id, start_dt, end_dt, status
      FROM uw.merit_workflow_config
      WHERE (unit_id = ${faculty.primary_unit_id} OR unit_id = ${faculty.parent_unit_id})
      AND status = 'active'
      AND is_deleted = FALSE
      ORDER BY unit_id = ${faculty.primary_unit_id} DESC -- Prioritize primary unit
    `;

    // Use the first active workflow found (prioritizing primary unit)
    const activeWorkflow = activeWorkflows.length > 0 ? activeWorkflows[0] : null;

    if (!activeWorkflow) {
      return NextResponse.json(
        { error: "No active merit review workflow" },
        { status: 404 }
      );
    }

    // Get the faculty member's report for the active workflow's unit
    const reportResult = activeWorkflow ? await sql`
      SELECT id, faculty_id, unit_id, report_doc, create_dt, update_dt, status
      FROM uw.merit_report
      WHERE faculty_id = ${faculty.faculty_id}
      AND unit_id = ${activeWorkflow.unit_id}
      AND is_deleted = FALSE
      ORDER BY create_dt DESC
      LIMIT 1
    ` : [];

    const report = reportResult[0] || null;

    return NextResponse.json({
      workflow: activeWorkflow,
      report: report,
    });
  } catch (error) {
    console.error("Error fetching faculty report:", error);
    return NextResponse.json(
      { error: "Failed to fetch faculty report" },
      { status: 500 }
    );
  }
}

// Create or update a faculty report
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get the faculty details using the faculty_id from the session
    const [faculty] = await sql`
      SELECT
        f.faculty_id,
        f.primary_unit_id,
        un.parent_unit_id
      FROM uw.faculty f
      JOIN uw.unit un ON f.primary_unit_id = un.unit_id
      WHERE f.faculty_id = ${session.user.facultyId}
      AND f.is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json(
        { error: "Faculty information not found" },
        { status: 404 }
      );
    }

    // Get the active workflow for the faculty's unit or parent unit
    const activeWorkflows = await sql`
      SELECT id, unit_id, start_dt, end_dt, status
      FROM uw.merit_workflow_config
      WHERE (unit_id = ${faculty.primary_unit_id} OR unit_id = ${faculty.parent_unit_id})
      AND status = 'active'
      AND is_deleted = FALSE
      ORDER BY unit_id = ${faculty.primary_unit_id} DESC -- Prioritize primary unit
    `;

    // Use the first active workflow found (prioritizing primary unit)
    const activeWorkflow = activeWorkflows.length > 0 ? activeWorkflows[0] : null;

    if (!activeWorkflow) {
      return NextResponse.json(
        { error: "No active merit review workflow" },
        { status: 404 }
      );
    }

    const { report_doc, status } = await request.json();

    if (!report_doc) {
      return NextResponse.json(
        { error: "Report content is required" },
        { status: 400 }
      );
    }

    // Check if the faculty member already has a report for this workflow
    const [existingReport] = await sql`
      SELECT id
      FROM uw.merit_report
      WHERE faculty_id = ${faculty.faculty_id}
      AND unit_id = ${activeWorkflow.unit_id}
      AND is_deleted = FALSE
    `;

    let report;

    if (existingReport) {
      // Update the existing report
      [report] = await sql`
        UPDATE uw.merit_report
        SET
          report_doc = ${report_doc},
          status = ${status || 'draft'},
          update_dt = NOW()
        WHERE id = ${existingReport.id}
        RETURNING id, faculty_id, unit_id, report_doc, create_dt, update_dt, status
      `;
    } else {
      // Get the faculty's level 4 unit ID
      const level4UnitId = await getLevel4UnitIdForFaculty(faculty.faculty_id);

      // Use the level 4 unit ID if available, otherwise fall back to the workflow unit_id
      const reportUnitId = level4UnitId || activeWorkflow.unit_id;

      // Create a new report
      [report] = await sql`
        INSERT INTO uw.merit_report (
          faculty_id, unit_id, report_doc, status
        ) VALUES (
          ${faculty.faculty_id}, ${reportUnitId}, ${report_doc}, ${status || 'draft'}
        )
        RETURNING id, faculty_id, unit_id, report_doc, create_dt, update_dt, status
      `;
    }

    return NextResponse.json(report);
  } catch (error) {
    console.error("Error saving faculty report:", error);
    return NextResponse.json(
      { error: "Failed to save faculty report" },
      { status: 500 }
    );
  }
}
