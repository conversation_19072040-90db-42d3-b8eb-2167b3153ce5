import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get a faculty member's report for admin users
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role or is a committee member
    const isAdmin: boolean = Boolean(
      session.user.roles?.includes("system_admin") ||
      session.user.roles?.includes("faculty_admin") ||
      session.user.roles?.includes("department_admin")
    );

    // Check if user is a committee member
    const [faculty] = await sql`
      SELECT faculty_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    let isCommitteeMember: boolean = false;
    if (faculty) {
      const [committeeMembership] = await sql`
        SELECT 1
        FROM uw.merit_review_committee
        WHERE faculty_id = ${faculty.faculty_id}
        AND is_deleted = FALSE
      `;
      isCommitteeMember = Boolean(committeeMembership);
    }

    if (!isAdmin && !isCommitteeMember) {
      return NextResponse.json(
        { error: "You don't have permission to access this resource" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Get the faculty ID for the current admin user
    const [adminFaculty] = await sql`
      SELECT f.faculty_id, f.primary_unit_id
      FROM uw.faculty f
      JOIN common.user u ON f.work_email = u.email
      WHERE u.user_id = ${session.user.id}
      AND f.is_deleted = FALSE
    `;

    // Get the report
    const report = await getReportById(parseInt(reportId), adminFaculty, isAdmin, Boolean(isCommitteeMember), faculty?.faculty_id, session.user.roles);

    if (!report) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    return NextResponse.json(report);
  } catch (error) {
    console.error("Error fetching faculty report for admin:", error);
    return NextResponse.json(
      { error: "Failed to fetch faculty report" },
      { status: 500 }
    );
  }
}

// Helper function to get a specific report by ID for admin users or committee members
async function getReportById(
  reportId: number,
  adminFaculty: any,
  _isAdmin: boolean, // Unused parameter, prefixed with underscore
  isCommitteeMember: boolean | undefined,
  facultyId: number | undefined,
  roles: string[] = []
) {
  try {
    // First, get the report to check access permissions
    const [reportBasic] = await sql`
      SELECT r.id, r.faculty_id, r.unit_id
      FROM uw.merit_report r
      WHERE r.id = ${reportId}
      AND r.is_deleted = FALSE
    `;

    if (!reportBasic) {
      return null;
    }

    // Check if user has access to this report
    let hasAccess = false;

    // System admin has access to all reports
    if (roles.includes("system_admin")) {
      hasAccess = true;
    }
    // Faculty admin needs to check if the report belongs to their faculty or child units
    else if (roles.includes("faculty_admin") && adminFaculty) {
      // Get the faculty level unit (level 3) for the admin
      const [facultyUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the admin's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${adminFaculty.primary_unit_id}
          AND is_deleted = FALSE

          UNION ALL

          -- Get all parent units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT unit_id
        FROM unit_hierarchy
        WHERE level_number = 3
        LIMIT 1
      `;

      if (facultyUnit) {
        // Check if the report's unit is within the admin's faculty hierarchy
        const [unitCheck] = await sql`
          WITH RECURSIVE unit_hierarchy AS (
            -- Start with the faculty unit
            SELECT unit_id, parent_unit_id
            FROM uw.unit
            WHERE unit_id = ${facultyUnit.unit_id}
            AND is_deleted = FALSE

            UNION ALL

            -- Get all child units
            SELECT u.unit_id, u.parent_unit_id
            FROM uw.unit u
            JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
            WHERE u.is_deleted = FALSE
          )
          SELECT 1 as has_access
          FROM unit_hierarchy
          WHERE unit_id = ${reportBasic.unit_id}
          LIMIT 1
        `;

        hasAccess = !!unitCheck;
      }
    }
    // Department admin needs to check if the report belongs to their specific unit
    else if (roles.includes("department_admin") && adminFaculty) {
      hasAccess = reportBasic.unit_id === adminFaculty.primary_unit_id;
    }
    // Committee member needs to check if the report belongs to their department (level 4 unit)
    else if (isCommitteeMember && facultyId) {
      // Get the committee member's unit
      const [committeeMemberUnit] = await sql`
        SELECT mrc.unit_id, u.level_number
        FROM uw.merit_review_committee mrc
        JOIN uw.unit u ON mrc.unit_id = u.unit_id
        WHERE mrc.faculty_id = ${facultyId}
        AND mrc.is_deleted = FALSE
        AND u.is_deleted = FALSE
      `;

      if (committeeMemberUnit) {
        // If the committee member's unit is level 4, check if it matches the report's unit
        if (committeeMemberUnit.level_number === 4) {
          hasAccess = reportBasic.unit_id === committeeMemberUnit.unit_id;
        }
        // If the committee member's unit is at a higher level (1-3), check if the report's unit is a child
        else if (committeeMemberUnit.level_number < 4) {
          const [childUnitCheck] = await sql`
            WITH RECURSIVE unit_hierarchy AS (
              -- Start with the committee member's unit
              SELECT unit_id, parent_unit_id
              FROM uw.unit
              WHERE unit_id = ${committeeMemberUnit.unit_id}
              AND is_deleted = FALSE

              UNION ALL

              -- Get all child units
              SELECT u.unit_id, u.parent_unit_id
              FROM uw.unit u
              JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
              WHERE u.is_deleted = FALSE
            )
            SELECT 1 as has_access
            FROM unit_hierarchy
            WHERE unit_id = ${reportBasic.unit_id}
            LIMIT 1
          `;

          hasAccess = !!childUnitCheck;
        }
        // If the committee member's unit is at a lower level (5+), check if the report's unit is a parent at level 4
        else if (committeeMemberUnit.level_number > 4) {
          const [parentUnitCheck] = await sql`
            WITH RECURSIVE unit_hierarchy AS (
              -- Start with the committee member's unit
              SELECT unit_id, parent_unit_id, level_number
              FROM uw.unit
              WHERE unit_id = ${committeeMemberUnit.unit_id}
              AND is_deleted = FALSE

              UNION ALL

              -- Get all parent units
              SELECT u.unit_id, u.parent_unit_id, u.level_number
              FROM uw.unit u
              JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
              WHERE u.is_deleted = FALSE
            )
            SELECT 1 as has_access
            FROM unit_hierarchy
            WHERE unit_id = ${reportBasic.unit_id}
            AND level_number = 4
            LIMIT 1
          `;

          hasAccess = !!parentUnitCheck;
        }
      }
    }

    if (!hasAccess) {
      return null;
    }

    // Now get the full report details
    const [report] = await sql`
      SELECT r.*, u.full_name as unit_name, w.start_dt as workflow_start_dt, w.end_dt as workflow_end_dt,
             CONCAT(f.first_name, ' ', f.last_name) as faculty_name, f.faculty_id
      FROM uw.merit_report r
      JOIN uw.unit u ON r.unit_id = u.unit_id
      JOIN uw.merit_workflow_config w ON r.workflow_id = w.id
      JOIN uw.faculty f ON r.faculty_id = f.faculty_id
      WHERE r.id = ${reportId}
      AND r.is_deleted = FALSE
    `;

    if (!report) {
      return null;
    }

    // Get all related sections
    const teaching = await getTeachingSection(reportId);
    const research = await getResearchSection(reportId);
    const service = await getServiceSection(reportId);
    const awards = await getAwardsSection(reportId);
    const additionalComments = await getAdditionalCommentsSection(reportId);

    return {
      ...report,
      teaching,
      research,
      service,
      awards,
      additionalComments
    };
  } catch (error) {
    console.error("Error fetching report by ID for admin:", error);
    throw error;
  }
}

// Helper function to get teaching section
async function getTeachingSection(reportId: number) {
  try {
    // Get teaching main data
    const [teaching] = await sql`
      SELECT * FROM uw.merit_report_teaching
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get teaching courses
    const courses = await sql`
      SELECT * FROM uw.merit_report_teaching_courses
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY term_year
    `;

    // Get student supervision
    const [supervision] = await sql`
      SELECT * FROM uw.merit_report_student_supervision
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get teaching summary
    const [summary] = await sql`
      SELECT * FROM uw.merit_report_teaching_summary
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return {
      ...teaching,
      courses,
      supervision,
      summary
    };
  } catch (error) {
    console.error("Error fetching teaching section:", error);
    throw error;
  }
}

// Helper function to get research section
async function getResearchSection(reportId: number) {
  try {
    // Get research main data
    const [research] = await sql`
      SELECT * FROM uw.merit_report_research
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get research publications
    const publications = await sql`
      SELECT * FROM uw.merit_report_research_publications
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY year DESC, publication_type
    `;

    // Get research grants
    const grants = await sql`
      SELECT * FROM uw.merit_report_research_grants
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY status, installment_year DESC
    `;

    // Get research summary
    const [summary] = await sql`
      SELECT * FROM uw.merit_report_research_summary
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return {
      ...research,
      publications,
      grants,
      summary
    };
  } catch (error) {
    console.error("Error fetching research section:", error);
    throw error;
  }
}

// Helper function to get service section
async function getServiceSection(reportId: number) {
  try {
    // Get service main data
    const [service] = await sql`
      SELECT * FROM uw.merit_report_service
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get service activities
    const activities = await sql`
      SELECT * FROM uw.merit_report_service_activities
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY service_type, start_date DESC
    `;

    // Get professional registration
    const registrations = await sql`
      SELECT * FROM uw.merit_report_professional_registration
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY registration_year DESC
    `;

    // Get service summary
    const [summary] = await sql`
      SELECT * FROM uw.merit_report_service_summary
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return {
      ...service,
      activities,
      registrations,
      summary
    };
  } catch (error) {
    console.error("Error fetching service section:", error);
    throw error;
  }
}

// Helper function to get awards section
async function getAwardsSection(reportId: number) {
  try {
    const awards = await sql`
      SELECT * FROM uw.merit_report_awards
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY year DESC, award_type
    `;

    return awards;
  } catch (error) {
    console.error("Error fetching awards section:", error);
    throw error;
  }
}

// Helper function to get additional comments section
async function getAdditionalCommentsSection(reportId: number) {
  try {
    const [comments] = await sql`
      SELECT * FROM uw.merit_report_additional_comments
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return comments;
  } catch (error) {
    console.error("Error fetching additional comments section:", error);
    throw error;
  }
}
