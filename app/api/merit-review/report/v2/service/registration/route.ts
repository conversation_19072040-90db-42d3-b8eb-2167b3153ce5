import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get professional registrations
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("report_id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the professional registrations
    const registrations = await sql`
      SELECT * FROM uw.merit_report_professional_registration
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY registration_year DESC
    `;

    return NextResponse.json(registrations);
  } catch (error) {
    console.error("Error fetching professional registrations:", error);
    return NextResponse.json(
      { error: "Failed to fetch professional registrations" },
      { status: 500 }
    );
  }
}

// Add a professional registration
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { 
      report_id, 
      designation, 
      registration_year, 
      province,
      requirements
    } = await request.json();

    if (!report_id || !designation) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Add the professional registration
    const [newRegistration] = await sql`
      INSERT INTO uw.merit_report_professional_registration (
        report_id, 
        designation, 
        registration_year, 
        province,
        requirements
      ) VALUES (
        ${report_id}, 
        ${designation}, 
        ${registration_year}, 
        ${province},
        ${requirements}
      )
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(newRegistration, { status: 201 });
  } catch (error) {
    console.error("Error adding professional registration:", error);
    return NextResponse.json(
      { error: "Failed to add professional registration" },
      { status: 500 }
    );
  }
}

// Update a professional registration
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { 
      id,
      report_id, 
      designation, 
      registration_year, 
      province,
      requirements
    } = await request.json();

    if (!id || !report_id) {
      return NextResponse.json(
        { error: "Registration ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the registration exists and belongs to the report
    const [existingRegistration] = await sql`
      SELECT id FROM uw.merit_report_professional_registration
      WHERE id = ${id}
      AND report_id = ${report_id}
      AND is_deleted = FALSE
    `;

    if (!existingRegistration) {
      return NextResponse.json(
        { error: "Registration not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Update the professional registration
    const [updatedRegistration] = await sql`
      UPDATE uw.merit_report_professional_registration
      SET 
        designation = ${designation}, 
        registration_year = ${registration_year}, 
        province = ${province},
        requirements = ${requirements},
        update_dt = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedRegistration);
  } catch (error) {
    console.error("Error updating professional registration:", error);
    return NextResponse.json(
      { error: "Failed to update professional registration" },
      { status: 500 }
    );
  }
}

// Delete a professional registration
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const reportId = searchParams.get("report_id");

    if (!id || !reportId) {
      return NextResponse.json(
        { error: "Registration ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the registration exists and belongs to the report
    const [existingRegistration] = await sql`
      SELECT id FROM uw.merit_report_professional_registration
      WHERE id = ${parseInt(id)}
      AND report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
    `;

    if (!existingRegistration) {
      return NextResponse.json(
        { error: "Registration not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Soft delete the professional registration
    await sql`
      UPDATE uw.merit_report_professional_registration
      SET is_deleted = TRUE, update_dt = NOW()
      WHERE id = ${parseInt(id)}
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${parseInt(reportId)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting professional registration:", error);
    return NextResponse.json(
      { error: "Failed to delete professional registration" },
      { status: 500 }
    );
  }
}
