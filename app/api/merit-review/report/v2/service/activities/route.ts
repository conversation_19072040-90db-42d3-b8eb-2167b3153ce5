import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get service activities
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("report_id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the service activities
    const activities = await sql`
      SELECT * FROM uw.merit_report_service_activities
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY service_type, start_date DESC
    `;

    return NextResponse.json(activities);
  } catch (error) {
    console.error("Error fetching service activities:", error);
    return NextResponse.json(
      { error: "Failed to fetch service activities" },
      { status: 500 }
    );
  }
}

// Add a service activity
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { 
      report_id, 
      service_type, 
      committee_name, 
      role,
      start_date,
      end_date,
      time_spent
    } = await request.json();

    if (!report_id || !service_type || !committee_name) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Add the service activity
    const [newActivity] = await sql`
      INSERT INTO uw.merit_report_service_activities (
        report_id, 
        service_type, 
        committee_name, 
        role,
        start_date,
        end_date,
        time_spent
      ) VALUES (
        ${report_id}, 
        ${service_type}, 
        ${committee_name}, 
        ${role},
        ${start_date},
        ${end_date},
        ${time_spent}
      )
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(newActivity, { status: 201 });
  } catch (error) {
    console.error("Error adding service activity:", error);
    return NextResponse.json(
      { error: "Failed to add service activity" },
      { status: 500 }
    );
  }
}

// Update a service activity
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { 
      id,
      report_id, 
      service_type, 
      committee_name, 
      role,
      start_date,
      end_date,
      time_spent
    } = await request.json();

    if (!id || !report_id) {
      return NextResponse.json(
        { error: "Activity ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the activity exists and belongs to the report
    const [existingActivity] = await sql`
      SELECT id FROM uw.merit_report_service_activities
      WHERE id = ${id}
      AND report_id = ${report_id}
      AND is_deleted = FALSE
    `;

    if (!existingActivity) {
      return NextResponse.json(
        { error: "Activity not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Update the service activity
    const [updatedActivity] = await sql`
      UPDATE uw.merit_report_service_activities
      SET 
        service_type = ${service_type}, 
        committee_name = ${committee_name}, 
        role = ${role},
        start_date = ${start_date},
        end_date = ${end_date},
        time_spent = ${time_spent},
        update_dt = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedActivity);
  } catch (error) {
    console.error("Error updating service activity:", error);
    return NextResponse.json(
      { error: "Failed to update service activity" },
      { status: 500 }
    );
  }
}

// Delete a service activity
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const reportId = searchParams.get("report_id");

    if (!id || !reportId) {
      return NextResponse.json(
        { error: "Activity ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the activity exists and belongs to the report
    const [existingActivity] = await sql`
      SELECT id FROM uw.merit_report_service_activities
      WHERE id = ${parseInt(id)}
      AND report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
    `;

    if (!existingActivity) {
      return NextResponse.json(
        { error: "Activity not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Soft delete the service activity
    await sql`
      UPDATE uw.merit_report_service_activities
      SET is_deleted = TRUE, update_dt = NOW()
      WHERE id = ${parseInt(id)}
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${parseInt(reportId)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting service activity:", error);
    return NextResponse.json(
      { error: "Failed to delete service activity" },
      { status: 500 }
    );
  }
}
