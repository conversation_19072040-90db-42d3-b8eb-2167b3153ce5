import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Update service summary
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { 
      report_id, 
      accomplishments,
      goals
    } = await request.json();

    if (!report_id) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Update the service summary
    const [updatedSummary] = await sql`
      UPDATE uw.merit_report_service_summary
      SET 
        accomplishments = ${accomplishments},
        goals = ${goals},
        update_dt = NOW()
      WHERE report_id = ${report_id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedSummary);
  } catch (error) {
    console.error("Error updating service summary:", error);
    return NextResponse.json(
      { error: "Failed to update service summary" },
      { status: 500 }
    );
  }
}
