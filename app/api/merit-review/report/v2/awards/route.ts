import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get awards
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("report_id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the awards
    const awards = await sql`
      SELECT * FROM uw.merit_report_awards
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY year DESC, award_type
    `;

    return NextResponse.json(awards);
  } catch (error) {
    console.error("Error fetching awards:", error);
    return NextResponse.json(
      { error: "Failed to fetch awards" },
      { status: 500 }
    );
  }
}

// Add an award
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { 
      report_id, 
      award_name, 
      award_type, 
      awarding_body,
      year,
      description
    } = await request.json();

    if (!report_id || !award_name || !award_type || !year) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Add the award
    const [newAward] = await sql`
      INSERT INTO uw.merit_report_awards (
        report_id, 
        award_name, 
        award_type, 
        awarding_body,
        year,
        description
      ) VALUES (
        ${report_id}, 
        ${award_name}, 
        ${award_type}, 
        ${awarding_body},
        ${year},
        ${description}
      )
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(newAward, { status: 201 });
  } catch (error) {
    console.error("Error adding award:", error);
    return NextResponse.json(
      { error: "Failed to add award" },
      { status: 500 }
    );
  }
}

// Update an award
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { 
      id,
      report_id, 
      award_name, 
      award_type, 
      awarding_body,
      year,
      description
    } = await request.json();

    if (!id || !report_id) {
      return NextResponse.json(
        { error: "Award ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the award exists and belongs to the report
    const [existingAward] = await sql`
      SELECT id FROM uw.merit_report_awards
      WHERE id = ${id}
      AND report_id = ${report_id}
      AND is_deleted = FALSE
    `;

    if (!existingAward) {
      return NextResponse.json(
        { error: "Award not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Update the award
    const [updatedAward] = await sql`
      UPDATE uw.merit_report_awards
      SET 
        award_name = ${award_name}, 
        award_type = ${award_type}, 
        awarding_body = ${awarding_body},
        year = ${year},
        description = ${description},
        update_dt = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedAward);
  } catch (error) {
    console.error("Error updating award:", error);
    return NextResponse.json(
      { error: "Failed to update award" },
      { status: 500 }
    );
  }
}

// Delete an award
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const reportId = searchParams.get("report_id");

    if (!id || !reportId) {
      return NextResponse.json(
        { error: "Award ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the award exists and belongs to the report
    const [existingAward] = await sql`
      SELECT id FROM uw.merit_report_awards
      WHERE id = ${parseInt(id)}
      AND report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
    `;

    if (!existingAward) {
      return NextResponse.json(
        { error: "Award not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Soft delete the award
    await sql`
      UPDATE uw.merit_report_awards
      SET is_deleted = TRUE, update_dt = NOW()
      WHERE id = ${parseInt(id)}
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${parseInt(reportId)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting award:", error);
    return NextResponse.json(
      { error: "Failed to delete award" },
      { status: 500 }
    );
  }
}
