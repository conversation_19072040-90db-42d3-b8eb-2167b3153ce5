import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { getLevel4UnitIdForFaculty } from "@/app/lib/utils/unit-utils";

// Get faculty member's own report
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("id");

    // If report ID is provided, get that specific report
    if (reportId) {
      const report = await getReportById(parseInt(reportId), session.user.facultyId);
      if (!report) {
        return NextResponse.json(
          { error: "Report not found or you don't have access to it" },
          { status: 404 }
        );
      }
      return NextResponse.json(report);
    }

    // Otherwise, get the faculty's active reports
    const reports = await getActiveReports(session.user.facultyId);
    return NextResponse.json(reports);
  } catch (error) {
    console.error("Error fetching faculty report:", error);
    return NextResponse.json(
      { error: "Failed to fetch faculty report" },
      { status: 500 }
    );
  }
}

// Create a new merit report
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { unit_id, workflow_id, report_type, report_year } = await request.json();

    if (!unit_id || !workflow_id || !report_type || !report_year) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if the workflow exists and is active
    const [workflow] = await sql`
      SELECT id FROM uw.merit_workflow_config
      WHERE id = ${workflow_id}
      AND status = 'active'
      AND is_deleted = FALSE
    `;

    if (!workflow) {
      return NextResponse.json(
        { error: "Workflow not found or not active" },
        { status: 404 }
      );
    }

    // Check if the faculty already has a report for this workflow
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE faculty_id = ${session.user.facultyId}
      AND workflow_id = ${workflow_id}
      AND is_deleted = FALSE
    `;

    if (existingReport) {
      return NextResponse.json(
        { error: "You already have a report for this workflow" },
        { status: 409 }
      );
    }

    // Get the faculty's level 4 unit ID
    const level4UnitId = await getLevel4UnitIdForFaculty(session.user.facultyId);

    // Use the level 4 unit ID if available, otherwise fall back to the provided unit_id
    const reportUnitId = level4UnitId || unit_id;

    // Create the new report
    const [newReport] = await sql`
      INSERT INTO uw.merit_report (
        faculty_id, unit_id, workflow_id, report_type, report_year, status
      ) VALUES (
        ${session.user.facultyId}, ${reportUnitId}, ${workflow_id}, ${report_type}, ${report_year}, 'draft'
      )
      RETURNING id, faculty_id, unit_id, workflow_id, report_type, report_year, status, create_dt, update_dt
    `;

    // Initialize the related tables with empty records
    await initializeReportSections(newReport.id);

    return NextResponse.json(newReport, { status: 201 });
  } catch (error) {
    console.error("Error creating merit report:", error);
    return NextResponse.json(
      { error: "Failed to create merit report" },
      { status: 500 }
    );
  }
}

// Update an existing merit report
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { id, status } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Update the report status if provided
    let updatedReport;
    if (status) {
      // If status is being changed to 'submitted', set the submit_dt
      if (status === 'submitted') {
        [updatedReport] = await sql`
          UPDATE uw.merit_report
          SET status = ${status}, update_dt = NOW(), submit_dt = NOW()
          WHERE id = ${id}
          RETURNING id, faculty_id, unit_id, workflow_id, report_type, report_year, status, create_dt, update_dt, submit_dt
        `;
      } else {
        [updatedReport] = await sql`
          UPDATE uw.merit_report
          SET status = ${status}, update_dt = NOW()
          WHERE id = ${id}
          RETURNING id, faculty_id, unit_id, workflow_id, report_type, report_year, status, create_dt, update_dt, submit_dt
        `;
      }
    } else {
      [updatedReport] = await sql`
        UPDATE uw.merit_report
        SET update_dt = NOW()
        WHERE id = ${id}
        RETURNING id, faculty_id, unit_id, workflow_id, report_type, report_year, status, create_dt, update_dt, submit_dt
      `;
    }

    return NextResponse.json(updatedReport);
  } catch (error) {
    console.error("Error updating merit report:", error);
    return NextResponse.json(
      { error: "Failed to update merit report" },
      { status: 500 }
    );
  }
}

// Delete a merit report (soft delete)
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(id)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Soft delete the report
    await sql`
      UPDATE uw.merit_report
      SET is_deleted = TRUE, update_dt = NOW()
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting merit report:", error);
    return NextResponse.json(
      { error: "Failed to delete merit report" },
      { status: 500 }
    );
  }
}

// Helper function to get a specific report by ID
async function getReportById(reportId: number, facultyId: number) {
  try {
    // Get the main report
    const [report] = await sql`
      SELECT r.*, u.full_name as unit_name, w.start_dt as workflow_start_dt, w.end_dt as workflow_end_dt
      FROM uw.merit_report r
      JOIN uw.unit u ON r.unit_id = u.unit_id
      JOIN uw.merit_workflow_config w ON r.workflow_id = w.id
      WHERE r.id = ${reportId}
      AND r.faculty_id = ${facultyId}
      AND r.is_deleted = FALSE
    `;

    if (!report) {
      return null;
    }

    // Get all related sections
    const teaching = await getTeachingSection(reportId);
    const research = await getResearchSection(reportId);
    const service = await getServiceSection(reportId);
    const awards = await getAwardsSection(reportId);
    const additionalComments = await getAdditionalCommentsSection(reportId);

    return {
      ...report,
      teaching,
      research,
      service,
      awards,
      additionalComments
    };
  } catch (error) {
    console.error("Error fetching report by ID:", error);
    throw error;
  }
}

// Helper function to get active reports for a faculty
async function getActiveReports(facultyId: number) {
  try {
    const reports = await sql`
      SELECT r.*, u.full_name as unit_name, w.start_dt as workflow_start_dt, w.end_dt as workflow_end_dt
      FROM uw.merit_report r
      JOIN uw.unit u ON r.unit_id = u.unit_id
      JOIN uw.merit_workflow_config w ON r.workflow_id = w.id
      WHERE r.faculty_id = ${facultyId}
      AND r.is_deleted = FALSE
      ORDER BY r.create_dt DESC
    `;

    return reports;
  } catch (error) {
    console.error("Error fetching active reports:", error);
    throw error;
  }
}

// Helper function to initialize all report sections
async function initializeReportSections(reportId: number) {
  try {
    // Initialize teaching section
    await sql`
      INSERT INTO uw.merit_report_teaching (report_id)
      VALUES (${reportId})
    `;

    // Initialize research section
    await sql`
      INSERT INTO uw.merit_report_research (report_id)
      VALUES (${reportId})
    `;

    // Initialize service section
    await sql`
      INSERT INTO uw.merit_report_service (report_id)
      VALUES (${reportId})
    `;

    // Initialize student supervision
    await sql`
      INSERT INTO uw.merit_report_student_supervision (report_id)
      VALUES (${reportId})
    `;

    // Initialize teaching summary
    await sql`
      INSERT INTO uw.merit_report_teaching_summary (report_id)
      VALUES (${reportId})
    `;

    // Initialize research summary
    await sql`
      INSERT INTO uw.merit_report_research_summary (report_id)
      VALUES (${reportId})
    `;

    // Initialize service summary
    await sql`
      INSERT INTO uw.merit_report_service_summary (report_id)
      VALUES (${reportId})
    `;

    // Initialize additional comments
    await sql`
      INSERT INTO uw.merit_report_additional_comments (report_id)
      VALUES (${reportId})
    `;
  } catch (error) {
    console.error("Error initializing report sections:", error);
    throw error;
  }
}

// Helper function to get teaching section
async function getTeachingSection(reportId: number) {
  try {
    // Get teaching main data
    const [teaching] = await sql`
      SELECT * FROM uw.merit_report_teaching
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get teaching courses
    const courses = await sql`
      SELECT * FROM uw.merit_report_teaching_courses
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY term_year
    `;

    // Get student supervision
    const [supervision] = await sql`
      SELECT * FROM uw.merit_report_student_supervision
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get teaching summary
    const [summary] = await sql`
      SELECT * FROM uw.merit_report_teaching_summary
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return {
      ...teaching,
      courses,
      supervision,
      summary
    };
  } catch (error) {
    console.error("Error fetching teaching section:", error);
    throw error;
  }
}

// Helper function to get research section
async function getResearchSection(reportId: number) {
  try {
    // Get research main data
    const [research] = await sql`
      SELECT * FROM uw.merit_report_research
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get research publications
    const publications = await sql`
      SELECT * FROM uw.merit_report_research_publications
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY year DESC, publication_type
    `;

    // Get research grants
    const grants = await sql`
      SELECT * FROM uw.merit_report_research_grants
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY status, installment_year DESC
    `;

    // Get research summary
    const [summary] = await sql`
      SELECT * FROM uw.merit_report_research_summary
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return {
      ...research,
      publications,
      grants,
      summary
    };
  } catch (error) {
    console.error("Error fetching research section:", error);
    throw error;
  }
}

// Helper function to get service section
async function getServiceSection(reportId: number) {
  try {
    // Get service main data
    const [service] = await sql`
      SELECT * FROM uw.merit_report_service
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    // Get service activities
    const activities = await sql`
      SELECT * FROM uw.merit_report_service_activities
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY service_type, start_date DESC
    `;

    // Get professional registration
    const registrations = await sql`
      SELECT * FROM uw.merit_report_professional_registration
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY registration_year DESC
    `;

    // Get service summary
    const [summary] = await sql`
      SELECT * FROM uw.merit_report_service_summary
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return {
      ...service,
      activities,
      registrations,
      summary
    };
  } catch (error) {
    console.error("Error fetching service section:", error);
    throw error;
  }
}

// Helper function to get awards section
async function getAwardsSection(reportId: number) {
  try {
    const awards = await sql`
      SELECT * FROM uw.merit_report_awards
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
      ORDER BY year DESC, award_type
    `;

    return awards;
  } catch (error) {
    console.error("Error fetching awards section:", error);
    throw error;
  }
}

// Helper function to get additional comments section
async function getAdditionalCommentsSection(reportId: number) {
  try {
    const [comments] = await sql`
      SELECT * FROM uw.merit_report_additional_comments
      WHERE report_id = ${reportId}
      AND is_deleted = FALSE
    `;

    return comments;
  } catch (error) {
    console.error("Error fetching additional comments section:", error);
    throw error;
  }
}
