import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { GoogleScholarPublicationRepository } from "@/lib/repositories/google-scholar-publication-repository";

// Helper function to truncate strings to prevent DB errors
function truncateString(str: string | null | undefined, maxLength: number): string | null {
  if (!str) return null;
  return str.length > maxLength ? str.substring(0, maxLength) : str;
}

// Bulk add research publications from Google Scholar
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { report_id } = await request.json();

    if (!report_id) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the faculty member's Google Scholar publications
    const publicationRepo = new GoogleScholarPublicationRepository();
    const publications = await publicationRepo.getPublicationsByFacultyId(session.user.facultyId);

    if (!publications || publications.length === 0) {
      return NextResponse.json(
        { message: "No Google Scholar publications found for this faculty member", publications: [] },
        { status: 200 }
      );
    }

    // Filter publications to only include those from the past 3 years
    const currentYear = new Date().getFullYear();
    const threeYearsAgo = currentYear - 3;

    const recentPublications = publications.filter(pub => {
      const pubYear = pub.year ? parseInt(pub.year.toString()) : 0;
      return pubYear >= threeYearsAgo;
    });

    console.log(`Filtered ${publications.length} total publications to ${recentPublications.length} from the past 3 years (${threeYearsAgo}-${currentYear})`);

    // Insert publications into the merit report
    const insertedPublications = [];
    for (const pub of recentPublications) {
      // Determine publication type based on venue (simple heuristic)
      let publicationType = 'journal';
      if (pub.venue && pub.venue.toLowerCase().includes('conference')) {
        publicationType = 'conference';
      } else if (pub.venue && pub.venue.toLowerCase().includes('book')) {
        publicationType = 'book';
      } else if (pub.venue && pub.venue.toLowerCase().includes('chapter')) {
        publicationType = 'chapter';
      }

      // Apply truncation to ensure values fit within database column constraints
      const title = truncateString(pub.title, 1000) || 'Untitled';
      const authors = truncateString(pub.authors, 1000) || 'Unknown Authors';
      const venue = truncateString(pub.venue, 1000) || 'Unknown Venue';

      // Insert the publication
      const [insertedPub] = await sql`
        INSERT INTO uw.merit_report_research_publications (
          report_id,
          publication_type,
          title,
          authors,
          venue,
          year,
          citation_count,
          is_highlighted
        ) VALUES (
          ${report_id},
          ${publicationType},
          ${title},
          ${authors},
          ${venue},
          ${pub.year || new Date().getFullYear()},
          ${pub.citations || 0},
          ${false}
        )
        RETURNING *
      `;

      insertedPublications.push(insertedPub);
    }

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json({
      message: `Successfully imported ${insertedPublications.length} publications from Google Scholar (past 3 years only)`,
      publications: insertedPublications,
      yearRange: `${threeYearsAgo}-${currentYear}`
    });
  } catch (error) {
    console.error("Error importing Google Scholar publications:", error);
    return NextResponse.json(
      { error: "Failed to import Google Scholar publications" },
      { status: 500 }
    );
  }
}
