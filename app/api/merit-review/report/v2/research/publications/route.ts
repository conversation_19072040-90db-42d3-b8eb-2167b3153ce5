import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Helper function to truncate strings to prevent DB errors
function truncateString(str: string | null | undefined, maxLength: number): string | null {
  if (!str) return null;
  return str.length > maxLength ? str.substring(0, maxLength) : str;
}

// Get research publications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("report_id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the research publications
    const publications = await sql`
      SELECT * FROM uw.merit_report_research_publications
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY year DESC, publication_type
    `;

    return NextResponse.json(publications);
  } catch (error) {
    console.error("Error fetching research publications:", error);
    return NextResponse.json(
      { error: "Failed to fetch research publications" },
      { status: 500 }
    );
  }
}

// Add a research publication
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    let {
      report_id,
      publication_type,
      title,
      authors,
      venue,
      year,
      doi,
      citation_count,
      is_highlighted
    } = await request.json();

    if (!report_id || !publication_type || !title || !authors || !venue || !year) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Apply truncation to ensure values fit within database column constraints
    publication_type = truncateString(publication_type, 50) || 'journal';
    title = truncateString(title, 1000) || 'Untitled';
    authors = truncateString(authors, 1000) || 'Unknown Authors';
    venue = truncateString(venue, 1000) || 'Unknown Venue';
    doi = doi ? truncateString(doi, 255) : null;

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Add the research publication
    const [newPublication] = await sql`
      INSERT INTO uw.merit_report_research_publications (
        report_id,
        publication_type,
        title,
        authors,
        venue,
        year,
        doi,
        citation_count,
        is_highlighted
      ) VALUES (
        ${report_id},
        ${publication_type},
        ${title},
        ${authors},
        ${venue},
        ${year},
        ${doi},
        ${citation_count || 0},
        ${is_highlighted || false}
      )
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(newPublication, { status: 201 });
  } catch (error) {
    console.error("Error adding research publication:", error);
    return NextResponse.json(
      { error: "Failed to add research publication" },
      { status: 500 }
    );
  }
}

// Update a research publication
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    let {
      id,
      report_id,
      publication_type,
      title,
      authors,
      venue,
      year,
      doi,
      citation_count,
      is_highlighted
    } = await request.json();

    if (!id || !report_id) {
      return NextResponse.json(
        { error: "Publication ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Apply truncation to ensure values fit within database column constraints
    publication_type = publication_type ? truncateString(publication_type, 50) : 'journal';
    title = title ? truncateString(title, 1000) : 'Untitled';
    authors = authors ? truncateString(authors, 1000) : 'Unknown Authors';
    venue = venue ? truncateString(venue, 1000) : 'Unknown Venue';
    doi = doi ? truncateString(doi, 255) : null;

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Update the publication
    const [updatedPublication] = await sql`
      UPDATE uw.merit_report_research_publications
      SET
        publication_type = ${publication_type},
        title = ${title},
        authors = ${authors},
        venue = ${venue},
        year = ${year},
        doi = ${doi},
        citation_count = ${citation_count || 0},
        is_highlighted = ${is_highlighted || false},
        update_dt = NOW()
      WHERE id = ${id}
      AND report_id = ${report_id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedPublication);
  } catch (error) {
    console.error("Error updating research publication:", error);
    return NextResponse.json(
      { error: "Failed to update research publication" },
      { status: 500 }
    );
  }
}

// Delete a research publication
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const reportId = searchParams.get("report_id");

    if (!id || !reportId) {
      return NextResponse.json(
        { error: "Publication ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Soft delete the publication
    await sql`
      UPDATE uw.merit_report_research_publications
      SET is_deleted = TRUE, update_dt = NOW()
      WHERE id = ${parseInt(id)}
      AND report_id = ${parseInt(reportId)}
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${parseInt(reportId)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting research publication:", error);
    return NextResponse.json(
      { error: "Failed to delete research publication" },
      { status: 500 }
    );
  }
}
