import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get research grants
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("report_id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the research grants
    const grants = await sql`
      SELECT * FROM uw.merit_report_research_grants
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY status, installment_year DESC
    `;

    return NextResponse.json(grants);
  } catch (error) {
    console.error("Error fetching research grants:", error);
    return NextResponse.json(
      { error: "Failed to fetch research grants" },
      { status: 500 }
    );
  }
}

// Add a research grant
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    let {
      report_id,
      grant_type,
      pi_name,
      collaborators,
      title,
      agency,
      amount,
      installment_year,
      share_percentage,
      status,
      submission_date
    } = await request.json();

    if (!report_id || !grant_type || !pi_name || !title || !agency || !amount || !status) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Truncate strings to fit within database column limits
    const truncateString = (str: string | null | undefined, maxLength: number) => {
      if (!str) return null;
      return str.substring(0, maxLength);
    };

    // Apply truncation to ensure values fit within database column constraints
    grant_type = truncateString(grant_type, 50) || 'research';
    pi_name = truncateString(pi_name, 255) || 'Unknown';
    collaborators = collaborators ? truncateString(collaborators, 255) : null;
    agency = truncateString(agency, 255) || 'Unknown Agency';
    status = truncateString(status, 50) || 'awarded';

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Add the research grant
    const [newGrant] = await sql`
      INSERT INTO uw.merit_report_research_grants (
        report_id,
        grant_type,
        pi_name,
        collaborators,
        title,
        agency,
        amount,
        installment_year,
        share_percentage,
        status,
        submission_date
      ) VALUES (
        ${report_id},
        ${grant_type},
        ${pi_name},
        ${collaborators},
        ${title},
        ${agency},
        ${amount},
        ${installment_year},
        ${share_percentage},
        ${status},
        ${submission_date}
      )
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(newGrant, { status: 201 });
  } catch (error) {
    console.error("Error adding research grant:", error);
    return NextResponse.json(
      { error: "Failed to add research grant" },
      { status: 500 }
    );
  }
}

// Update a research grant
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    let {
      id,
      report_id,
      grant_type,
      pi_name,
      collaborators,
      title,
      agency,
      amount,
      installment_year,
      share_percentage,
      status,
      submission_date
    } = await request.json();

    if (!id || !report_id) {
      return NextResponse.json(
        { error: "Grant ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Truncate strings to fit within database column limits
    const truncateString = (str: string | null | undefined, maxLength: number) => {
      if (!str) return null;
      return str.substring(0, maxLength);
    };

    // Apply truncation to ensure values fit within database column constraints
    grant_type = truncateString(grant_type, 50) || 'research';
    pi_name = truncateString(pi_name, 255) || 'Unknown';
    collaborators = collaborators ? truncateString(collaborators, 255) : null;
    agency = truncateString(agency, 255) || 'Unknown Agency';
    status = truncateString(status, 50) || 'awarded';

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the grant exists and belongs to the report
    const [existingGrant] = await sql`
      SELECT id FROM uw.merit_report_research_grants
      WHERE id = ${id}
      AND report_id = ${report_id}
      AND is_deleted = FALSE
    `;

    if (!existingGrant) {
      return NextResponse.json(
        { error: "Grant not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Update the research grant
    const [updatedGrant] = await sql`
      UPDATE uw.merit_report_research_grants
      SET
        grant_type = ${grant_type},
        pi_name = ${pi_name},
        collaborators = ${collaborators},
        title = ${title},
        agency = ${agency},
        amount = ${amount},
        installment_year = ${installment_year},
        share_percentage = ${share_percentage},
        status = ${status},
        submission_date = ${submission_date},
        update_dt = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedGrant);
  } catch (error) {
    console.error("Error updating research grant:", error);
    return NextResponse.json(
      { error: "Failed to update research grant" },
      { status: 500 }
    );
  }
}

// Delete a research grant
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const reportId = searchParams.get("report_id");

    if (!id || !reportId) {
      return NextResponse.json(
        { error: "Grant ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the grant exists and belongs to the report
    const [existingGrant] = await sql`
      SELECT id FROM uw.merit_report_research_grants
      WHERE id = ${parseInt(id)}
      AND report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
    `;

    if (!existingGrant) {
      return NextResponse.json(
        { error: "Grant not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Soft delete the research grant
    await sql`
      UPDATE uw.merit_report_research_grants
      SET is_deleted = TRUE, update_dt = NOW()
      WHERE id = ${parseInt(id)}
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${parseInt(reportId)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting research grant:", error);
    return NextResponse.json(
      { error: "Failed to delete research grant" },
      { status: 500 }
    );
  }
}
