import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Bulk add research grants
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { report_id, grants } = await request.json();

    if (!report_id || !grants || !Array.isArray(grants) || grants.length === 0) {
      return NextResponse.json(
        { error: "Report ID and grants array are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Prepare values for bulk insert
    const values = grants.map(grant => {
      // Sanitize numeric values
      let sanitizedAmount = 0;
      if (grant.amount !== undefined && grant.amount !== null) {
        const parsedValue = parseFloat(grant.amount);
        sanitizedAmount = isNaN(parsedValue) ? 0 : parsedValue;
      }

      let sanitizedSharePercentage = 100; // Default to 100%
      if (grant.share_percentage !== undefined && grant.share_percentage !== null) {
        const parsedValue = parseFloat(grant.share_percentage);
        sanitizedSharePercentage = isNaN(parsedValue) ? 100 : parsedValue;
      }

      let sanitizedInstallmentYear = null;
      if (grant.installment_year !== undefined && grant.installment_year !== null) {
        const parsedValue = parseInt(grant.installment_year);
        sanitizedInstallmentYear = isNaN(parsedValue) ? null : parsedValue;
      }

      // Truncate strings to fit within database column limits
      const truncateString = (str: string | null | undefined, maxLength: number) => {
        if (!str) return null;
        return str.substring(0, maxLength);
      };

      return {
        report_id,
        // Truncate grant_type to 50 characters (VARCHAR(50))
        grant_type: truncateString(grant.grant_type, 50) || 'research',
        // Truncate pi_name to 255 characters (VARCHAR(255))
        pi_name: truncateString(grant.pi_name, 255) || 'Unknown',
        // Truncate collaborators to 255 characters if present
        collaborators: grant.collaborators ? truncateString(grant.collaborators, 255) : null,
        // Truncate title to fit TEXT field (no practical limit needed)
        title: grant.title || 'Unknown Title',
        // Truncate agency to 255 characters (VARCHAR(255))
        agency: truncateString(grant.agency, 255) || 'Unknown Agency',
        amount: sanitizedAmount,
        installment_year: sanitizedInstallmentYear,
        share_percentage: sanitizedSharePercentage,
        // Truncate status to 50 characters (VARCHAR(50))
        status: truncateString(grant.status, 50) || 'awarded',
        submission_date: grant.submission_date || null
      };
    });

    // Perform bulk insert
    const insertedGrants = await sql`
      INSERT INTO uw.merit_report_research_grants ${sql(
        values,
        'report_id',
        'grant_type',
        'pi_name',
        'collaborators',
        'title',
        'agency',
        'amount',
        'installment_year',
        'share_percentage',
        'status',
        'submission_date'
      )}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json({ grants: insertedGrants });
  } catch (error) {
    console.error("Error adding research grants in bulk:", error);
    return NextResponse.json(
      { error: "Failed to add research grants in bulk" },
      { status: 500 }
    );
  }
}
