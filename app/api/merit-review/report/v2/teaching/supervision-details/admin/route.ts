import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get supervision details for a specific report (admin access)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const isAdmin = session.user.roles?.includes("system_admin") ||
                    session.user.roles?.includes("faculty_admin") ||
                    session.user.roles?.includes("department_admin");

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to access this resource" },
        { status: 403 }
      );
    }

    // Get report_id from query parameters
    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get('report_id');

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Get the faculty ID for the current admin user
    const [adminFaculty] = await sql`
      SELECT f.faculty_id, f.primary_unit_id
      FROM uw.faculty f
      JOIN common.user u ON f.work_email = u.email
      WHERE u.user_id = ${session.user.id}
      AND f.is_deleted = FALSE
    `;

    // First, get the report to check access permissions
    const [reportBasic] = await sql`
      SELECT r.id, r.faculty_id, r.unit_id
      FROM uw.merit_report r
      WHERE r.id = ${parseInt(reportId)}
      AND r.is_deleted = FALSE
    `;

    if (!reportBasic) {
      return NextResponse.json(
        { error: "Report not found" },
        { status: 404 }
      );
    }

    // Check if admin has access to this report
    let hasAccess = false;

    // System admin has access to all reports
    if (session.user.roles?.includes("system_admin")) {
      hasAccess = true;
    }
    // Faculty admin needs to check if the report belongs to their faculty or child units
    else if (session.user.roles?.includes("faculty_admin") && adminFaculty) {
      // Get the faculty level unit (level 3) for the admin
      const [facultyUnit] = await sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the admin's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${adminFaculty.primary_unit_id}
          AND is_deleted = FALSE

          UNION ALL

          -- Get all parent units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.unit_id = uh.parent_unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT unit_id
        FROM unit_hierarchy
        WHERE level_number = 3
        LIMIT 1
      `;

      if (facultyUnit) {
        // Check if the report's unit is within the admin's faculty hierarchy
        const [unitCheck] = await sql`
          WITH RECURSIVE unit_hierarchy AS (
            -- Start with the faculty unit
            SELECT unit_id, parent_unit_id
            FROM uw.unit
            WHERE unit_id = ${facultyUnit.unit_id}
            AND is_deleted = FALSE

            UNION ALL

            -- Get all child units
            SELECT u.unit_id, u.parent_unit_id
            FROM uw.unit u
            JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
            WHERE u.is_deleted = FALSE
          )
          SELECT 1 as has_access
          FROM unit_hierarchy
          WHERE unit_id = ${reportBasic.unit_id}
          LIMIT 1
        `;

        hasAccess = !!unitCheck;
      }
    }
    // Department admin needs to check if the report belongs to their specific unit
    else if (session.user.roles?.includes("department_admin") && adminFaculty) {
      hasAccess = reportBasic.unit_id === adminFaculty.primary_unit_id;
    }

    if (!hasAccess) {
      return NextResponse.json(
        { error: "You don't have permission to access this report" },
        { status: 403 }
      );
    }

    // Get the supervision details
    const supervisionDetails = await sql`
      SELECT * FROM uw.merit_report_supervision
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY term DESC, student_name
    `;

    return NextResponse.json(supervisionDetails);
  } catch (error) {
    console.error("Error fetching supervision details for admin:", error);
    return NextResponse.json(
      { error: "Failed to fetch supervision details" },
      { status: 500 }
    );
  }
}
