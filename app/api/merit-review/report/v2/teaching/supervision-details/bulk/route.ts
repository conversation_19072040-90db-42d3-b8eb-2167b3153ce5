import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Bulk add supervision details
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { report_id, supervisions } = await request.json();

    if (!report_id || !supervisions || !Array.isArray(supervisions) || supervisions.length === 0) {
      return NextResponse.json(
        { error: "Report ID and supervisions array are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Prepare values for bulk insert
    const values = supervisions.map(supervision => {
      return {
        report_id,
        supervision_id: supervision.supervision_id || null,
        term: supervision.term || null,
        faculty: supervision.faculty || null,
        department: supervision.department || null,
        student_id: supervision.student_id || null,
        student_name: supervision.student_name || 'Unknown Student',
        academic_plan: supervision.academic_plan || 'Unknown Plan',
        supervisor_last_name: supervision.supervisor_last_name || null,
        supervisor_first_name: supervision.supervisor_first_name || null,
        citizenship: supervision.citizenship || null,
        email_address: supervision.email_address || null
      };
    });

    // Perform bulk insert
    const insertedSupervisions = await sql`
      INSERT INTO uw.merit_report_supervision ${sql(
        values,
        'report_id',
        'supervision_id',
        'term',
        'faculty',
        'department',
        'student_id',
        'student_name',
        'academic_plan',
        'supervisor_last_name',
        'supervisor_first_name',
        'citizenship',
        'email_address'
      )}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json({
      message: `Successfully added ${insertedSupervisions.length} supervision details`,
      supervisions: insertedSupervisions
    }, { status: 201 });
  } catch (error) {
    console.error("Error bulk adding supervision details:", error);
    return NextResponse.json(
      { error: "Failed to bulk add supervision details" },
      { status: 500 }
    );
  }
}
