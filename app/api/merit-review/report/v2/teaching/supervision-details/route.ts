import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get supervision details for a specific report
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get report_id from query parameters
    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get('report_id');

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the supervision details
    const supervisionDetails = await sql`
      SELECT * FROM uw.merit_report_supervision
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY term DESC, student_name
    `;

    return NextResponse.json(supervisionDetails);
  } catch (error) {
    console.error("Error fetching supervision details:", error);
    return NextResponse.json(
      { error: "Failed to fetch supervision details" },
      { status: 500 }
    );
  }
}

// Add supervision details to a report
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const {
      report_id,
      supervision_id,
      term,
      faculty,
      department,
      student_id,
      student_name,
      academic_plan,
      supervisor_last_name,
      supervisor_first_name,
      citizenship,
      email_address
    } = await request.json();

    if (!report_id || !student_name || !academic_plan) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Add the supervision detail with null checks for optional fields
    const [newSupervision] = await sql`
      INSERT INTO uw.merit_report_supervision (
        report_id,
        supervision_id,
        term,
        faculty,
        department,
        student_id,
        student_name,
        academic_plan,
        supervisor_last_name,
        supervisor_first_name,
        citizenship,
        email_address
      ) VALUES (
        ${report_id},
        ${supervision_id || null},
        ${term || null},
        ${faculty || null},
        ${department || null},
        ${student_id || null},
        ${student_name},
        ${academic_plan},
        ${supervisor_last_name || null},
        ${supervisor_first_name || null},
        ${citizenship || null},
        ${email_address || null}
      )
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(newSupervision, { status: 201 });
  } catch (error) {
    console.error("Error adding supervision detail:", error);
    return NextResponse.json(
      { error: "Failed to add supervision detail" },
      { status: 500 }
    );
  }
}

// Update a supervision detail
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const {
      id,
      report_id,
      term,
      faculty,
      department,
      student_id,
      student_name,
      academic_plan,
      supervisor_last_name,
      supervisor_first_name,
      citizenship,
      email_address
    } = await request.json();

    if (!id || !report_id) {
      return NextResponse.json(
        { error: "Supervision ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Update the supervision detail with null checks for optional fields
    const [updatedSupervision] = await sql`
      UPDATE uw.merit_report_supervision
      SET
        term = ${term || null},
        faculty = ${faculty || null},
        department = ${department || null},
        student_id = ${student_id || null},
        student_name = ${student_name},
        academic_plan = ${academic_plan},
        supervisor_last_name = ${supervisor_last_name || null},
        supervisor_first_name = ${supervisor_first_name || null},
        citizenship = ${citizenship || null},
        email_address = ${email_address || null},
        update_dt = NOW()
      WHERE id = ${id}
      AND report_id = ${report_id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedSupervision);
  } catch (error) {
    console.error("Error updating supervision detail:", error);
    return NextResponse.json(
      { error: "Failed to update supervision detail" },
      { status: 500 }
    );
  }
}

// Delete a supervision detail
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get id and report_id from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const reportId = searchParams.get('report_id');

    if (!id || !reportId) {
      return NextResponse.json(
        { error: "Supervision ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Soft delete the supervision detail
    await sql`
      UPDATE uw.merit_report_supervision
      SET is_deleted = TRUE
      WHERE id = ${parseInt(id)}
      AND report_id = ${parseInt(reportId)}
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${parseInt(reportId)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting supervision detail:", error);
    return NextResponse.json(
      { error: "Failed to delete supervision detail" },
      { status: 500 }
    );
  }
}
