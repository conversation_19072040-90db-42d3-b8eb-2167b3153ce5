import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get teaching courses
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("report_id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get the teaching courses
    const courses = await sql`
      SELECT * FROM uw.merit_report_teaching_courses
      WHERE report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
      ORDER BY term_year
    `;

    return NextResponse.json(courses);
  } catch (error) {
    console.error("Error fetching teaching courses:", error);
    return NextResponse.json(
      { error: "Failed to fetch teaching courses" },
      { status: 500 }
    );
  }
}

// Add a teaching course
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const requestBody = await request.json();
    console.log('Received request body:', JSON.stringify(requestBody));

    const {
      report_id,
      term_year,
      course_number,
      course_title,
      scp_q1_q3,
      scp_q1_q3_std_dev,
      scp_q4_q6,
      scp_q4_q6_std_dev,
      num_students,
      response_percentage
    } = requestBody;

    // Check each required field individually and log which one is missing
    if (!report_id || !term_year || !course_number || !course_title) {
      const missingFields = [];
      if (!report_id) missingFields.push('report_id');
      if (!term_year) missingFields.push('term_year');
      if (!course_number) missingFields.push('course_number');
      if (!course_title) missingFields.push('course_title');

      console.error(`Missing required fields: ${missingFields.join(', ')}`);

      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Sanitize the data before inserting
    // Ensure response_percentage is a reasonable number (0-100)
    let sanitizedResponsePercentage = 0;
    try {
      // Try to parse the response_percentage as a number
      const parsedValue = typeof response_percentage === 'string'
        ? parseFloat(response_percentage)
        : Number(response_percentage);

      // Ensure it's within the valid range (0-100)
      sanitizedResponsePercentage = Math.min(Math.max(Math.round(parsedValue), 0), 100);

      // If it's NaN, use 0
      if (isNaN(sanitizedResponsePercentage)) {
        sanitizedResponsePercentage = 0;
      }
    } catch (e) {
      console.error('Error parsing response_percentage:', e);
      sanitizedResponsePercentage = 0;
    }

    const sanitizedData = {
      report_id,
      term_year: term_year || 'Unknown Term',
      course_number: course_number || 'Unknown Course',
      course_title: course_title || 'Unknown Title',
      scp_q1_q3: scp_q1_q3 || '0.00',
      scp_q1_q3_std_dev: scp_q1_q3_std_dev || '0.00',
      scp_q4_q6: scp_q4_q6 || '0.00',
      scp_q4_q6_std_dev: scp_q4_q6_std_dev || '0.00',
      num_students: num_students || 0,
      response_percentage: sanitizedResponsePercentage
    };

    console.log('Sanitized data for insertion:', sanitizedData);

    // Add the teaching course
    const [newCourse] = await sql`
      INSERT INTO uw.merit_report_teaching_courses (
        report_id,
        term_year,
        course_number,
        course_title,
        scp_q1_q3,
        scp_q1_q3_std_dev,
        scp_q4_q6,
        scp_q4_q6_std_dev,
        num_students,
        response_percentage
      ) VALUES (
        ${sanitizedData.report_id},
        ${sanitizedData.term_year},
        ${sanitizedData.course_number},
        ${sanitizedData.course_title},
        ${sanitizedData.scp_q1_q3},
        ${sanitizedData.scp_q1_q3_std_dev},
        ${sanitizedData.scp_q4_q6},
        ${sanitizedData.scp_q4_q6_std_dev},
        ${sanitizedData.num_students},
        ${sanitizedData.response_percentage}
      )
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(newCourse, { status: 201 });
  } catch (error) {
    console.error("Error adding teaching course:", error);
    return NextResponse.json(
      { error: "Failed to add teaching course" },
      { status: 500 }
    );
  }
}

// Update a teaching course
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const {
      id,
      report_id,
      term_year,
      course_number,
      course_title,
      scp_q1_q3,
      scp_q1_q3_std_dev,
      scp_q4_q6,
      scp_q4_q6_std_dev,
      num_students,
      response_percentage
    } = await request.json();

    if (!id || !report_id) {
      return NextResponse.json(
        { error: "Course ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the course exists and belongs to the report
    const [existingCourse] = await sql`
      SELECT id FROM uw.merit_report_teaching_courses
      WHERE id = ${id}
      AND report_id = ${report_id}
      AND is_deleted = FALSE
    `;

    if (!existingCourse) {
      return NextResponse.json(
        { error: "Course not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Sanitize the data before updating
    // Ensure response_percentage is a reasonable number (0-100)
    let sanitizedResponsePercentage = 0;
    try {
      // Try to parse the response_percentage as a number
      const parsedValue = typeof response_percentage === 'string'
        ? parseFloat(response_percentage)
        : Number(response_percentage);

      // Ensure it's within the valid range (0-100)
      sanitizedResponsePercentage = Math.min(Math.max(Math.round(parsedValue), 0), 100);

      // If it's NaN, use 0
      if (isNaN(sanitizedResponsePercentage)) {
        sanitizedResponsePercentage = 0;
      }
    } catch (e) {
      console.error('Error parsing response_percentage:', e);
      sanitizedResponsePercentage = 0;
    }

    // Update the teaching course
    const [updatedCourse] = await sql`
      UPDATE uw.merit_report_teaching_courses
      SET
        term_year = ${term_year || 'Unknown Term'},
        course_number = ${course_number || 'Unknown Course'},
        course_title = ${course_title || 'Unknown Title'},
        scp_q1_q3 = ${scp_q1_q3 || '0.00'},
        scp_q1_q3_std_dev = ${scp_q1_q3_std_dev || '0.00'},
        scp_q4_q6 = ${scp_q4_q6 || '0.00'},
        scp_q4_q6_std_dev = ${scp_q4_q6_std_dev || '0.00'},
        num_students = ${num_students || 0},
        response_percentage = ${sanitizedResponsePercentage},
        update_dt = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json(updatedCourse);
  } catch (error) {
    console.error("Error updating teaching course:", error);
    return NextResponse.json(
      { error: "Failed to update teaching course" },
      { status: 500 }
    );
  }
}

// Delete a teaching course
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const reportId = searchParams.get("report_id");

    if (!id || !reportId) {
      return NextResponse.json(
        { error: "Course ID and Report ID are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${parseInt(reportId)}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Check if the course exists and belongs to the report
    const [existingCourse] = await sql`
      SELECT id FROM uw.merit_report_teaching_courses
      WHERE id = ${parseInt(id)}
      AND report_id = ${parseInt(reportId)}
      AND is_deleted = FALSE
    `;

    if (!existingCourse) {
      return NextResponse.json(
        { error: "Course not found or doesn't belong to this report" },
        { status: 404 }
      );
    }

    // Soft delete the teaching course
    await sql`
      UPDATE uw.merit_report_teaching_courses
      SET is_deleted = TRUE, update_dt = NOW()
      WHERE id = ${parseInt(id)}
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${parseInt(reportId)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting teaching course:", error);
    return NextResponse.json(
      { error: "Failed to delete teaching course" },
      { status: 500 }
    );
  }
}
