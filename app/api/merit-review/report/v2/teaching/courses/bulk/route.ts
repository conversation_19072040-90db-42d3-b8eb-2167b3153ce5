import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Bulk add teaching courses
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if faculty_id is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    const { report_id, courses } = await request.json();

    if (!report_id || !courses || !Array.isArray(courses) || courses.length === 0) {
      return NextResponse.json(
        { error: "Report ID and courses array are required" },
        { status: 400 }
      );
    }

    // Check if the report exists and belongs to the faculty
    const [existingReport] = await sql`
      SELECT id FROM uw.merit_report
      WHERE id = ${report_id}
      AND faculty_id = ${session.user.facultyId}
      AND is_deleted = FALSE
    `;

    if (!existingReport) {
      return NextResponse.json(
        { error: "Report not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Prepare values for bulk insert
    const values = courses.map(course => {
      // Sanitize response percentage to ensure it's a valid number
      let sanitizedResponsePercentage = 0;
      if (course.response_percentage !== undefined && course.response_percentage !== null) {
        const parsedValue = parseFloat(course.response_percentage);
        sanitizedResponsePercentage = isNaN(parsedValue) ? 0 : parsedValue;
      }

      return {
        report_id,
        term_year: course.term_year || 'Unknown Term',
        course_number: course.course_number || 'Unknown Course',
        course_title: course.course_title || 'Unknown Title',
        scp_q1_q3: course.scp_q1_q3 || '0.00',
        scp_q1_q3_std_dev: course.scp_q1_q3_std_dev || '0.00',
        scp_q4_q6: course.scp_q4_q6 || '0.00',
        scp_q4_q6_std_dev: course.scp_q4_q6_std_dev || '0.00',
        num_students: course.num_students || 0,
        response_percentage: sanitizedResponsePercentage
      };
    });

    // Perform bulk insert
    const insertedCourses = await sql`
      INSERT INTO uw.merit_report_teaching_courses ${sql(
        values,
        'report_id',
        'term_year',
        'course_number',
        'course_title',
        'scp_q1_q3',
        'scp_q1_q3_std_dev',
        'scp_q4_q6',
        'scp_q4_q6_std_dev',
        'num_students',
        'response_percentage'
      )}
      RETURNING *
    `;

    // Update the main report's update_dt
    await sql`
      UPDATE uw.merit_report
      SET update_dt = NOW()
      WHERE id = ${report_id}
    `;

    return NextResponse.json({
      message: `Successfully added ${insertedCourses.length} courses`,
      courses: insertedCourses
    }, { status: 201 });
  } catch (error) {
    console.error("Error bulk adding teaching courses:", error);
    return NextResponse.json(
      { error: "Failed to bulk add teaching courses" },
      { status: 500 }
    );
  }
}
