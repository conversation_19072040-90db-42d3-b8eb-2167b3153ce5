import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get reviews submitted by the current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT f.faculty_id
      FROM faculty f
      JOIN common.user u ON f.work_email = u.email
      WHERE u.user_id = ${session.user.id}
      AND f.is_deleted = FALSE
    `;

    if (!faculty && !session.user.roles?.includes("system_admin")) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get reviews submitted by the user
    let reviewsQuery;
    
    if (session.user.roles?.includes("system_admin") && !faculty) {
      // For system admin without faculty record, show all reviews
      reviewsQuery = sql`
        SELECT mrr.id, mrr.report_id, mrr.reviewer_id, 
               mrr.teaching_rating, mrr.research_rating, mrr.service_rating, 
               mrr.comments, mrr.created_at, mrr.updated_at,
               f.first_name, f.last_name, f.work_email,
               rf.first_name as reviewer_first_name, rf.last_name as reviewer_last_name
        FROM merit_review_rating mrr
        JOIN merit_report mr ON mrr.report_id = mr.id
        JOIN faculty f ON mr.faculty_id = f.faculty_id
        LEFT JOIN faculty rf ON mrr.reviewer_id = rf.faculty_id
        WHERE mrr.is_deleted = FALSE
        ORDER BY mrr.created_at DESC
        LIMIT 100
      `;
    } else {
      // Get reviews submitted by the faculty member
      reviewsQuery = sql`
        SELECT mrr.id, mrr.report_id, mrr.reviewer_id, 
               mrr.teaching_rating, mrr.research_rating, mrr.service_rating, 
               mrr.comments, mrr.created_at, mrr.updated_at,
               f.first_name, f.last_name, f.work_email
        FROM merit_review_rating mrr
        JOIN merit_report mr ON mrr.report_id = mr.id
        JOIN faculty f ON mr.faculty_id = f.faculty_id
        WHERE mrr.reviewer_id = ${faculty.faculty_id}
        AND mrr.is_deleted = FALSE
        ORDER BY mrr.created_at DESC
      `;
    }

    const reviews = await reviewsQuery;
    
    // Format the reviews for the response
    const formattedReviews = reviews.map(review => ({
      id: review.id,
      report_id: review.report_id,
      reviewer_id: review.reviewer_id,
      faculty_name: `${review.first_name} ${review.last_name}`,
      faculty_email: review.work_email,
      reviewer_name: review.reviewer_first_name && review.reviewer_last_name 
        ? `${review.reviewer_first_name} ${review.reviewer_last_name}`
        : null,
      teaching_rating: review.teaching_rating,
      research_rating: review.research_rating,
      service_rating: review.service_rating,
      comments: review.comments,
      created_at: review.created_at,
      updated_at: review.updated_at,
    }));

    return NextResponse.json(formattedReviews);
  } catch (error) {
    console.error("Error fetching my reviews:", error);
    return NextResponse.json(
      { error: "Failed to fetch my reviews" },
      { status: 500 }
    );
  }
}
