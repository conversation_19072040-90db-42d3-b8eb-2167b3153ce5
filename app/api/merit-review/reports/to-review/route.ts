import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get reports that need to be reviewed by the current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT f.faculty_id
      FROM faculty f
      JOIN common.user u ON f.work_email = u.email
      WHERE u.user_id = ${session.user.id}
      AND f.is_deleted = FALSE
    `;

    if (!faculty && !session.user.roles?.includes("system_admin")) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Check if user is a committee member
    let committeeUnits = [];
    
    if (faculty) {
      const committeeResults = await sql`
        SELECT mrc.unit_id
        FROM merit_review_committee mrc
        WHERE mrc.faculty_id = ${faculty.faculty_id}
        AND mrc.is_deleted = FALSE
      `;
      
      committeeUnits = committeeResults.map(row => row.unit_id);
    }
    
    // If user is not a committee member and not a system admin, return empty array
    if (committeeUnits.length === 0 && !session.user.roles?.includes("system_admin")) {
      return NextResponse.json([]);
    }

    // Get reports that need to be reviewed
    let reportsQuery;
    
    if (session.user.roles?.includes("system_admin")) {
      // System admin can see all reports
      reportsQuery = sql`
        SELECT mr.id, mr.faculty_id, mr.unit_id, mr.report_doc, mr.create_dt, mr.update_dt, mr.status,
               f.first_name, f.last_name, f.work_email,
               u.full_name as unit_name
        FROM merit_report mr
        JOIN faculty f ON mr.faculty_id = f.faculty_id
        JOIN unit u ON mr.unit_id = u.unit_id
        WHERE mr.status = 'submitted'
        AND mr.is_deleted = FALSE
        ORDER BY mr.update_dt DESC
      `;
    } else {
      // Get reports from units where the user is a committee member
      reportsQuery = sql`
        SELECT mr.id, mr.faculty_id, mr.unit_id, mr.report_doc, mr.create_dt, mr.update_dt, mr.status,
               f.first_name, f.last_name, f.work_email,
               u.full_name as unit_name
        FROM merit_report mr
        JOIN faculty f ON mr.faculty_id = f.faculty_id
        JOIN unit u ON mr.unit_id = u.unit_id
        WHERE mr.unit_id IN (${committeeUnits})
        AND mr.status = 'submitted'
        AND mr.faculty_id != ${faculty.faculty_id}
        AND mr.is_deleted = FALSE
        ORDER BY mr.update_dt DESC
      `;
    }

    const allReports = await reportsQuery;
    
    // Filter out reports that the user has already reviewed
    const reviewedReports = await sql`
      SELECT report_id
      FROM merit_review_rating
      WHERE reviewer_id = ${faculty?.faculty_id || 0}
      AND is_deleted = FALSE
    `;
    
    const reviewedReportIds = new Set(reviewedReports.map(r => r.report_id));
    
    // Filter out reports where there's a conflict of interest
    const conflicts = await sql`
      SELECT faculty_id
      FROM merit_conflict_of_interest
      WHERE committee_member_id = ${faculty?.faculty_id || 0}
      AND is_deleted = FALSE
    `;
    
    const conflictFacultyIds = new Set(conflicts.map(c => c.faculty_id));
    
    // Filter the reports
    const reportsToReview = allReports.filter(report => {
      // Skip if already reviewed
      if (reviewedReportIds.has(report.id)) {
        return false;
      }
      
      // Skip if there's a conflict of interest
      if (conflictFacultyIds.has(report.faculty_id)) {
        return false;
      }
      
      return true;
    });

    return NextResponse.json(reportsToReview);
  } catch (error) {
    console.error("Error fetching reports to review:", error);
    return NextResponse.json(
      { error: "Failed to fetch reports to review" },
      { status: 500 }
    );
  }
}
