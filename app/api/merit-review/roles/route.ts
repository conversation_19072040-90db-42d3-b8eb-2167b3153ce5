import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get roles and faculty members with those roles for a specific unit
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const unitId = searchParams.get("unit_id");

    if (!unitId) {
      return NextResponse.json(
        { error: "Unit ID is required" },
        { status: 400 }
      );
    }

    // Check if user has appropriate role
    const roles = session.user.roles || [];
    if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get all institution roles
    const institutionRoles = await sql`
      SELECT role_id, role_name
      FROM uw.institution_role
      WHERE is_deleted = FALSE
      ORDER BY role_name
    `;

    // Get faculty members with roles for the specified unit
    const facultyRoles = await sql`
      SELECT 
        fir.faculty_institution_role_id,
        fir.faculty_id,
        fir.institution_role_id,
        fir.unit_id,
        ir.role_name,
        f.first_name,
        f.last_name,
        f.preferred_name,
        f.work_email
      FROM uw.faculty_institution_role fir
      JOIN uw.institution_role ir ON fir.institution_role_id = ir.role_id
      JOIN uw.faculty f ON fir.faculty_id = f.faculty_id
      WHERE fir.unit_id = ${parseInt(unitId)}
      AND fir.is_deleted = FALSE
      ORDER BY ir.role_name, f.last_name, f.first_name
    `;

    // Get committee members for the specified unit
    const committeeMembers = await sql`
      SELECT 
        mrc.id,
        mrc.faculty_id,
        mrc.committee_name,
        f.first_name,
        f.last_name,
        f.preferred_name,
        f.work_email
      FROM uw.merit_review_committee mrc
      JOIN uw.faculty f ON mrc.faculty_id = f.faculty_id
      WHERE mrc.unit_id = ${parseInt(unitId)}
      AND mrc.is_deleted = FALSE
      ORDER BY f.last_name, f.first_name
    `;

    // Define role functions in the merit review process
    const roleFunctions = {
      "department_admin": [
        "Configure merit review workflow",
        "Manage committee members",
        "View all department reports"
      ],
      "department_approver": [
        "Review faculty submissions",
        "Provide preliminary ratings",
        "Approve final ratings"
      ],
      "faculty_admin": [
        "Configure faculty-level workflows",
        "View all faculty reports",
        "Approve final ratings"
      ],
      "faculty_approver": [
        "Review department recommendations",
        "Provide final approval",
        "Handle disagreements"
      ],
      "regular_user": [
        "Submit merit review reports",
        "View own submissions",
        "Respond to feedback"
      ],
      "committee_member": [
        "Review faculty submissions",
        "Provide ratings and feedback",
        "Participate in committee discussions"
      ]
    };

    return NextResponse.json({
      institutionRoles,
      facultyRoles,
      committeeMembers,
      roleFunctions
    });
  } catch (error) {
    console.error("Error fetching roles:", error);
    return NextResponse.json(
      { error: "Failed to fetch roles data" },
      { status: 500 }
    );
  }
}
