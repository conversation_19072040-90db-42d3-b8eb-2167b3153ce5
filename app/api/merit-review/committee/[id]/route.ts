import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get a specific committee member
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    const [committeeMember] = await sql`
      SELECT mrc.id, mrc.unit_id, mrc.committee_name, mrc.faculty_id,
             f.first_name, f.last_name, f.work_email,
             u.full_name as unit_name
      FROM merit_review_committee mrc
      JOIN faculty f ON mrc.faculty_id = f.faculty_id
      JOIN unit u ON mrc.unit_id = u.unit_id
      WHERE mrc.id = ${parseInt(id)}
      AND mrc.is_deleted = FALSE
    `;

    if (!committeeMember) {
      return NextResponse.json(
        { error: "Committee member not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(committeeMember);
  } catch (error) {
    console.error("Error fetching committee member:", error);
    return NextResponse.json(
      { error: "Failed to fetch committee member" },
      { status: 500 }
    );
  }
}

// Update a committee member
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];
    const { committee_name } = await request.json();

    // Check if committee member exists
    const [existingMember] = await sql`
      SELECT * FROM merit_review_committee
      WHERE id = ${parseInt(id)}
      AND is_deleted = FALSE
    `;

    if (!existingMember) {
      return NextResponse.json(
        { error: "Committee member not found" },
        { status: 404 }
      );
    }

    // Update the committee member
    const [updatedMember] = await sql`
      UPDATE merit_review_committee
      SET
        committee_name = COALESCE(${committee_name}, committee_name),
        updated_at = NOW()
      WHERE id = ${parseInt(id)}
      RETURNING id, unit_id, committee_name, faculty_id
    `;

    return NextResponse.json(updatedMember);
  } catch (error) {
    console.error("Error updating committee member:", error);
    return NextResponse.json(
      { error: "Failed to update committee member" },
      { status: 500 }
    );
  }
}

// Remove a committee member (soft delete)
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Extract the id from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // Check if committee member exists
    const [existingMember] = await sql`
      SELECT * FROM merit_review_committee
      WHERE id = ${parseInt(id)}
      AND is_deleted = FALSE
    `;

    if (!existingMember) {
      return NextResponse.json(
        { error: "Committee member not found" },
        { status: 404 }
      );
    }

    // Soft delete the committee member
    await sql`
      UPDATE merit_review_committee
      SET is_deleted = TRUE, updated_at = NOW()
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({
      message: "Committee member removed successfully",
    });
  } catch (error) {
    console.error("Error removing committee member:", error);
    return NextResponse.json(
      { error: "Failed to remove committee member" },
      { status: 500 }
    );
  }
}
