import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get a specific rating
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the report_id from the query parameters
    const { searchParams } = new URL(request.url);
    const report_id = searchParams.get("report_id");

    if (!report_id) {
      return NextResponse.json(
        { error: "Missing report_id parameter" },
        { status: 400 }
      );
    }

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT faculty_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json({ error: "Faculty not found" }, { status: 404 });
    }

    // Get the rating
    const [rating] = await sql`
      SELECT
        id,
        report_id,
        reviewer_id,
        teaching_rating,
        research_rating,
        service_rating,
        comments,
        is_submitted,
        created_at,
        updated_at
      FROM uw.merit_review_rating
      WHERE report_id = ${report_id}
      AND reviewer_id = ${faculty.faculty_id}
      AND is_deleted = FALSE
    `;

    if (!rating) {
      return NextResponse.json({ error: "Rating not found" }, { status: 404 });
    }

    return NextResponse.json(rating);
  } catch (error) {
    console.error("Error fetching rating:", error);
    return NextResponse.json(
      { error: "Failed to fetch rating" },
      { status: 500 }
    );
  }
}

// Create or update a rating
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const {
      report_id,
      teaching_rating,
      research_rating,
      service_rating,
      comments,
      is_submitted = false
    } = await request.json();

    if (!report_id || teaching_rating === undefined || research_rating === undefined || service_rating === undefined) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT faculty_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json({ error: "Faculty not found" }, { status: 404 });
    }

    // Check if the report exists and get its unit_id
    const [report] = await sql`
      SELECT mr.id, mr.unit_id, mr.faculty_id
      FROM uw.merit_report mr
      WHERE mr.id = ${report_id}
      AND mr.is_deleted = FALSE
    `;

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 });
    }

    // Check if user is a committee member for this unit
    const [isCommitteeMember] = await sql`
      SELECT 1
      FROM uw.merit_review_committee mrc
      WHERE mrc.unit_id = ${report.unit_id}
      AND mrc.faculty_id = ${faculty.faculty_id}
      AND mrc.is_deleted = FALSE
    `;

    if (!isCommitteeMember) {
      return NextResponse.json(
        { error: "You are not a committee member for this unit" },
        { status: 403 }
      );
    }

    // Check if the faculty is trying to rate their own report
    if (report.faculty_id === faculty.faculty_id) {
      return NextResponse.json(
        { error: "You cannot rate your own report" },
        { status: 403 }
      );
    }

    // Check if there's an active workflow and if we're past the deadline
    const [activeWorkflow] = await sql`
      SELECT id, end_dt
      FROM uw.merit_workflow_config
      WHERE unit_id = ${report.unit_id}
      AND status = 'active'
      AND is_deleted = FALSE
      ORDER BY created_at DESC
      LIMIT 1
    `;

    if (activeWorkflow && new Date(activeWorkflow.end_dt) < new Date()) {
      return NextResponse.json(
        { error: "The deadline for submitting ratings has passed" },
        { status: 403 }
      );
    }

    // Check if user has already submitted a rating for this report
    const [existingRating] = await sql`
      SELECT id, is_submitted
      FROM uw.merit_review_rating
      WHERE report_id = ${report_id}
      AND reviewer_id = ${faculty.faculty_id}
      AND is_deleted = FALSE
    `;

    if (existingRating && existingRating.is_submitted) {
      return NextResponse.json(
        { error: "You have already submitted a rating for this report and cannot change it" },
        { status: 403 }
      );
    }

    let rating;
    if (existingRating) {
      // Update the existing rating
      [rating] = await sql`
        UPDATE uw.merit_review_rating
        SET
          teaching_rating = ${teaching_rating},
          research_rating = ${research_rating},
          service_rating = ${service_rating},
          comments = ${comments || null},
          is_submitted = ${is_submitted},
          updated_at = NOW()
        WHERE id = ${existingRating.id}
        RETURNING id, report_id, reviewer_id, teaching_rating, research_rating, service_rating, comments, is_submitted, created_at, updated_at
      `;
    } else {
      // Create a new rating
      [rating] = await sql`
        INSERT INTO uw.merit_review_rating (
          report_id, reviewer_id, teaching_rating, research_rating, service_rating, comments, is_submitted
        ) VALUES (
          ${report_id}, ${faculty.faculty_id}, ${teaching_rating}, ${research_rating}, ${service_rating}, ${comments || null}, ${is_submitted}
        )
        RETURNING id, report_id, reviewer_id, teaching_rating, research_rating, service_rating, comments, is_submitted, created_at, updated_at
      `;
    }

    return NextResponse.json(rating);
  } catch (error) {
    console.error("Error submitting rating:", error);
    return NextResponse.json(
      { error: "Failed to submit rating" },
      { status: 500 }
    );
  }
}
