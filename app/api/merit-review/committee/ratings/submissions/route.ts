import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET(request: NextRequest) {
  try {
    console.log("Committee Ratings Submissions API - Request received");
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      console.log("Committee Ratings Submissions API - Unauthorized: No user ID in session");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("Committee Ratings Submissions API - User email:", session.user?.email);

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT faculty_id, primary_unit_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    if (!faculty) {
      console.log("Committee Ratings Submissions API - Faculty not found for email:", session.user?.email);
      return NextResponse.json({ error: "Faculty not found" }, { status: 404 });
    }

    console.log("Committee Ratings Submissions API - Faculty ID:", faculty.faculty_id);

    // Check if user is a committee member
    const [isCommitteeMember] = await sql`
      SELECT mrc.unit_id, u.level_number
      FROM uw.merit_review_committee mrc
      JOIN uw.unit u ON mrc.unit_id = u.unit_id
      WHERE mrc.faculty_id = ${faculty.faculty_id}
      AND mrc.is_deleted = FALSE
      AND u.is_deleted = FALSE
    `;

    if (!isCommitteeMember) {
      console.log("Committee Ratings Submissions API - User is not a committee member");
      return NextResponse.json(
        { error: "You are not a committee member" },
        { status: 403 }
      );
    }

    console.log("Committee Ratings Submissions API - User is a committee member for unit:", isCommitteeMember.unit_id, "Level:", isCommitteeMember.level_number);

    // Get active workflow for the committee member's unit
    const [activeWorkflow] = await sql`
      SELECT id, end_dt
      FROM uw.merit_workflow_config
      WHERE unit_id = ${isCommitteeMember.unit_id}
      AND status = 'active'
      AND is_deleted = FALSE
      ORDER BY created_at DESC
      LIMIT 1
    `;

    // Get all submissions from the user's committee unit that need review
    // Exclude the user's own submissions
    // If the committee member's unit is level 4, use it directly
    // Otherwise, find all level 4 units related to the committee member's unit
    let submissions;

    if (isCommitteeMember.level_number === 4) {
      // Committee member is already at level 4, get submissions from this unit
      submissions = await sql`
        SELECT
          mr.id,
          mr.faculty_id,
          CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.create_dt,
          mr.update_dt,
          mr.submit_dt,
          mr.status,
          CASE
            WHEN mrr.id IS NULL THEN 'not_started'
            WHEN mrr.is_submitted = TRUE THEN 'submitted'
            ELSE 'in_progress'
          END as rating_status,
          mrr.id as rating_id,
          CAST(mrr.teaching_rating AS FLOAT) as teaching_rating,
          CAST(mrr.research_rating AS FLOAT) as research_rating,
          CAST(mrr.service_rating AS FLOAT) as service_rating,
          mrr.comments,
          mrr.created_at as rating_created_at,
          mrr.updated_at as rating_updated_at,
          mrr.is_submitted,
          ${activeWorkflow ? activeWorkflow.end_dt : null} as deadline
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        LEFT JOIN uw.merit_review_rating mrr ON
          mrr.report_id = mr.id AND
          mrr.reviewer_id = ${faculty.faculty_id} AND
          mrr.is_deleted = FALSE
        WHERE mr.unit_id = ${isCommitteeMember.unit_id}
        AND mr.faculty_id != ${faculty.faculty_id}
        AND mr.is_deleted = FALSE
        AND mr.status = 'submitted'
        ORDER BY mr.create_dt DESC
      `;
    } else if (isCommitteeMember.level_number < 4) {
      // Committee member is at a higher level (1-3), find all level 4 child units
      submissions = await sql`
        WITH RECURSIVE level4_units AS (
          -- Find all level 4 units that are descendants of the committee member's unit
          SELECT unit_id
          FROM uw.unit
          WHERE parent_unit_id = ${isCommitteeMember.unit_id}
          AND level_number = 4
          AND is_deleted = FALSE

          UNION ALL

          SELECT u.unit_id
          FROM uw.unit u
          JOIN uw.unit parent ON u.parent_unit_id = parent.unit_id
          JOIN level4_units l4 ON parent.parent_unit_id = l4.unit_id
          WHERE u.level_number = 4
          AND u.is_deleted = FALSE
        )
        SELECT
          mr.id,
          mr.faculty_id,
          CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.create_dt,
          mr.update_dt,
          mr.submit_dt,
          mr.status,
          CASE
            WHEN mrr.id IS NULL THEN 'not_started'
            WHEN mrr.is_submitted = TRUE THEN 'submitted'
            ELSE 'in_progress'
          END as rating_status,
          mrr.id as rating_id,
          CAST(mrr.teaching_rating AS FLOAT) as teaching_rating,
          CAST(mrr.research_rating AS FLOAT) as research_rating,
          CAST(mrr.service_rating AS FLOAT) as service_rating,
          mrr.comments,
          mrr.created_at as rating_created_at,
          mrr.updated_at as rating_updated_at,
          mrr.is_submitted,
          ${activeWorkflow ? activeWorkflow.end_dt : null} as deadline
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        LEFT JOIN uw.merit_review_rating mrr ON
          mrr.report_id = mr.id AND
          mrr.reviewer_id = ${faculty.faculty_id} AND
          mrr.is_deleted = FALSE
        WHERE mr.unit_id IN (SELECT unit_id FROM level4_units)
        AND mr.faculty_id != ${faculty.faculty_id}
        AND mr.is_deleted = FALSE
        AND mr.status = 'submitted'
        ORDER BY mr.create_dt DESC
      `;
    } else {
      // Committee member is at a lower level (5+), find the parent level 4 unit
      submissions = await sql`
        WITH RECURSIVE parent_level4_unit AS (
          -- Find the level 4 parent of the committee member's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${isCommitteeMember.unit_id}
          AND is_deleted = FALSE

          UNION ALL

          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN parent_level4_unit p ON u.unit_id = p.parent_unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT
          mr.id,
          mr.faculty_id,
          CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
          mr.unit_id,
          u.full_name as unit_name,
          mr.create_dt,
          mr.update_dt,
          mr.submit_dt,
          mr.status,
          CASE
            WHEN mrr.id IS NULL THEN 'not_started'
            WHEN mrr.is_submitted = TRUE THEN 'submitted'
            ELSE 'in_progress'
          END as rating_status,
          mrr.id as rating_id,
          CAST(mrr.teaching_rating AS FLOAT) as teaching_rating,
          CAST(mrr.research_rating AS FLOAT) as research_rating,
          CAST(mrr.service_rating AS FLOAT) as service_rating,
          mrr.comments,
          mrr.created_at as rating_created_at,
          mrr.updated_at as rating_updated_at,
          mrr.is_submitted,
          ${activeWorkflow ? activeWorkflow.end_dt : null} as deadline
        FROM uw.merit_report mr
        JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
        JOIN uw.unit u ON mr.unit_id = u.unit_id
        LEFT JOIN uw.merit_review_rating mrr ON
          mrr.report_id = mr.id AND
          mrr.reviewer_id = ${faculty.faculty_id} AND
          mrr.is_deleted = FALSE
        WHERE mr.unit_id = (
          SELECT unit_id FROM parent_level4_unit
          WHERE level_number = 4
          LIMIT 1
        )
        AND mr.faculty_id != ${faculty.faculty_id}
        AND mr.is_deleted = FALSE
        AND mr.status = 'submitted'
        ORDER BY mr.create_dt DESC
      `;
    }

    return NextResponse.json(submissions);
  } catch (error) {
    console.error("Error fetching submissions for rating:", error);
    return NextResponse.json(
      { error: "Failed to fetch submissions" },
      { status: 500 }
    );
  }
}
