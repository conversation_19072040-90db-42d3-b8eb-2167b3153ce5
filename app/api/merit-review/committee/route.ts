import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get committee members
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userUnitId = searchParams.get("user_unit_id");

    // Check if user has appropriate role
    const roles = session.user.roles || [];
    if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    let query;
    if (roles.includes("system_admin")) {
      // System admin can see all committee members
      query = sql`
        SELECT mrc.*, f.full_name as faculty_name, u.full_name as unit_name
        FROM uw.merit_review_committee mrc
        JOIN uw.faculty f ON mrc.faculty_id = f.faculty_id
        JOIN uw.unit u ON mrc.unit_id = u.unit_id
        WHERE mrc.is_deleted = FALSE
        ORDER BY u.full_name, f.full_name
      `;
    } else if (roles.includes("faculty_admin")) {
      // For faculty_admin, get committee members for their unit and all child units
      query = sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the user's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${parseInt(userUnitId || "0")}
          AND is_deleted = FALSE
          
          UNION ALL
          
          -- Get all child units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT DISTINCT mrc.*, f.full_name as faculty_name, u.full_name as unit_name
        FROM uw.merit_review_committee mrc
        JOIN uw.faculty f ON mrc.faculty_id = f.faculty_id
        JOIN uw.unit u ON mrc.unit_id = u.unit_id
        JOIN unit_hierarchy uh ON mrc.unit_id = uh.unit_id
        WHERE mrc.is_deleted = FALSE
        ORDER BY u.full_name, f.full_name
      `;
    } else if (roles.includes("department_admin")) {
      // For department_admin, get committee members for their unit only
      query = sql`
        SELECT mrc.*, f.full_name as faculty_name, u.full_name as unit_name
        FROM uw.merit_review_committee mrc
        JOIN uw.faculty f ON mrc.faculty_id = f.faculty_id
        JOIN uw.unit u ON mrc.unit_id = u.unit_id
        WHERE mrc.unit_id = ${parseInt(userUnitId || "0")}
        AND mrc.is_deleted = FALSE
        ORDER BY f.full_name
      `;
    }

    const committeeMembers = await query;
    return NextResponse.json(committeeMembers);
  } catch (error) {
    console.error("Error fetching committee members:", error);
    return NextResponse.json(
      { error: "Failed to fetch committee members" },
      { status: 500 }
    );
  }
}

// Add a new committee member
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { unit_id, faculty_id } = await request.json();

    if (!unit_id || !faculty_id) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if faculty member is already in the committee
    const existingMember = await sql`
      SELECT id FROM uw.merit_review_committee
      WHERE unit_id = ${unit_id}
      AND faculty_id = ${faculty_id}
      AND is_deleted = FALSE
    `;

    if (existingMember.length > 0) {
      return NextResponse.json(
        { error: "Faculty member is already in the committee" },
        { status: 409 }
      );
    }

    // Add the new committee member
    const [newMember] = await sql`
      INSERT INTO uw.merit_review_committee (
        unit_id, faculty_id
      ) VALUES (
        ${unit_id}, ${faculty_id}
      )
      RETURNING id, unit_id, faculty_id
    `;

    return NextResponse.json(newMember, { status: 201 });
  } catch (error) {
    console.error("Error adding committee member:", error);
    return NextResponse.json(
      { error: "Failed to add committee member" },
      { status: 500 }
    );
  }
}

// Remove a committee member
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Missing committee member ID" },
        { status: 400 }
      );
    }

    // Soft delete the committee member
    await sql`
      UPDATE uw.merit_review_committee
      SET is_deleted = TRUE
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing committee member:", error);
    return NextResponse.json(
      { error: "Failed to remove committee member" },
      { status: 500 }
    );
  }
}
