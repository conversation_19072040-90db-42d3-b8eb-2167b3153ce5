import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ isMember: false }, { status: 401 });
    }

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT faculty_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json({ isMember: false }, { status: 200 });
    }

    // Check if user is a committee member
    const [isCommitteeMember] = await sql`
      SELECT unit_id
      FROM uw.merit_review_committee
      WHERE faculty_id = ${faculty.faculty_id}
      AND is_deleted = FALSE
    `;

    return NextResponse.json({ 
      isMember: !!isCommitteeMember,
      unitId: isCommitteeMember?.unit_id || null
    }, { status: 200 });
  } catch (error) {
    console.error("Error checking committee membership:", error);
    return NextResponse.json({ isMember: false }, { status: 500 });
  }
}
