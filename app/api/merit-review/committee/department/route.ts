import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get committee members and regular faculty for a department admin's unit
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get the department admin's primary unit
    const [adminFaculty] = await sql`
      SELECT faculty_id, primary_unit_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
        AND is_deleted = FALSE
      LIMIT 1
    `;

    if (!adminFaculty) {
      return NextResponse.json(
        { error: "Admin faculty record not found" },
        { status: 404 }
      );
    }

    const unitId = adminFaculty.primary_unit_id;

    // Get the unit name
    const [unit] = await sql`
      SELECT full_name
      FROM uw.unit
      WHERE unit_id = ${unitId}
        AND is_deleted = FALSE
      LIMIT 1
    `;

    // Get all regular faculty in the department
    const regularFaculty = await sql`
      SELECT
        f.faculty_id,
        f.first_name,
        f.last_name,
        CONCAT(f.first_name, ' ', f.last_name) as full_name,
        f.work_email
      FROM uw.faculty f
      WHERE f.primary_unit_id = ${unitId}
        AND f.job_family = 'Regular Faculty'
        AND f.is_deleted = FALSE
      ORDER BY f.last_name, f.first_name
    `;

    // Get current committee members
    const committeeMembers = await sql`
      SELECT
        mrc.id,
        mrc.faculty_id,
        CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
        mrc.created_at
      FROM uw.merit_review_committee mrc
      JOIN uw.faculty f ON mrc.faculty_id = f.faculty_id
      WHERE mrc.unit_id = ${unitId}
        AND mrc.is_deleted = FALSE
      ORDER BY f.last_name, f.first_name
    `;

    return NextResponse.json({
      unitId,
      unitName: unit?.full_name || "",
      committeeName: `${unit?.full_name || ""} Merit Review Committee`,
      regularFaculty,
      committeeMembers
    });
  } catch (error) {
    console.error("Error fetching committee data:", error);
    return NextResponse.json(
      { error: "Failed to fetch committee data" },
      { status: 500 }
    );
  }
}

// Add a new committee member
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { unit_id, faculty_id } = await request.json();

    if (!unit_id || !faculty_id) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the unit name for committee_name
    const [unit] = await sql`
      SELECT full_name
      FROM uw.unit
      WHERE unit_id = ${unit_id}
        AND is_deleted = FALSE
      LIMIT 1
    `;

    if (!unit) {
      return NextResponse.json(
        { error: "Unit not found" },
        { status: 404 }
      );
    }

    const committee_name = `${unit.full_name} Merit Review Committee`;

    // Check if faculty member is already in the committee
    const existingMember = await sql`
      SELECT id FROM uw.merit_review_committee
      WHERE unit_id = ${unit_id}
      AND faculty_id = ${faculty_id}
      AND is_deleted = FALSE
    `;

    if (existingMember.length > 0) {
      return NextResponse.json(
        { error: "Faculty member is already in the committee" },
        { status: 409 }
      );
    }

    // Add the new committee member
    const [newMember] = await sql`
      INSERT INTO uw.merit_review_committee (
        unit_id, committee_name, faculty_id
      ) VALUES (
        ${unit_id}, ${committee_name}, ${faculty_id}
      )
      RETURNING id, unit_id, committee_name, faculty_id
    `;

    // Get the faculty member details
    const [faculty] = await sql`
      SELECT
        faculty_id,
        CONCAT(first_name, ' ', last_name) as faculty_name
      FROM uw.faculty
      WHERE faculty_id = ${faculty_id}
        AND is_deleted = FALSE
      LIMIT 1
    `;

    return NextResponse.json({
      ...newMember,
      faculty_name: faculty?.faculty_name || ""
    });
  } catch (error) {
    console.error("Error adding committee member:", error);
    return NextResponse.json(
      { error: "Failed to add committee member" },
      { status: 500 }
    );
  }
}

// Remove a committee member
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Missing committee member ID" },
        { status: 400 }
      );
    }

    // Check if committee member exists
    const [existingMember] = await sql`
      SELECT * FROM uw.merit_review_committee
      WHERE id = ${parseInt(id)}
      AND is_deleted = FALSE
    `;

    if (!existingMember) {
      return NextResponse.json(
        { error: "Committee member not found" },
        { status: 404 }
      );
    }

    // Soft delete the committee member
    await sql`
      UPDATE uw.merit_review_committee
      SET is_deleted = TRUE, updated_at = NOW()
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({
      message: "Committee member removed successfully",
    });
  } catch (error) {
    console.error("Error removing committee member:", error);
    return NextResponse.json(
      { error: "Failed to remove committee member" },
      { status: 500 }
    );
  }
}
