import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user's unit ID and check if they are a committee member
    const [userUnit] = await sql`
      SELECT u.unit_id, u.parent_id
      FROM uw.unit u
      JOIN uw.faculty f ON f.primary_unit_id = u.unit_id
      WHERE f.faculty_id = ${session.user.id}
    `;

    if (!userUnit) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get all submissions from the user's unit that need review
    const submissions = await sql`
      SELECT 
        mr.id,
        mr.faculty_id,
        f.full_name as faculty_name,
        mr.unit_id,
        u.full_name as unit_name,
        mr.report_doc,
        mr.create_dt,
        mr.update_dt,
        mr.status,
        (
          SELECT json_agg(
            json_build_object(
              'id', mrr.id,
              'teaching_rating', mrr.teaching_rating,
              'research_rating', mrr.research_rating,
              'service_rating', mrr.service_rating,
              'comments', mrr.comments,
              'created_at', mrr.created_at
            )
          )
          FROM uw.merit_review_ratings mrr
          WHERE mrr.merit_report_id = mr.id
        ) as ratings
      FROM uw.merit_report mr
      JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
      JOIN uw.unit u ON mr.unit_id = u.unit_id
      WHERE mr.unit_id = ${userUnit.unit_id}
      AND mr.is_deleted = FALSE
      AND mr.status = 'submitted'
      ORDER BY mr.create_dt DESC
    `;

    return NextResponse.json(submissions);
  } catch (error) {
    console.error("Error fetching submissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch submissions" },
      { status: 500 }
    );
  }
} 