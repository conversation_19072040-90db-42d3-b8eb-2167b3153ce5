import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const {
      merit_report_id,
      teaching_rating,
      research_rating,
      service_rating,
      comments,
    } = await request.json();

    if (!merit_report_id || !teaching_rating || !research_rating || !service_rating) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if the user is a committee member for this submission
    const [submission] = await sql`
      SELECT mr.unit_id
      FROM uw.merit_report mr
      JOIN uw.unit u ON mr.unit_id = u.unit_id
      WHERE mr.id = ${merit_report_id}
      AND mr.is_deleted = FALSE
    `;

    if (!submission) {
      return NextResponse.json(
        { error: "Submission not found" },
        { status: 404 }
      );
    }

    // Check if user is a committee member for this unit
    const [isCommitteeMember] = await sql`
      SELECT 1
      FROM uw.merit_review_committee mrc
      WHERE mrc.unit_id = ${submission.unit_id}
      AND mrc.faculty_id = ${session.user.id}
      AND mrc.is_deleted = FALSE
    `;

    if (!isCommitteeMember) {
      return NextResponse.json(
        { error: "You are not a committee member for this unit" },
        { status: 403 }
      );
    }

    // Check if user has already rated this submission
    const [existingRating] = await sql`
      SELECT id
      FROM uw.merit_review_rating
      WHERE report_id = ${merit_report_id}
      AND reviewer_id = ${session.user.id}
      AND is_deleted = FALSE
    `;

    if (existingRating) {
      return NextResponse.json(
        { error: "You have already rated this submission" },
        { status: 409 }
      );
    }

    // Create the new rating
    const [newRating] = await sql`
      INSERT INTO uw.merit_review_rating (
        report_id,
        reviewer_id,
        teaching_rating,
        research_rating,
        service_rating,
        comments,
        is_submitted
      ) VALUES (
        ${merit_report_id},
        ${session.user.id},
        ${teaching_rating},
        ${research_rating},
        ${service_rating},
        ${comments},
        TRUE
      )
      RETURNING id, report_id, reviewer_id, teaching_rating, research_rating, service_rating, comments, created_at, is_submitted
    `;

    return NextResponse.json(newRating, { status: 201 });
  } catch (error) {
    console.error("Error submitting rating:", error);
    return NextResponse.json(
      { error: "Failed to submit rating" },
      { status: 500 }
    );
  }
}