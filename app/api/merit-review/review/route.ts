import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get reviews for a specific report
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get("report_id");

    if (!reportId) {
      return NextResponse.json(
        { error: "Report ID is required" },
        { status: 400 }
      );
    }

    // Check if user has appropriate role to view reviews
    const roles = session.user.roles || [];
    const canViewReviews =
      roles.includes("department_admin") ||
      roles.includes("department_support") ||
      roles.includes("department_approver") ||
      roles.includes("faculty_admin") ||
      roles.includes("faculty_approver") ||
      roles.includes("system_admin");

    if (!canViewReviews) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get the reviews for the report
    const reviews = await sql`
      SELECT mrr.id, mrr.report_id, mrr.reviewer_id, 
             mrr.teaching_rating, mrr.research_rating, mrr.service_rating, 
             mrr.comments, mrr.created_at, mrr.updated_at,
             f.first_name, f.last_name
      FROM merit_review_rating mrr
      JOIN faculty f ON mrr.reviewer_id = f.faculty_id
      WHERE mrr.report_id = ${parseInt(reportId)}
      AND mrr.is_deleted = FALSE
      ORDER BY mrr.created_at DESC
    `;

    return NextResponse.json(reviews);
  } catch (error) {
    console.error("Error fetching reviews:", error);
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 }
    );
  }
}

// Submit a review for a report
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT f.faculty_id
      FROM faculty f
      JOIN common.user u ON f.work_email = u.email
      WHERE u.user_id = ${session.user.id}
      AND f.is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Check if user is a committee member
    const [committeeMember] = await sql`
      SELECT id
      FROM merit_review_committee
      WHERE faculty_id = ${faculty.faculty_id}
      AND is_deleted = FALSE
    `;

    if (!committeeMember && !session.user.roles?.includes("system_admin")) {
      return NextResponse.json(
        { error: "User is not a committee member" },
        { status: 403 }
      );
    }

    const { report_id, teaching_rating, research_rating, service_rating, comments } =
      await request.json();

    if (!report_id || !teaching_rating || !research_rating || !service_rating) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if the report exists and is in the correct status
    const [report] = await sql`
      SELECT mr.id, mr.faculty_id
      FROM merit_report mr
      WHERE mr.id = ${report_id}
      AND mr.status = 'submitted'
      AND mr.is_deleted = FALSE
    `;

    if (!report) {
      return NextResponse.json(
        { error: "Report not found or not in submitted status" },
        { status: 404 }
      );
    }

    // Check for conflicts of interest
    const [conflict] = await sql`
      SELECT id
      FROM merit_conflict_of_interest
      WHERE committee_member_id = ${faculty.faculty_id}
      AND faculty_id = ${report.faculty_id}
      AND is_deleted = FALSE
    `;

    if (conflict) {
      return NextResponse.json(
        { error: "Conflict of interest exists" },
        { status: 409 }
      );
    }

    // Check if the reviewer has already reviewed this report
    const [existingReview] = await sql`
      SELECT id
      FROM merit_review_rating
      WHERE report_id = ${report_id}
      AND reviewer_id = ${faculty.faculty_id}
      AND is_deleted = FALSE
    `;

    let review;

    if (existingReview) {
      // Update the existing review
      [review] = await sql`
        UPDATE merit_review_rating
        SET 
          teaching_rating = ${teaching_rating},
          research_rating = ${research_rating},
          service_rating = ${service_rating},
          comments = ${comments || null},
          updated_at = NOW()
        WHERE id = ${existingReview.id}
        RETURNING id, report_id, reviewer_id, teaching_rating, research_rating, service_rating, comments, created_at, updated_at
      `;
    } else {
      // Create a new review
      [review] = await sql`
        INSERT INTO merit_review_rating (
          report_id, reviewer_id, teaching_rating, research_rating, service_rating, comments
        ) VALUES (
          ${report_id}, ${faculty.faculty_id}, ${teaching_rating}, ${research_rating}, ${service_rating}, ${comments || null}
        )
        RETURNING id, report_id, reviewer_id, teaching_rating, research_rating, service_rating, comments, created_at, updated_at
      `;
    }

    // Check if all committee members have reviewed this report
    const committeeMembers = await sql`
      SELECT mrc.faculty_id
      FROM merit_review_committee mrc
      JOIN merit_report mr ON mrc.unit_id = mr.unit_id
      LEFT JOIN merit_conflict_of_interest mcoi ON 
        mcoi.committee_member_id = mrc.faculty_id AND 
        mcoi.faculty_id = mr.faculty_id AND 
        mcoi.is_deleted = FALSE
      WHERE mr.id = ${report_id}
      AND mrc.is_deleted = FALSE
      AND mcoi.id IS NULL
    `;

    const reviews = await sql`
      SELECT DISTINCT reviewer_id
      FROM merit_review_rating
      WHERE report_id = ${report_id}
      AND is_deleted = FALSE
    `;

    // If all eligible committee members have reviewed, update report status
    if (reviews.length >= committeeMembers.length) {
      await sql`
        UPDATE merit_report
        SET status = 'reviewed'
        WHERE id = ${report_id}
      `;
    }

    return NextResponse.json(review);
  } catch (error) {
    console.error("Error submitting review:", error);
    return NextResponse.json(
      { error: "Failed to submit review" },
      { status: 500 }
    );
  }
}
