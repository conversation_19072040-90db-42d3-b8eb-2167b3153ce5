import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get preliminary ratings for a unit head
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_approver or department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_approver") && !roles.includes("department_admin") &&
        !roles.includes("faculty_admin") && !roles.includes("faculty_approver")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get the faculty ID and unit for the current user
    const [faculty] = await sql`
      SELECT faculty_id, primary_unit_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json({ error: "Faculty not found" }, { status: 404 });
    }

    // Get all submissions from the user's unit that need preliminary ratings
    const submissions = await sql`
      SELECT
        mr.id,
        mr.faculty_id,
        CONCAT(f.first_name, ' ', f.last_name) as faculty_name,
        mr.unit_id,
        u.full_name as unit_name,
        mr.create_dt,
        mr.update_dt,
        mr.submit_dt,
        mr.status,
        CASE
          WHEN mpr.id IS NULL THEN 'not_started'
          WHEN mpr.is_submitted = TRUE THEN 'submitted'
          ELSE 'in_progress'
        END as rating_status,
        mpr.id as rating_id,
        mpr.teaching_rating,
        mpr.research_rating,
        mpr.service_rating,
        mpr.comments,
        mpr.created_at as rating_created_at,
        mpr.updated_at as rating_updated_at,
        mpr.is_submitted
      FROM uw.merit_report mr
      JOIN uw.faculty f ON mr.faculty_id = f.faculty_id
      JOIN uw.unit u ON mr.unit_id = u.unit_id
      LEFT JOIN uw.merit_preliminary_rating mpr ON
        mpr.report_id = mr.id AND
        mpr.unit_head_id = ${faculty.faculty_id} AND
        mpr.is_deleted = FALSE
      WHERE mr.unit_id = ${faculty.primary_unit_id}
      AND mr.is_deleted = FALSE
      AND mr.status = 'under_review'
      ORDER BY mr.create_dt DESC
    `;

    return NextResponse.json(submissions);
  } catch (error) {
    console.error("Error fetching submissions for preliminary rating:", error);
    return NextResponse.json(
      { error: "Failed to fetch submissions" },
      { status: 500 }
    );
  }
}

// Create or update a preliminary rating
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_approver or department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_approver") && !roles.includes("department_admin") &&
        !roles.includes("faculty_approver")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const {
      report_id,
      teaching_rating,
      research_rating,
      service_rating,
      comments,
      is_submitted = false
    } = await request.json();

    if (!report_id || teaching_rating === undefined || research_rating === undefined || service_rating === undefined) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the faculty ID for the current user
    const [faculty] = await sql`
      SELECT faculty_id
      FROM uw.faculty
      WHERE work_email = ${session.user?.email || ''}
      AND is_deleted = FALSE
    `;

    if (!faculty) {
      return NextResponse.json({ error: "Faculty not found" }, { status: 404 });
    }

    // Check if the report exists and get its unit_id
    const [report] = await sql`
      SELECT mr.id, mr.unit_id
      FROM uw.merit_report mr
      WHERE mr.id = ${report_id}
      AND mr.is_deleted = FALSE
    `;

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 });
    }

    // Check if user has already submitted a preliminary rating for this report
    const [existingRating] = await sql`
      SELECT id, is_submitted
      FROM uw.merit_preliminary_rating
      WHERE report_id = ${report_id}
      AND unit_head_id = ${faculty.faculty_id}
      AND is_deleted = FALSE
    `;

    if (existingRating && existingRating.is_submitted) {
      return NextResponse.json(
        { error: "You have already submitted a preliminary rating for this report and cannot change it" },
        { status: 403 }
      );
    }

    let rating;
    if (existingRating) {
      // Update the existing rating
      [rating] = await sql`
        UPDATE uw.merit_preliminary_rating
        SET
          teaching_rating = ${teaching_rating},
          research_rating = ${research_rating},
          service_rating = ${service_rating},
          comments = ${comments || null},
          is_submitted = ${is_submitted},
          updated_at = NOW()
        WHERE id = ${existingRating.id}
        RETURNING id, report_id, unit_head_id, teaching_rating, research_rating, service_rating, comments, is_submitted, created_at, updated_at
      `;
    } else {
      // Create a new rating
      [rating] = await sql`
        INSERT INTO uw.merit_preliminary_rating (
          report_id, unit_head_id, teaching_rating, research_rating, service_rating, comments, is_submitted
        ) VALUES (
          ${report_id}, ${faculty.faculty_id}, ${teaching_rating}, ${research_rating}, ${service_rating}, ${comments || null}, ${is_submitted}
        )
        RETURNING id, report_id, unit_head_id, teaching_rating, research_rating, service_rating, comments, is_submitted, created_at, updated_at
      `;
    }

    return NextResponse.json(rating);
  } catch (error) {
    console.error("Error submitting preliminary rating:", error);
    return NextResponse.json(
      { error: "Failed to submit preliminary rating" },
      { status: 500 }
    );
  }
}
