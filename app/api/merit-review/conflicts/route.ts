import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get all conflicts of interest
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userUnitId = searchParams.get("user_unit_id");

    // Check if user has appropriate role
    const roles = session.user.roles || [];
    if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    let query;
    if (roles.includes("system_admin")) {
      // System admin can see all conflicts
      query = sql`
        SELECT coi.*, f.full_name as faculty_name, cm.faculty_id as committee_member_faculty_id
        FROM uw.conflict_of_interest coi
        JOIN uw.faculty f ON coi.faculty_id = f.faculty_id
        JOIN uw.merit_review_committee cm ON coi.committee_member_id = cm.id
        WHERE coi.is_deleted = FALSE
        ORDER BY f.full_name
      `;
    } else if (roles.includes("faculty_admin")) {
      // For faculty_admin, get conflicts for their unit and all child units
      query = sql`
        WITH RECURSIVE unit_hierarchy AS (
          -- Start with the user's unit
          SELECT unit_id, parent_unit_id, level_number
          FROM uw.unit
          WHERE unit_id = ${parseInt(userUnitId || "0")}
          AND is_deleted = FALSE
          
          UNION ALL
          
          -- Get all child units
          SELECT u.unit_id, u.parent_unit_id, u.level_number
          FROM uw.unit u
          JOIN unit_hierarchy uh ON u.parent_unit_id = uh.unit_id
          WHERE u.is_deleted = FALSE
        )
        SELECT DISTINCT coi.*, f.full_name as faculty_name, cm.faculty_id as committee_member_faculty_id
        FROM uw.conflict_of_interest coi
        JOIN uw.faculty f ON coi.faculty_id = f.faculty_id
        JOIN uw.merit_review_committee cm ON coi.committee_member_id = cm.id
        JOIN unit_hierarchy uh ON cm.unit_id = uh.unit_id
        WHERE coi.is_deleted = FALSE
        ORDER BY f.full_name
      `;
    } else if (roles.includes("department_admin")) {
      // For department_admin, get conflicts for their unit only
      query = sql`
        SELECT coi.*, f.full_name as faculty_name, cm.faculty_id as committee_member_faculty_id
        FROM uw.conflict_of_interest coi
        JOIN uw.faculty f ON coi.faculty_id = f.faculty_id
        JOIN uw.merit_review_committee cm ON coi.committee_member_id = cm.id
        WHERE cm.unit_id = ${parseInt(userUnitId || "0")}
        AND coi.is_deleted = FALSE
        ORDER BY f.full_name
      `;
    }

    const conflicts = await query;
    return NextResponse.json(conflicts);
  } catch (error) {
    console.error("Error fetching conflicts of interest:", error);
    return NextResponse.json(
      { error: "Failed to fetch conflicts of interest" },
      { status: 500 }
    );
  }
}

// Add a new conflict of interest
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { committee_member_id, faculty_id, reason } = await request.json();

    if (!committee_member_id || !faculty_id || !reason) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if conflict already exists
    const existingConflict = await sql`
      SELECT id FROM uw.conflict_of_interest
      WHERE committee_member_id = ${committee_member_id}
      AND faculty_id = ${faculty_id}
      AND is_deleted = FALSE
    `;

    if (existingConflict.length > 0) {
      return NextResponse.json(
        { error: "Conflict of interest already exists" },
        { status: 409 }
      );
    }

    // Add the new conflict of interest
    const [newConflict] = await sql`
      INSERT INTO uw.conflict_of_interest (
        committee_member_id, faculty_id, reason
      ) VALUES (
        ${committee_member_id}, ${faculty_id}, ${reason}
      )
      RETURNING id, committee_member_id, faculty_id, reason, created_at
    `;

    return NextResponse.json(newConflict, { status: 201 });
  } catch (error) {
    console.error("Error adding conflict of interest:", error);
    return NextResponse.json(
      { error: "Failed to add conflict of interest" },
      { status: 500 }
    );
  }
}

// Remove a conflict of interest
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has department_admin role
    const roles = session.user.roles || [];
    if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Missing conflict of interest ID" },
        { status: 400 }
      );
    }

    // Soft delete the conflict of interest
    await sql`
      UPDATE uw.conflict_of_interest
      SET is_deleted = TRUE
      WHERE id = ${parseInt(id)}
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing conflict of interest:", error);
    return NextResponse.json(
      { error: "Failed to remove conflict of interest" },
      { status: 500 }
    );
  }
} 