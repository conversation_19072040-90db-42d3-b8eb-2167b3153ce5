import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';
import { sql } from '@/app/lib/db';

// Get supervision data for the current faculty user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if facultyId is available in the session
    if (!session.user.facultyId) {
      return NextResponse.json(
        { error: "User is not a faculty member" },
        { status: 404 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;
    const startTerm = searchParams.get('start_term') ? parseInt(searchParams.get('start_term')!) : undefined;
    const endTerm = searchParams.get('end_term') ? parseInt(searchParams.get('end_term')!) : undefined;

    // Build the query
    let query = sql`
      SELECT
        sd.supervision_id,
        sd.term,
        sd.faculty,
        sd.department,
        sd.student_id,
        sd.student_name,
        sd.academic_plan,
        sd.supervisor_last_name,
        sd.supervisor_first_name,
        sd.citizenship
      FROM quest.supervision_data sd
      WHERE sd.faculty_id = ${session.user.facultyId}
    `;

    // Add term filters if provided
    if (startTerm) {
      query = sql`${query} AND sd.term >= ${startTerm}`;
    }
    
    if (endTerm) {
      query = sql`${query} AND sd.term <= ${endTerm}`;
    }

    // Add order by and limit
    query = sql`${query} ORDER BY sd.term DESC`;
    
    if (limit) {
      query = sql`${query} LIMIT ${limit}`;
    }

    const result = await query;

    // Count supervision by academic plan/level
    const undergraduateCount = result.filter(s => 
      s.academic_plan && s.academic_plan.toLowerCase().includes('undergraduate')).length;
    
    const mastersCount = result.filter(s => 
      s.academic_plan && (
        s.academic_plan.toLowerCase().includes('master') || 
        s.academic_plan.toLowerCase().includes('masc') ||
        s.academic_plan.toLowerCase().includes('meng')
      )).length;
    
    const phdCount = result.filter(s => 
      s.academic_plan && (
        s.academic_plan.toLowerCase().includes('phd') || 
        s.academic_plan.toLowerCase().includes('doctor')
      )).length;
    
    // We don't have specific identifiers for PDF, visitors in the data
    // These would need to be determined by other means
    
    // Return both the raw data and the summary counts
    return NextResponse.json({
      supervisions: result,
      summary: {
        undergrad_supervised: undergraduateCount,
        masters_supervised: mastersCount,
        phd_supervised: phdCount,
        // Default others to 0 as we don't have that data
        pdf_supervised: 0,
        visitors_supervised: 0,
        // Co-supervised counts would need additional data
        undergrad_cosupervised: 0,
        masters_cosupervised: 0,
        phd_cosupervised: 0,
        pdf_cosupervised: 0,
        visitors_cosupervised: 0
      }
    });
  } catch (error) {
    console.error('Error fetching faculty supervision data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch supervision data' },
      { status: 500 }
    );
  }
}
