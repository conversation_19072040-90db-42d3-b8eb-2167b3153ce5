import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';
import { GoogleScholarPublicationRepository } from '@/lib/repositories/google-scholar-publication-repository';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');
    const facultyId = searchParams.get('facultyId');
    const getAllFaculty = searchParams.get('getAllFaculty') === 'true';
    const sortBy = searchParams.get('sortBy') as 'year' | 'citations' || 'year';

    const publicationRepo = new GoogleScholarPublicationRepository();

    // Check if user is admin
    const isAdmin = session.user.roles?.includes('system_admin') ||
                    session.user.roles?.includes('institution_admin');

    if (isAdmin) {
      // If facultyId is provided, get publications for that faculty
      if (facultyId) {
        const facultyIdNum = parseInt(facultyId);
        const publications = await publicationRepo.getPublicationsByFacultyId(facultyIdNum, sortBy);
        const facultyDetails = await publicationRepo.getFacultyDetailsById(facultyIdNum);

        return NextResponse.json({
          publications,
          totalCount: publications.length,
          facultyDetails,
          page: 1,
          pageSize: publications.length,
          totalPages: 1
        });
      } else if (getAllFaculty) {
        // Get all faculty without pagination for client-side filtering
        const { faculty, totalCount } = await publicationRepo.getFacultyList(1, 1000); // Large page size to get all

        return NextResponse.json({
          faculty,
          totalCount,
          page: 1,
          pageSize: faculty.length,
          totalPages: 1
        });
      } else {
        // Get paginated faculty list
        const { faculty, totalCount } = await publicationRepo.getFacultyList(page, pageSize);

        return NextResponse.json({
          faculty,
          totalCount,
          page,
          pageSize,
          totalPages: Math.ceil(totalCount / pageSize)
        });
      }
    } else {
      // Regular users can only see their own publications
      const userEmail = session.user.email;

      if (!userEmail) {
        return NextResponse.json({ error: 'User email not found' }, { status: 400 });
      }

      try {
        const publications = await publicationRepo.getPublicationsByEmail(userEmail, sortBy);
        const totalCount = await publicationRepo.getPublicationsCountByEmail(userEmail);

        return NextResponse.json({
          publications,
          totalCount,
          page: 1,
          pageSize: publications.length,
          totalPages: 1
        });
      } catch (error) {
        console.error('Error fetching user publications:', error);
        // Return empty publications if user is not a faculty member or has no publications
        return NextResponse.json({
          publications: [],
          totalCount: 0,
          page: 1,
          pageSize: 0,
          totalPages: 0,
          message: 'No publications found for this user'
        });
      }
    }
  } catch (error) {
    console.error('Error fetching publications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch publications' },
      { status: 500 }
    );
  }
}
