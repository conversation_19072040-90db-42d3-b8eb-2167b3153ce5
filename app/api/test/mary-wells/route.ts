import { NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';

export async function GET() {
  try {
    const email = '<EMAIL>';
    console.log(`Simulating profile fetch for: ${email}`);

    // First check if the user exists in the faculty table
    const facultyCheck = await sql`
      SELECT faculty_id, first_name, last_name
      FROM uw.faculty
      WHERE work_email = ${email}
        AND is_deleted = FALSE
    `;

    if (facultyCheck.length === 0) {
      console.log(`No faculty record found for email: ${email}`);
      return NextResponse.json({ error: 'Faculty not found' }, { status: 404 });
    }

    const facultyId = facultyCheck[0].faculty_id;
    console.log(`Found faculty with ID: ${facultyId}, name: ${facultyCheck[0].first_name} ${facultyCheck[0].last_name}`);

    // Fetch Google Scholar profile for the faculty member
    const result = await sql`
      SELECT
        ap.scholar_id,
        ap.name,
        ap.affiliation,
        ap.areas_of_interest,
        ap.citations_all,
        ap.h_index_all,
        ap.profile_url,
        ap.profile_image_url
      FROM googlescholar.author_profile ap
      WHERE ap.faculty_id = ${facultyId}
      LIMIT 1
    `;

    if (result.length === 0) {
      console.log(`No Google Scholar profile found for faculty ID: ${facultyId}`);
      return NextResponse.json({ error: 'Google Scholar profile not found' }, { status: 404 });
    }

    const profile = result[0];
    console.log(`Found Google Scholar profile with scholar_id: ${profile.scholar_id}`);
    console.log(`Profile image URL: ${profile.profile_image_url}`);

    // Return the profile data
    return NextResponse.json({
      faculty: {
        id: facultyId,
        firstName: facultyCheck[0].first_name,
        lastName: facultyCheck[0].last_name,
        email: email
      },
      scholarProfile: profile
    });
  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}
