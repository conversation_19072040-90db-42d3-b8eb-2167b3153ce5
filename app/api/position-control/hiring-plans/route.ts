import { getServerSession } from "next-auth/next";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";
import { z } from "zod";

const HiringPlanSchema = z.object({
  plan_year: z.number().min(2020).max(2050),
  reference_code: z.string().min(1).max(100),
  department_id: z.number(),
  position_description: z.string().optional(),
  career_path: z.enum(['tenure_stream', 'teaching_stream', 'research_faculty']).optional(),
  priority_level: z.number().min(1).max(10).optional(),
  budget_allocation: z.number().optional(),
  status: z.string().default('active'),
});

/**
 * GET handler for hiring plans
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw forbidden("Authentication required");
    }

    const { searchParams } = new URL(request.url);
    const year = searchParams.get('year');
    const department_id = searchParams.get('department_id');
    const career_path = searchParams.get('career_path');
    const status = searchParams.get('status') || 'active';

    const roles = session.user.roles || [];

    // Check permissions
    if (!roles.includes('system_admin') &&
        !roles.includes('faculty_admin') &&
        !roles.includes('department_admin')) {
      throw forbidden("You don't have permission to access hiring plans");
    }

    let whereClause = 'WHERE hp.is_deleted = FALSE';
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Add filters
    if (year) {
      whereClause += ` AND hp.plan_year = $${paramIndex}`;
      queryParams.push(parseInt(year));
      paramIndex++;
    }

    if (department_id) {
      whereClause += ` AND hp.department_id = $${paramIndex}`;
      queryParams.push(parseInt(department_id));
      paramIndex++;
    }

    if (career_path) {
      whereClause += ` AND hp.career_path = $${paramIndex}`;
      queryParams.push(career_path);
      paramIndex++;
    }

    if (status) {
      whereClause += ` AND hp.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    // For department admins, only show plans for their department
    if (roles.includes('department_admin') && !roles.includes('faculty_admin') && !roles.includes('system_admin')) {
      const userDepartment = await sql`
        SELECT primary_unit_id FROM uw.faculty WHERE faculty_id = ${session.user.id}
      `;

      if (userDepartment.length > 0) {
        whereClause += ` AND hp.department_id = $${paramIndex}`;
        queryParams.push(userDepartment[0].primary_unit_id);
        paramIndex++;
      }
    }

    const query = `
      SELECT
        hp.*,
        u.full_name as department_name,
        u.short_name as department_short_name,
        COUNT(fpr.id) as active_requests
      FROM uw.hiring_plans hp
      LEFT JOIN uw.unit u ON hp.department_id = u.unit_id
      LEFT JOIN uw.faculty_position_requests fpr ON hp.reference_code = fpr.hiring_plan_reference
        AND fpr.is_deleted = FALSE
        AND fpr.status NOT IN ('rejected', 'completed')
      ${whereClause}
      GROUP BY hp.id, u.full_name, u.short_name
      ORDER BY hp.plan_year DESC, hp.priority_level ASC, hp.reference_code ASC
    `;

    const hiringPlans = await sql.unsafe(query, queryParams);

    // Get summary statistics
    const summaryQuery = `
      SELECT
        hp.plan_year,
        COUNT(*) as total_plans,
        COUNT(CASE WHEN hp.status = 'active' THEN 1 END) as active_plans,
        SUM(hp.budget_allocation) as total_budget,
        COUNT(fpr.id) as total_requests
      FROM uw.hiring_plans hp
      LEFT JOIN uw.faculty_position_requests fpr ON hp.reference_code = fpr.hiring_plan_reference
        AND fpr.is_deleted = FALSE
      ${whereClause.replace('hp.', 'hp.')}
      GROUP BY hp.plan_year
      ORDER BY hp.plan_year DESC
    `;

    const summary = await sql.unsafe(summaryQuery, queryParams);

    return NextResponse.json({
      hiring_plans: hiringPlans,
      summary
    });

  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

/**
 * POST handler to create new hiring plan
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw forbidden("Authentication required");
    }

    const roles = session.user.roles || [];

    // Check permissions - only faculty admin and system admin can create hiring plans
    if (!roles.includes('system_admin') && !roles.includes('faculty_admin')) {
      throw forbidden("You don't have permission to create hiring plans");
    }

    const body = await request.json();
    const validatedData = HiringPlanSchema.parse(body);

    // Check if reference code already exists for this year
    const existingPlan = await sql`
      SELECT id FROM uw.hiring_plans
      WHERE plan_year = ${validatedData.plan_year}
      AND reference_code = ${validatedData.reference_code}
      AND is_deleted = FALSE
    `;

    if (existingPlan.length > 0) {
      return NextResponse.json(
        { error: "A hiring plan with this reference code already exists for this year" },
        { status: 409 }
      );
    }

    // Verify department exists
    const department = await sql`
      SELECT unit_id FROM uw.unit WHERE unit_id = ${validatedData.department_id}
    `;

    if (department.length === 0) {
      return NextResponse.json(
        { error: "Department not found" },
        { status: 404 }
      );
    }

    // Create the hiring plan
    const [newPlan] = await sql`
      INSERT INTO uw.hiring_plans (
        plan_year,
        reference_code,
        department_id,
        position_description,
        career_path,
        priority_level,
        budget_allocation,
        status
      ) VALUES (
        ${validatedData.plan_year},
        ${validatedData.reference_code},
        ${validatedData.department_id},
        ${validatedData.position_description || null},
        ${validatedData.career_path || null},
        ${validatedData.priority_level || null},
        ${validatedData.budget_allocation || null},
        ${validatedData.status}
      )
      RETURNING *
    `;

    // Get the created plan with department info
    const [planWithDepartment] = await sql`
      SELECT
        hp.*,
        u.full_name as department_name,
        u.short_name as department_short_name
      FROM uw.hiring_plans hp
      LEFT JOIN uw.unit u ON hp.department_id = u.unit_id
      WHERE hp.id = ${newPlan.id}
    `;

    return NextResponse.json(planWithDepartment, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

/**
 * PUT handler to update hiring plan
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw forbidden("Authentication required");
    }

    const roles = session.user.roles || [];

    // Check permissions
    if (!roles.includes('system_admin') && !roles.includes('faculty_admin')) {
      throw forbidden("You don't have permission to update hiring plans");
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json({ error: "Plan ID is required" }, { status: 400 });
    }

    // Validate update data
    const validatedData = HiringPlanSchema.partial().parse(updateData);

    // Check if plan exists
    const [existingPlan] = await sql`
      SELECT * FROM uw.hiring_plans
      WHERE id = ${id} AND is_deleted = FALSE
    `;

    if (!existingPlan) {
      return NextResponse.json({ error: "Hiring plan not found" }, { status: 404 });
    }

    // If updating reference code or year, check for duplicates
    if (validatedData.reference_code || validatedData.plan_year) {
      const checkYear = validatedData.plan_year || existingPlan.plan_year;
      const checkCode = validatedData.reference_code || existingPlan.reference_code;

      const duplicate = await sql`
        SELECT id FROM uw.hiring_plans
        WHERE plan_year = ${checkYear}
        AND reference_code = ${checkCode}
        AND id != ${id}
        AND is_deleted = FALSE
      `;

      if (duplicate.length > 0) {
        return NextResponse.json(
          { error: "A hiring plan with this reference code already exists for this year" },
          { status: 409 }
        );
      }
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    for (const [key, value] of Object.entries(validatedData)) {
      updateFields.push(`${key} = $${paramIndex}`);
      updateValues.push(value);
      paramIndex++;
    }

    updateFields.push(`updated_at = NOW()`);

    const updateQuery = `
      UPDATE uw.hiring_plans
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    updateValues.push(id);

    const [updatedPlan] = await sql.unsafe(updateQuery, updateValues);

    // Get updated plan with department info
    const [planWithDepartment] = await sql`
      SELECT
        hp.*,
        u.full_name as department_name,
        u.short_name as department_short_name
      FROM uw.hiring_plans hp
      LEFT JOIN uw.unit u ON hp.department_id = u.unit_id
      WHERE hp.id = ${updatedPlan.id}
    `;

    return NextResponse.json(planWithDepartment);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
