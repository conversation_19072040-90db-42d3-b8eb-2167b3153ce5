import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";

/**
 * GET handler for position details data
 */
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check authorization
    if (!session?.user?.roles?.includes("system_admin") && 
        !session?.user?.roles?.includes("faculty_admin")) {
      throw forbidden("You don't have permission to access position control data");
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const level04 = searchParams.get('level_04');
    const jobFamily = searchParams.get('job_family');
    const staffingStatus = searchParams.get('staffing_status');
    const searchQuery = searchParams.get('search');

    // Build the query with filters
    let query = `
      SELECT
        id,
        position_name,
        reference_id,
        worker_type,
        employee_type,
        time_type,
        staffing_status,
        available_for_hire,
        worker,
        contract_end_date,
        business_title,
        job_profile,
        frozen,
        freeze_date,
        freeze_reason,
        previous_incumbent,
        position_vacate_date,
        fte,
        job_family,
        job_family_groups,
        cost_center,
        class_indicator,
        manager,
        supervisory_organization,
        level_03,
        level_04,
        level_05,
        level_06,
        level_07,
        import_date
      FROM workday.position_details
      WHERE 1=1
    `;

    const queryParams: any[] = [];
    let paramIndex = 1;

    // Add filters if provided
    if (level04) {
      query += ` AND level_04 = $${paramIndex++}`;
      queryParams.push(level04);
    }

    if (jobFamily) {
      query += ` AND job_family = $${paramIndex++}`;
      queryParams.push(jobFamily);
    }

    if (staffingStatus) {
      query += ` AND staffing_status = $${paramIndex++}`;
      queryParams.push(staffingStatus);
    }

    if (searchQuery) {
      query += ` AND (
        position_name ILIKE $${paramIndex} OR
        worker ILIKE $${paramIndex} OR
        business_title ILIKE $${paramIndex} OR
        job_profile ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${searchQuery}%`);
      paramIndex++;
    }

    // Add order by
    query += ` ORDER BY reference_id`;

    // Execute the query
    const result = await sql.unsafe(query, queryParams);

    return NextResponse.json(result);
  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
