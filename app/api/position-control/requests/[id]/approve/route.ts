import { getServerSession } from "next-auth/next";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";
import { z } from "zod";

const ApprovalSchema = z.object({
  action: z.enum(['approve', 'reject']),
  comments: z.string().optional(),
  signature: z.string().optional(),
});

// Define the approval workflow steps and required roles
const APPROVAL_WORKFLOW = {
  'submitted': {
    next_status: 'unit_head_review',
    required_roles: ['department_admin', 'department_support'],
    approval_step: 'unit_head_approved'
  },
  'unit_head_review': {
    next_status: 'donna_review',
    required_roles: ['department_admin', 'department_support'],
    approval_step: 'unit_head_approved'
  },
  'donna_review': {
    next_status: 'veronica_review',
    required_roles: ['faculty_admin', 'faculty_support'], // <PERSON>'s role
    approval_step: 'donna_approved'
  },
  'veronica_review': {
    next_status: 'dean_review',
    required_roles: ['faculty_admin', 'faculty_support'], // Veronica's role
    approval_step: 'veronica_approved'
  },
  'dean_review': {
    next_status: 'provost_review',
    required_roles: ['faculty_admin', 'dean'], // Dean's role
    approval_step: 'dean_approved'
  },
  'provost_review': {
    next_status: 'completed',
    required_roles: ['division_admin', 'provost'], // Provost's role
    approval_step: 'provost_approved'
  }
};

/**
 * POST handler for approving/rejecting position requests
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw forbidden("Authentication required");
    }

    const userId = session.user.id;
    const resolvedParams = await params;
    const requestId = parseInt(resolvedParams.id);
    if (isNaN(requestId)) {
      return NextResponse.json({ error: "Invalid request ID" }, { status: 400 });
    }

    const body = await request.json();
    const { action, comments, signature } = ApprovalSchema.parse(body);

    // Get the current request
    const [currentRequest] = await sql`
      SELECT * FROM uw.faculty_position_requests
      WHERE id = ${requestId} AND is_deleted = FALSE
    `;

    if (!currentRequest) {
      return NextResponse.json({ error: "Request not found" }, { status: 404 });
    }

    const currentStatus = currentRequest.status;
    const roles = session.user.roles || [];

    // Check if the current status allows approval
    const workflowStep = APPROVAL_WORKFLOW[currentStatus as keyof typeof APPROVAL_WORKFLOW];
    if (!workflowStep) {
      return NextResponse.json(
        { error: "Request is not in a state that allows approval" },
        { status: 400 }
      );
    }

    // Check if user has required role for this approval step
    const hasRequiredRole = workflowStep.required_roles.some(role => roles.includes(role));
    if (!hasRequiredRole && !roles.includes('system_admin')) {
      throw forbidden("You don't have permission to approve at this step");
    }

    // For unit head approval, also check if user is the unit head
    if (currentStatus === 'unit_head_review' || currentStatus === 'submitted') {
      const isUnitHead = currentRequest.home_unit_head_id === userId ||
                        currentRequest.second_unit_head_id === userId;

      if (!isUnitHead && !roles.includes('system_admin') && !roles.includes('faculty_admin')) {
        throw forbidden("Only the unit head can approve at this step");
      }
    }

    let newStatus: string;
    let approvalStep: string;

    if (action === 'approve') {
      newStatus = workflowStep.next_status;
      approvalStep = workflowStep.approval_step;
    } else {
      newStatus = 'rejected';
      approvalStep = 'rejected';
    }

    // Start transaction
    await sql.begin(async (sql) => {
      // Update the request status
      await sql`
        UPDATE uw.faculty_position_requests
        SET
          status = ${newStatus},
          updated_at = NOW()
        WHERE id = ${requestId}
      `;

      // Record the approval/rejection
      if (action === 'approve') {
        await sql`
          INSERT INTO uw.request_approvals (
            request_id,
            approval_step,
            approver_id,
            approved_at,
            comments,
            signature
          ) VALUES (
            ${requestId},
            ${approvalStep},
            ${userId},
            NOW(),
            ${comments || null},
            ${signature || null}
          )
        `;
      } else {
        await sql`
          INSERT INTO uw.request_approvals (
            request_id,
            approval_step,
            approver_id,
            rejected_at,
            comments,
            signature
          ) VALUES (
            ${requestId},
            ${approvalStep},
            ${userId},
            NOW(),
            ${comments || null},
            ${signature || null}
          )
        `;
      }

      // If approved and moving to provost review, generate forms
      if (action === 'approve' && newStatus === 'provost_review') {
        // TODO: Trigger form generation
        // This would call a service to generate the Mission Critical Form and Authorization to Advertise Form
      }

      // If provost approved, generate final documents and distribute
      if (action === 'approve' && newStatus === 'completed') {
        // TODO: Generate final approval package and distribute to stakeholders
        // This would include position number assignment and mission critical number
      }
    });

    // Get updated request with related data
    const [updatedRequest] = await sql`
      SELECT
        fpr.*,
        hd.full_name as home_department_name,
        sd.full_name as second_department_name,
        creator.first_name || ' ' || creator.last_name as created_by_name
      FROM uw.faculty_position_requests fpr
      LEFT JOIN uw.unit hd ON fpr.home_department_id = hd.unit_id
      LEFT JOIN uw.unit sd ON fpr.second_department_id = sd.unit_id
      LEFT JOIN uw.faculty creator ON fpr.created_by = creator.faculty_id
      WHERE fpr.id = ${requestId}
    `;

    // Get approval history
    const approvals = await sql`
      SELECT
        ra.*,
        f.first_name || ' ' || f.last_name as approver_name
      FROM uw.request_approvals ra
      LEFT JOIN uw.faculty f ON ra.approver_id = f.faculty_id
      WHERE ra.request_id = ${requestId}
      ORDER BY ra.created_at ASC
    `;

    return NextResponse.json({
      request: updatedRequest,
      approvals,
      message: action === 'approve' ? 'Request approved successfully' : 'Request rejected'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

/**
 * GET handler to get approval history for a request
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw forbidden("Authentication required");
    }

    const resolvedParams = await params;
    const requestId = parseInt(resolvedParams.id);
    if (isNaN(requestId)) {
      return NextResponse.json({ error: "Invalid request ID" }, { status: 400 });
    }

    // Check if user has access to this request
    const [positionRequest] = await sql`
      SELECT * FROM uw.faculty_position_requests
      WHERE id = ${requestId} AND is_deleted = FALSE
    `;

    if (!positionRequest) {
      return NextResponse.json({ error: "Request not found" }, { status: 404 });
    }

    const roles = session.user.roles || [];
    const userId = session.user.id;

    // Check access permissions
    const hasAccess =
      roles.includes('system_admin') ||
      roles.includes('faculty_admin') ||
      positionRequest.created_by === userId ||
      positionRequest.home_unit_head_id === userId ||
      positionRequest.second_unit_head_id === userId;

    if (!hasAccess) {
      throw forbidden("You don't have permission to view this request");
    }

    // Get approval history
    const approvals = await sql`
      SELECT
        ra.*,
        f.first_name || ' ' || f.last_name as approver_name,
        f.work_email as approver_email
      FROM uw.request_approvals ra
      LEFT JOIN uw.faculty f ON ra.approver_id = f.faculty_id
      WHERE ra.request_id = ${requestId}
      ORDER BY ra.created_at ASC
    `;

    // Get current workflow step info
    const currentStatus = positionRequest.status;
    const workflowStep = APPROVAL_WORKFLOW[currentStatus as keyof typeof APPROVAL_WORKFLOW];

    const canApprove = workflowStep && (
      workflowStep.required_roles.some(role => roles.includes(role)) ||
      roles.includes('system_admin') ||
      (currentStatus === 'unit_head_review' &&
       (positionRequest.home_unit_head_id === userId || positionRequest.second_unit_head_id === userId))
    );

    return NextResponse.json({
      approvals,
      current_status: currentStatus,
      can_approve: canApprove,
      next_step: workflowStep?.next_status || null,
      required_roles: workflowStep?.required_roles || []
    });

  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
