import { getServerSession } from "next-auth/next";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";
import { z } from "zod";

// Validation schema for position requests
const PositionRequestSchema = z.object({
  // Hiring Unit(s) Information
  is_joint_appointment: z.boolean().default(false),
  home_department_id: z.number(),
  home_department_percentage: z.number().min(0).max(100).default(100),
  second_department_id: z.number().nullable().optional(),
  second_department_percentage: z.number().min(0).max(100).nullable().optional(),

  // Position Details
  career_path: z.enum(['tenure_stream', 'teaching_stream', 'research_faculty']),
  position_type: z.enum(['new_position', 'replacement']),

  // New position details
  new_position_type: z.enum(['addition_to_operating_complement', 'not_in_complement']).nullable().optional(),
  hiring_plan_reference: z.string().nullable().optional(),

  // Replacement position details
  existing_position_number: z.string().nullable().optional(),
  replacement_reason: z.enum(['resignation', 'retirement', 'termination', 'death', 'other']).nullable().optional(),
  replacement_reason_other: z.string().nullable().optional(),
  incumbent_name: z.string().nullable().optional(),
  incumbent_employee_id: z.string().nullable().optional(),
  termination_date: z.string().nullable().optional(),
  is_bridge_position: z.boolean().default(false),
  bridge_position_number: z.string().nullable().optional(),
  bridge_end_date: z.string().nullable().optional(),

  // Common details
  funding_sources: z.string().nullable().optional(),
  position_notes: z.string().nullable().optional(),

  // Advertisement
  position_title: z.string().max(500),
  advertisement_body: z.string().refine((val) => {
    // Count words for validation (same logic as frontend)
    const words = val.trim().split(/\s+/).filter(word => word.length > 0);
    return words.length <= 1000;
  }, {
    message: "Advertisement body must be 1000 words or less"
  }),
});

/**
 * GET handler for position requests
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.facultyId) {
      throw forbidden("Authentication required");
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const department_id = searchParams.get('department_id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    const roles = session.user.roles || [];
    const userId = session.user.facultyId;

    // Build query based on user roles
    let whereClause = 'WHERE fpr.is_deleted = FALSE';
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Role-based filtering
    if (roles.includes('system_admin') || roles.includes('faculty_admin')) {
      // Can see all requests
    } else if (roles.includes('department_admin') || roles.includes('department_support')) {
      // Can see requests from their department
      whereClause += ` AND (fpr.home_department_id IN (
        SELECT unit_id FROM uw.unit WHERE unit_id = (
          SELECT primary_unit_id FROM uw.faculty WHERE faculty_id = $${paramIndex}
        )
      ) OR fpr.second_department_id IN (
        SELECT unit_id FROM uw.unit WHERE unit_id = (
          SELECT primary_unit_id FROM uw.faculty WHERE faculty_id = $${paramIndex}
        )
      ))`;
      queryParams.push(userId);
      paramIndex++;
    } else {
      // Regular users can only see their own requests
      whereClause += ` AND fpr.created_by = $${paramIndex}`;
      queryParams.push(userId);
      paramIndex++;
    }

    // Additional filters
    if (status) {
      whereClause += ` AND fpr.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    if (department_id) {
      whereClause += ` AND (fpr.home_department_id = $${paramIndex} OR fpr.second_department_id = $${paramIndex})`;
      queryParams.push(parseInt(department_id));
      paramIndex++;
    }

    const query = `
      SELECT
        fpr.*,
        hd.full_name as home_department_name,
        sd.full_name as second_department_name,
        huh.first_name || ' ' || huh.last_name as home_unit_head_name,
        suh.first_name || ' ' || suh.last_name as second_unit_head_name,
        creator.first_name || ' ' || creator.last_name as created_by_name,
        hp.reference_code as hiring_plan_code,
        hp.position_description as hiring_plan_description
      FROM uw.faculty_position_requests fpr
      LEFT JOIN uw.unit hd ON fpr.home_department_id = hd.unit_id
      LEFT JOIN uw.unit sd ON fpr.second_department_id = sd.unit_id
      LEFT JOIN uw.faculty huh ON fpr.home_unit_head_id = huh.faculty_id
      LEFT JOIN uw.faculty suh ON fpr.second_unit_head_id = suh.faculty_id
      LEFT JOIN uw.faculty creator ON fpr.created_by = creator.faculty_id
      LEFT JOIN uw.hiring_plans hp ON fpr.hiring_plan_reference = hp.reference_code
      ${whereClause}
      ORDER BY fpr.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const requests = await sql.unsafe(query, queryParams);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM uw.faculty_position_requests fpr
      ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2); // Remove limit and offset
    const [{ total }] = await sql.unsafe(countQuery, countParams);

    return NextResponse.json({
      requests,
      pagination: {
        page,
        limit,
        total: parseInt(total),
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

/**
 * POST handler to create new position request
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.facultyId) {
      throw forbidden("Authentication required");
    }

    const body = await request.json();

    // Transform empty strings to null for optional fields to ensure consistency
    const cleanedBody = {
      ...body,
      hiring_plan_reference: body.hiring_plan_reference === '' ? null : body.hiring_plan_reference,
      existing_position_number: body.existing_position_number === '' ? null : body.existing_position_number,
      replacement_reason_other: body.replacement_reason_other === '' ? null : body.replacement_reason_other,
      incumbent_name: body.incumbent_name === '' ? null : body.incumbent_name,
      incumbent_employee_id: body.incumbent_employee_id === '' ? null : body.incumbent_employee_id,
      termination_date: body.termination_date === '' ? null : body.termination_date,
      bridge_position_number: body.bridge_position_number === '' ? null : body.bridge_position_number,
      bridge_end_date: body.bridge_end_date === '' ? null : body.bridge_end_date,
      funding_sources: body.funding_sources === '' ? null : body.funding_sources,
      position_notes: body.position_notes === '' ? null : body.position_notes,
    };

    const validatedData = PositionRequestSchema.parse(cleanedBody);

    // Validate joint appointment data
    if (validatedData.is_joint_appointment) {
      if (!validatedData.second_department_id || !validatedData.second_department_percentage) {
        return NextResponse.json(
          { error: "Second department and percentage required for joint appointments" },
          { status: 400 }
        );
      }
      if (validatedData.home_department_percentage + validatedData.second_department_percentage !== 100) {
        return NextResponse.json(
          { error: "Department percentages must total 100%" },
          { status: 400 }
        );
      }
    }

    // Validate position type specific fields
    if (validatedData.position_type === 'new_position') {
      if (!validatedData.new_position_type || !validatedData.hiring_plan_reference) {
        return NextResponse.json(
          { error: "New position type and hiring plan reference required for new positions" },
          { status: 400 }
        );
      }
    } else if (validatedData.position_type === 'replacement') {
      if (!validatedData.existing_position_number || !validatedData.replacement_reason) {
        return NextResponse.json(
          { error: "Position number and replacement reason required for replacement positions" },
          { status: 400 }
        );
      }
    }

    // Get unit heads for the departments
    const homeUnitHead = await sql`
      SELECT faculty_id FROM uw.faculty
      WHERE primary_unit_id = ${validatedData.home_department_id}
      AND rank_name LIKE '%Head%' OR rank_name LIKE '%Chair%' OR rank_name LIKE '%Director%'
      LIMIT 1
    `;

    let secondUnitHead = null;
    if (validatedData.second_department_id) {
      const secondUnitHeadResult = await sql`
        SELECT faculty_id FROM uw.faculty
        WHERE primary_unit_id = ${validatedData.second_department_id}
        AND rank_name LIKE '%Head%' OR rank_name LIKE '%Chair%' OR rank_name LIKE '%Director%'
        LIMIT 1
      `;
      secondUnitHead = secondUnitHeadResult[0]?.faculty_id || null;
    }

    // Create the position request
    const [newRequest] = await sql`
      INSERT INTO uw.faculty_position_requests (
        created_by,
        is_joint_appointment,
        home_department_id,
        home_department_percentage,
        home_unit_head_id,
        second_department_id,
        second_department_percentage,
        second_unit_head_id,
        career_path,
        position_type,
        new_position_type,
        hiring_plan_reference,
        existing_position_number,
        replacement_reason,
        replacement_reason_other,
        incumbent_name,
        incumbent_employee_id,
        termination_date,
        is_bridge_position,
        bridge_position_number,
        bridge_end_date,
        funding_sources,
        position_notes,
        position_title,
        advertisement_body
      ) VALUES (
        ${session.user.facultyId},
        ${validatedData.is_joint_appointment},
        ${validatedData.home_department_id},
        ${validatedData.home_department_percentage},
        ${homeUnitHead[0]?.faculty_id || null},
        ${validatedData.second_department_id || null},
        ${validatedData.second_department_percentage || null},
        ${secondUnitHead},
        ${validatedData.career_path},
        ${validatedData.position_type},
        ${validatedData.new_position_type || null},
        ${validatedData.hiring_plan_reference || null},
        ${validatedData.existing_position_number || null},
        ${validatedData.replacement_reason || null},
        ${validatedData.replacement_reason_other || null},
        ${validatedData.incumbent_name || null},
        ${validatedData.incumbent_employee_id || null},
        ${validatedData.termination_date || null},
        ${validatedData.is_bridge_position},
        ${validatedData.bridge_position_number || null},
        ${validatedData.bridge_end_date || null},
        ${validatedData.funding_sources || null},
        ${validatedData.position_notes || null},
        ${validatedData.position_title},
        ${validatedData.advertisement_body}
      )
      RETURNING *
    `;

    return NextResponse.json(newRequest, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
