import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";

/**
 * GET handler for position filter options
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    // Check authorization
    if (!session?.user?.roles?.includes("system_admin") && 
        !session?.user?.roles?.includes("faculty_admin")) {
      throw forbidden("You don't have permission to access position control data");
    }

    // Fetch distinct values for filters
    const jobFamilies = await sql`
      SELECT DISTINCT job_family
      FROM workday.position_details
      WHERE job_family IS NOT NULL
      ORDER BY job_family
    `;

    const level04Units = await sql`
      SELECT DISTINCT level_04
      FROM workday.position_details
      WHERE level_04 IS NOT NULL
      ORDER BY level_04
    `;

    const staffingStatuses = await sql`
      SELECT DISTINCT staffing_status
      FROM workday.position_details
      WHERE staffing_status IS NOT NULL
      ORDER BY staffing_status
    `;

    return NextResponse.json({
      jobFamilies: jobFamilies.map(item => item.job_family),
      level04Units: level04Units.map(item => item.level_04),
      staffingStatuses: staffingStatuses.map(item => item.staffing_status)
    });
  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
