import { NextResponse } from 'next/server';
import { sql } from "@/app/lib/db";

export async function GET() {
  try {
    const result = await sql`
      SELECT 
        c.committee_id,
        c.name,
        c.short_name,
        c.primary_unit_id,
        c.effective_date,
        c.previous_name,
        c.description,
        c.is_deleted,
        u.full_name as unit_name
      FROM uw.committee c
      LEFT JOIN uw.unit u ON c.primary_unit_id = u.unit_id
      ORDER BY c.name
    `;
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching committees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch committee data' },
      { status: 500 }
    );
  }
} 