import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("system_admin") &&
      !session?.user?.roles?.includes("institution_admin")) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  try {
    const result = await sql`
      SELECT unit_id, full_name, level_number, parent_unit_id
      FROM uw.unit
      ORDER BY level_number, full_name
    `;

    // Convert the result to a plain array of objects
    const units = result.map(row => ({
      unit_id: Number(row.unit_id),
      full_name: String(row.full_name),
      level_number: Number(row.level_number),
      parent_unit_id: row.parent_unit_id ? Number(row.parent_unit_id) : null
    }));

    return NextResponse.json(units);
  } catch (err) {
    console.error('Error fetching units:', err);
    return NextResponse.json(
      { error: err instanceof Error ? err.message : "Failed to fetch units" },
      { status: 500 }
    );
  }
} 