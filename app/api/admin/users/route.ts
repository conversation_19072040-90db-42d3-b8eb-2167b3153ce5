import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";
import bcrypt from "bcryptjs";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    // Check authorization
    if (!session?.user?.roles?.includes("system_admin")) {
      throw forbidden("You don't have permission to view users");
    }

    // Fetch all users with their roles
    const users = await sql`
      SELECT 
        u.user_id,
        u.email,
        u.name,
        u.created_at,
        array_agg(DISTINCT r.role_name) as system_roles,
        array_agg(DISTINCT ir.role_name) as institution_roles
      FROM common.user u
      LEFT JOIN common.user_role ur ON u.user_id = ur.user_id AND ur.deleted_at IS NULL
      LEFT JOIN common.role r ON ur.role_id = r.role_id
      LEFT JOIN uw.faculty f ON u.email = f.work_email AND f.is_deleted = false
      LEFT JOIN uw.faculty_institution_role fir ON f.faculty_id = fir.faculty_id AND fir.is_deleted = false
      LEFT JOIN uw.institution_role ir ON fir.institution_role_id = ir.role_id
      WHERE u.deleted_at IS NULL
      GROUP BY u.user_id, u.email, u.name, u.created_at
      ORDER BY u.created_at DESC
    `;

    return NextResponse.json(users);
  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check authorization
    if (!session?.user?.roles?.includes("system_admin")) {
      throw forbidden("You don't have permission to create users");
    }

    const { email, name, password } = await request.json();

    if (!email || !name || !password) {
      return NextResponse.json(
        { error: "Email, name, and password are required" },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await sql`
      SELECT user_id FROM common.user WHERE email = ${email}
    `;

    if (existingUser.length > 0) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Start a transaction
    await sql`BEGIN`;

    try {
      // Insert into common.user
      const userResult = await sql`
        INSERT INTO common.user (email, name)
        VALUES (${email}, ${name})
        RETURNING user_id
      `;

      const userId = userResult[0].user_id;

      // Insert into common.password
      await sql`
        INSERT INTO common.password (user_id, password_hash)
        VALUES (${userId}, ${hashedPassword})
      `;

      await sql`COMMIT`;

      return NextResponse.json({ 
        message: "User created successfully",
        userId 
      });
    } catch (error) {
      await sql`ROLLBACK`;
      throw error;
    }
  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
} 