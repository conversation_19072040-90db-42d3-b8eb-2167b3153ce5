import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import * as XLSX from 'xlsx';

const BATCH_SIZE = 500;

interface FacultyData {
  intelicampus_id: string;
  sso_id: string;
  first_name: string;
  last_name: string;
  work_email: string;
  primary_unit_id: number;
  primary_unit_percentage: number;
  date_started: Date;
  tenure_status: string;
  position_id: number;
  job_family: string;
}

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("system_admin")) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    console.log('Starting faculty import process...');

    // Read the file content
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(worksheet) as Record<string, any>[];

    console.log(`Read ${data.length} rows from Excel file`);
    console.log('First row sample:', data[0]);

    if (!data || data.length === 0) {
      return NextResponse.json({ error: "No data found in the file" }, { status: 400 });
    }

    // Get all existing unit IDs
    const existingUnits = await sql`
      SELECT unit_id FROM uw.unit
    `;
    const existingUnitIds = new Set(existingUnits.map((unit: any) => unit.unit_id));

    // Process rows in batches
    const batches: FacultyData[][] = [];
    let currentBatch: FacultyData[] = [];
    let totalFaculty = 0;
    let skippedNonPrimary = 0;
    let skippedInvalidUnit = 0;

    for (const [index, row] of data.entries()) {
      try {
        // Only process rows where Primary Job (Y/N) is 'Yes'
        if (row['Primary Job (Y/N)'] !== 'Yes') {
          skippedNonPrimary++;
          continue;
        }

        const primaryUnitId = parseInt(row['Department ID']);
        
        // Skip if primary_unit_id doesn't exist in uw.unit table
        if (!existingUnitIds.has(primaryUnitId)) {
          console.log(`Skipping faculty ${row['First Name']} ${row['Last Name']} - Invalid unit ID: ${primaryUnitId}`);
          skippedInvalidUnit++;
          continue;
        }

        console.log(`Processing faculty: ${row['First Name']} ${row['Last Name']}`);
        
        const facultyData: FacultyData = {
          intelicampus_id: row['Operator ID'],
          sso_id: row['Operator ID'],
          first_name: row['First Name'],
          last_name: row['Last Name'],
          work_email: row['Email - Work'],
          primary_unit_id: primaryUnitId,
          primary_unit_percentage: parseFloat(row['FTE']),
          date_started: new Date(row['Hire Date']),
          tenure_status: row['Tenure Status'],
          position_id: parseInt(row['Position ID']),
          job_family: row['Job Family']
        };

        currentBatch.push(facultyData);
        totalFaculty++;

        // If we've reached the batch size, add the batch to batches array
        if (currentBatch.length >= BATCH_SIZE) {
          console.log(`Creating batch of ${currentBatch.length} faculty members`);
          batches.push([...currentBatch]);
          currentBatch = [];
        }

      } catch (err) {
        console.error(`Error processing row ${index + 1}:`, err);
        return NextResponse.json(
          { 
            error: `Error processing row ${index + 1}: ${err instanceof Error ? err.message : 'Unknown error'}`,
            row: index + 1,
            data: row
          },
          { status: 400 }
        );
      }
    }

    // Add any remaining rows as the final batch
    if (currentBatch.length > 0) {
      console.log(`Creating final batch of ${currentBatch.length} faculty members`);
      batches.push(currentBatch);
    }

    console.log(`Total faculty to be inserted: ${totalFaculty}`);
    console.log(`Skipped non-primary jobs: ${skippedNonPrimary}`);
    console.log(`Skipped invalid unit IDs: ${skippedInvalidUnit}`);
    console.log(`Number of batches: ${batches.length}`);

    // Process each batch
    for (const [batchIndex, batch] of batches.entries()) {
      try {
        if (batch.length === 0) continue;

        // Get all possible columns
        const columns = [
          'intelicampus_id',
          'sso_id',
          'first_name',
          'last_name',
          'work_email',
          'primary_unit_id',
          'primary_unit_percentage',
          'date_started',
          'tenure_status',
          'position_id',
          'job_family'
        ];
        
        // Create values string for each row
        const valuesStrings = batch.map(row => {
          const values = columns.map(col => {
            const value = (row as any)[col];
            if (value === undefined || value === null || value === '') {
              return 'NULL';
            }
            if (value instanceof Date) {
              return `'${value.toISOString()}'`;
            }
            return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
          });
          return `(${values.join(', ')})`;
        });

        // Create the batch insert query
        const sqlQuery = `INSERT INTO uw.faculty (${columns.join(', ')}) VALUES ${valuesStrings.join(', ')}`;
        
        // Log the SQL statement
        console.log(`Executing batch ${batchIndex + 1} (${batch.length} rows):`, sqlQuery);

        // Execute the batch insert
        await sql.unsafe(sqlQuery);
        console.log(`Successfully inserted batch ${batchIndex + 1}`);

      } catch (err) {
        console.error(`Error in batch ${batchIndex + 1}:`, err);
        return NextResponse.json(
          { 
            error: `Error in batch ${batchIndex + 1}: ${err instanceof Error ? err.message : 'Unknown error'}`,
            batchIndex: batchIndex + 1,
            batchSize: batch.length
          },
          { status: 400 }
        );
      }
    }

    console.log('Import completed successfully');
    return NextResponse.json({ 
      success: true, 
      message: `Successfully imported ${totalFaculty} faculty members in ${batches.length} batches (${skippedNonPrimary} non-primary jobs skipped, ${skippedInvalidUnit} invalid unit IDs skipped)` 
    });
  } catch (err) {
    console.error('Import error:', err);
    return NextResponse.json(
      { error: err instanceof Error ? err.message : "Failed to import data" },
      { status: 500 }
    );
  }
} 