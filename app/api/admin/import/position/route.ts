import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import * as XLSX from 'xlsx';

const BATCH_SIZE = 500;

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("system_admin")) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const mapping = JSON.parse(formData.get('mapping') as string) as Record<string, string>;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Read the file content
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(worksheet) as Record<string, any>[];

    if (!data || data.length === 0) {
      return NextResponse.json({ error: "No data found in the file" }, { status: 400 });
    }

    // Log the mapping and first row of data
    console.log('Column mapping:', mapping);
    console.log('First row of data:', data[0]);

    // Validate required fields
    const requiredFields = ['position_id'];
    const missingFields = requiredFields.filter(field => !mapping[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required field mappings: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Process rows in batches
    const batches: Record<string, any>[][] = [];
    let currentBatch: Record<string, any>[] = [];

    for (const [index, row] of data.entries()) {
      try {
        const mappedData: Record<string, any> = {};
        
        // Map the data according to the provided mapping
        for (const [positionColumn, excelColumn] of Object.entries(mapping)) {
          if (excelColumn) {
            mappedData[positionColumn] = row[excelColumn];
          }
        }

        // Validate required fields are present in the mapped data
        const missingRequiredValues = requiredFields.filter(field => !mappedData[field]);
        if (missingRequiredValues.length > 0) {
          return NextResponse.json(
            { 
              error: `Row ${index + 1} is missing required values: ${missingRequiredValues.join(', ')}`,
              row: index + 1,
              data: row,
              mappedData
            },
            { status: 400 }
          );
        }

        currentBatch.push(mappedData);

        // If we've reached the batch size, add the batch to batches array
        if (currentBatch.length === BATCH_SIZE) {
          batches.push([...currentBatch]);
          currentBatch = [];
        }

      } catch (err) {
        return NextResponse.json(
          { 
            error: `Error processing row ${index + 1}: ${err instanceof Error ? err.message : 'Unknown error'}`,
            row: index + 1,
            data: row
          },
          { status: 400 }
        );
      }
    }

    // Add any remaining rows as the final batch
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    // Process each batch
    for (const [batchIndex, batch] of batches.entries()) {
      try {
        if (batch.length === 0) continue;

        // Get all possible columns from the mapping
        const columns = Object.keys(mapping);
        
        // Create values string for each row
        const valuesStrings = batch.map(row => {
          const values = columns.map(col => {
            const value = row[col];
            if (value === undefined || value === null || value === '') {
              return 'NULL';
            }
            return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
          });
          return `(${values.join(', ')})`;
        });

        // Create the batch insert query
        const sqlQuery = `INSERT INTO uw.position (${columns.join(', ')}) VALUES ${valuesStrings.join(', ')}`;
        
        // Log the SQL statement
        console.log(`Batch ${batchIndex + 1} SQL (${batch.length} rows):`, sqlQuery);

        // Execute the batch insert
        await sql.unsafe(sqlQuery);

      } catch (err) {
        return NextResponse.json(
          { 
            error: `Error in batch ${batchIndex + 1}: ${err instanceof Error ? err.message : 'Unknown error'}`,
            batchIndex: batchIndex + 1,
            batchSize: batch.length
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: `Successfully imported ${data.length} rows in ${batches.length} batches` 
    });
  } catch (err) {
    console.error('Import error:', err);
    return NextResponse.json(
      { error: err instanceof Error ? err.message : "Failed to import data" },
      { status: 500 }
    );
  }
} 