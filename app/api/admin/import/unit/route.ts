import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import * as XLSX from "xlsx";
import columnMapping from "@/app/dashboard/sys_admin/import/column_mapping.json";

interface UnitData {
  [key: string]: any; // Allow any string keys since we're reading from Excel
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("system_admin")) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    if (!file) {
      return NextResponse.json(
        { error: "No file uploaded" },
        { status: 400 }
      );
    }

    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer);
    const sheet = workbook.Sheets["Units_data"];
    
    if (!sheet) {
      return NextResponse.json(
        { error: "Sheet 'Units_data' not found in the Excel file" },
        { status: 400 }
      );
    }

    const data = XLSX.utils.sheet_to_json(sheet) as UnitData[];
    console.log(`Read ${data.length} rows from Units_data sheet`);

    // Create a mapping of unit abbreviations to unit IDs from the input data
    const abbreviationToUnitId = new Map<string, string>();
    data.forEach(row => {
      const unitId = row[columnMapping.unit.unit_id];
      const abbreviation = row[columnMapping.unit.abbreviation];
      if (unitId && abbreviation) {
        abbreviationToUnitId.set(abbreviation, unitId);
      }
    });

    // Process data in batches
    const batchSize = 100;
    let totalImported = 0;
    let skippedRows = 0;

    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      // Process each row in the batch
      for (const row of batch) {
        try {
          const parentUnitAbbr = row[columnMapping.unit.parent_unit_id];
          const parentUnitId = parentUnitAbbr ? abbreviationToUnitId.get(parentUnitAbbr) || null : null;

          // Map Excel columns to database columns using columnMapping
          const mappedData = {
            unit_id: row[columnMapping.unit.unit_id] || null,
            full_name: row[columnMapping.unit.full_name] || null,
            short_name: row[columnMapping.unit.short_name] || null,
            abbreviation: row[columnMapping.unit.abbreviation] || null,
            level_number: row[columnMapping.unit.level_number] || null,
            parent_unit_id: parentUnitId,
            unit_type: row[columnMapping.unit.unit_type] || null,
            previous_name_1: row[columnMapping.unit.previous_name_1] || null
          };
          
          // Log the data before executing the query
          console.log('Executing insert for unit:', {
            unit_id: mappedData.unit_id,
            full_name: mappedData.full_name,
            short_name: mappedData.short_name,
            abbreviation: mappedData.abbreviation,
            level_number: mappedData.level_number,
            parent_unit_id: mappedData.parent_unit_id,
            parent_unit_abbr: parentUnitAbbr,
            unit_type: mappedData.unit_type,
            previous_name_1: mappedData.previous_name_1
          });

          await sql`
            INSERT INTO uw.unit (
              unit_id,
              full_name,
              short_name,
              abbreviation,
              level_number,
              parent_unit_id,
              unit_type,
              previous_name_1
            ) VALUES (
              ${mappedData.unit_id},
              ${mappedData.full_name},
              ${mappedData.short_name},
              ${mappedData.abbreviation},
              ${mappedData.level_number},
              ${mappedData.parent_unit_id},
              ${mappedData.unit_type},
              ${mappedData.previous_name_1}
            )
          `;
          totalImported++;
        } catch (err) {
          console.error(`Error processing row:`, err);
          skippedRows++;
        }
      }
      
      console.log(`Processed batch of ${batch.length} units`);
    }

    return NextResponse.json({
      message: `Successfully imported ${totalImported} units`,
      skipped: skippedRows
    });
  } catch (err) {
    console.error('Error importing unit data:', err);
    return NextResponse.json(
      { error: err instanceof Error ? err.message : "Failed to import unit data" },
      { status: 500 }
    );
  }
} 