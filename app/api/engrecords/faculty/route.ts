import { NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query') || '';
  const page = Number(searchParams.get('page')) || 1;
  const ITEMS_PER_PAGE = 10;
  const offset = (page - 1) * ITEMS_PER_PAGE;

  try {
    let faculty;
    let totalCount;

    if (query) {
      // Search by name or nexus
      faculty = await sql`
        SELECT nexus, first_name, last_name
        FROM engrecords.eng_fac
        WHERE active = 'Y'
          AND (
            first_name ILIKE ${`%${query}%`} OR
            last_name ILIKE ${`%${query}%`} OR
            nexus ILIKE ${`%${query}%`}
          )
        ORDER BY last_name, first_name
        LIMIT ${ITEMS_PER_PAGE}
        OFFSET ${offset}
      `;

      totalCount = await sql`
        SELECT COUNT(*) as count
        FROM engrecords.eng_fac
        WHERE active = 'Y'
          AND (
            first_name ILIKE ${`%${query}%`} OR
            last_name ILIKE ${`%${query}%`} OR
            nexus ILIKE ${`%${query}%`}
          )
      `;
    } else {
      // Get all faculty with pagination
      faculty = await sql`
        SELECT nexus, first_name, last_name
        FROM engrecords.eng_fac
        WHERE active = 'Y'
        ORDER BY last_name, first_name
        LIMIT ${ITEMS_PER_PAGE}
        OFFSET ${offset}
      `;

      totalCount = await sql`
        SELECT COUNT(*) as count
        FROM engrecords.eng_fac
        WHERE active = 'Y'
      `;
    }

    const totalPages = Math.ceil(Number(totalCount[0]?.count || 0) / ITEMS_PER_PAGE);

    return NextResponse.json({
      faculty,
      totalPages,
    });
  } catch (error) {
    console.error('Error fetching faculty:', error);
    return NextResponse.json(
      { error: 'Failed to fetch faculty' },
      { status: 500 }
    );
  }
}