import { NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';

export async function GET() {
  try {
    // Get distinct faculty groups
    const facGroups = await sql`
      SELECT DISTINCT fac_group
      FROM engrecords.eng_fac
      WHERE active = 'Y' AND fac_group IS NOT NULL
      ORDER BY fac_group
    `;

    // Get distinct organization units with their full names
    const orgUnits = await sql`
      SELECT DISTINCT ef.fac_org_unit, u.full_name
      FROM engrecords.eng_fac ef
      LEFT JOIN uw.unit u ON u.unit_id = ef.fac_org_unit::integer
      WHERE ef.active = 'Y' AND ef.fac_org_unit IS NOT NULL
      ORDER BY u.full_name
    `;

    return NextResponse.json({
      facGroups: facGroups.map(group => ({ value: group.fac_group, label: group.fac_group })),
      orgUnits: orgUnits.map(unit => ({ 
        value: unit.fac_org_unit, 
        label: unit.full_name || unit.fac_org_unit 
      }))
    });
  } catch (error) {
    console.error('Error fetching faculty filters:', error);
    return NextResponse.json(
      { error: 'Failed to fetch faculty filters' },
      { status: 500 }
    );
  }
}
