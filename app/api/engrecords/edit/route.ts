import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';
import { sql } from '@/app/lib/db';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has system_admin role
    if (!session?.user?.id || !session.user.roles?.includes('system_admin')) {
      return NextResponse.json(
        { error: 'Unauthorized. Only system administrators can edit records.' },
        { status: 403 }
      );
    }

    // Parse request body
    const { value, fieldName, recordId, tableName, pageUrl } = await request.json();

    if (!value || !fieldName || !recordId || !tableName || !pageUrl) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate tableName to prevent SQL injection
    const validTables = ['eng_fac', 'appts_fac', 'appts_admin', 'appts_cross', 'appts_joint', 'appts_over', 'degrees', 'awards'];
    if (!validTables.includes(tableName)) {
      return NextResponse.json(
        { error: 'Invalid table name' },
        { status: 400 }
      );
    }

    // Define tables that use auto_nbr as primary key
    const tablesWithAutoNbr = ['appts_fac', 'appts_admin', 'appts_cross', 'appts_joint', 'appts_over', 'degrees', 'awards'];
    const usesAutoNbr = tablesWithAutoNbr.includes(tableName);

    // Validate fieldName to prevent SQL injection
    // This would need to be expanded based on the actual fields in each table
    const validFields: Record<string, string[]> = {
      'eng_fac': ['first_name', 'last_name', 'appt_title', 'descr', 'fac_org_unit', 'appt_type', 'track_type'],
      'appts_fac': ['fac_category', 'fac_stage', 'fac_type', 'fac_rank_desc', 'fac_teaching_perc', 'fac_research_perc', 'fac_service_perc', 'fac_merit_cycle', 'fac_notes'],
      'appts_admin': ['admin_title_prefix', 'admin_title', 'admin_portfolio', 'admin_program', 'admin_stipend', 'admin_teaching_release', 'admin_research_supp', 'admin_teaching_perc', 'admin_research_perc', 'admin_service_perc', 'admin_notes'],
      'appts_cross': ['cross_other_org_unit', 'cross_notes'],
      'appts_joint': ['joint_home_org_unit', 'joint_home_dept_fte', 'joint_org_unit_1', 'joint_dept_fte_1', 'joint_org_unit_2', 'joint_dept_fte_2', 'joint_org_unit_3', 'joint_dept_fte_3', 'joint_notes'],
      'appts_over': ['over_notes'],
      'degrees': ['deg_type', 'deg_name', 'deg_institution', 'deg_province_name'],
      'awards': ['award_category', 'award_name', 'award_status']
    };

    if (!validFields[tableName].includes(fieldName)) {
      return NextResponse.json(
        { error: 'Invalid field name' },
        { status: 400 }
      );
    }

    // Construct the SQL query for the update
    let updateQuery;
    if (usesAutoNbr) {
      // For tables with auto_nbr, use it as the primary key
      updateQuery = `
        UPDATE engrecords.${tableName}
        SET ${fieldName} = $1
        WHERE auto_nbr = $2
        RETURNING *
      `;
    } else {
      // For tables without auto_nbr (like eng_fac), use nexus
      updateQuery = `
        UPDATE engrecords.${tableName}
        SET ${fieldName} = $1
        WHERE nexus = $2
        RETURNING *
      `;
    }

    // Execute the update query
    const result = await sql.unsafe(updateQuery, [value, recordId]);

    if (!result || result.length === 0) {
      return NextResponse.json(
        { error: 'Failed to update record or record not found' },
        { status: 404 }
      );
    }

    // Record the change in the changelog table
    const changelogQuery = `
      INSERT INTO uw.changelog (
        user_id,
        page_url,
        table_name,
        record_id,
        sql_query
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING changelog_id
    `;

    // Construct SQL query text for changelog
    let sqlQueryText;
    if (usesAutoNbr) {
      sqlQueryText = `UPDATE engrecords.${tableName} SET ${fieldName} = '${value}' WHERE auto_nbr = '${recordId}'`;
    } else {
      sqlQueryText = `UPDATE engrecords.${tableName} SET ${fieldName} = '${value}' WHERE nexus = '${recordId}'`;
    }

    await sql.unsafe(changelogQuery, [
      session.user.id,
      pageUrl,
      tableName,
      recordId,
      sqlQueryText
    ]);

    return NextResponse.json({
      message: 'Record updated successfully',
      data: result[0]
    });
  } catch (error) {
    console.error('Error updating record:', error);
    return NextResponse.json(
      { error: 'Failed to update record' },
      { status: 500 }
    );
  }
}
