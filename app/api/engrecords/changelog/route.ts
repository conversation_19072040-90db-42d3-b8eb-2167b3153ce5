import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';
import { sql } from '@/app/lib/db';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has system_admin role
    if (!session?.user?.id || !session.user.roles?.includes('system_admin')) {
      return NextResponse.json(
        { error: 'Unauthorized. Only system administrators can view change history.' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const pageUrl = searchParams.get('pageUrl');

    if (!pageUrl) {
      return NextResponse.json(
        { error: 'Missing required page URL parameter' },
        { status: 400 }
      );
    }

    // Fetch changelog entries for this page URL only
    const changelogEntries = await sql`
      SELECT
        c.changelog_id,
        c.timestamp,
        c.page_url,
        c.table_name,
        c.record_id,
        c.sql_query,
        u.email as user_email,
        u.display_name as user_name
      FROM uw.changelog c
      JOIN common.user u ON c.user_id = u.user_id
      WHERE c.is_deleted = false
        AND c.page_url = ${pageUrl}
      ORDER BY c.timestamp DESC
      LIMIT 100
    `;

    return NextResponse.json({
      changelogEntries
    });
  } catch (error) {
    console.error('Error fetching changelog entries:', error);
    return NextResponse.json(
      { error: 'Failed to fetch changelog entries' },
      { status: 500 }
    );
  }
}
