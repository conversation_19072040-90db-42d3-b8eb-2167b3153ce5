import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the userId from the query parameter
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    // Verify that the requested userId matches the session userId
    if (userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch the display_name from the common.user table
    const result = await sql`
      SELECT display_name
      FROM common.user
      WHERE user_id = ${userId} AND deleted_at IS NULL
    `;

    if (result.length === 0) {
      return NextResponse.json({ displayName: '' });
    }

    return NextResponse.json({ displayName: result[0].display_name });
  } catch (error) {
    console.error('Error fetching display name:', error);
    return NextResponse.json(
      { error: 'Failed to fetch display name' },
      { status: 500 }
    );
  }
}
