import { NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`Fetching Google Scholar profile for user: ${session.user.email}`);

    // First check if the user exists in the faculty table
    const facultyCheck = await sql`
      SELECT faculty_id, first_name, last_name
      FROM uw.faculty
      WHERE work_email = ${session.user.email}
        AND is_deleted = FALSE
    `;

    if (facultyCheck.length === 0) {
      console.log(`No faculty record found for email: ${session.user.email}`);
      return NextResponse.json(null);
    }

    const facultyId = facultyCheck[0].faculty_id;
    console.log(`Found faculty with ID: ${facultyId}, name: ${facultyCheck[0].first_name} ${facultyCheck[0].last_name}`);

    // Fetch Google Scholar profile for the faculty member
    const result = await sql`
      SELECT
        ap.scholar_id,
        ap.name,
        ap.affiliation,
        ap.areas_of_interest,
        ap.citations_all,
        ap.h_index_all,
        ap.profile_url,
        ap.profile_image_url
      FROM googlescholar.author_profile ap
      WHERE ap.faculty_id = ${facultyId}
      LIMIT 1
    `;

    if (result.length === 0) {
      console.log(`No Google Scholar profile found for faculty ID: ${facultyId}`);
      return NextResponse.json(null);
    }

    const profile = result[0];
    console.log(`Found Google Scholar profile with scholar_id: ${profile.scholar_id}`);

    // Use the stored profile_image_url from the database
    // This should be the path to the local file in public/faculty/avatar
    if (profile.profile_image_url) {
      console.log(`Using stored profile image URL: ${profile.profile_image_url}`);
    } else if (profile.scholar_id) {
      // If no profile_image_url is set but we have a scholar_id, we can try to construct a path
      // This is a fallback in case the photo hasn't been downloaded yet
      // Get faculty_id for this profile
      const facultyResult = await sql`
        SELECT faculty_id
        FROM googlescholar.author_profile
        WHERE scholar_id = ${profile.scholar_id}
      `;

      if (facultyResult.length > 0) {
        const facultyId = facultyResult[0].faculty_id;
        // Construct the path to where the photo should be
        profile.profile_image_url = `/faculty/avatar/${facultyId}.jpg`;
        console.log(`Constructed fallback profile image URL: ${profile.profile_image_url}`);
      }
    }

    return NextResponse.json(profile);
  } catch (error) {
    console.error('Error fetching Google Scholar profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Google Scholar profile' },
      { status: 500 }
    );
  }
}
