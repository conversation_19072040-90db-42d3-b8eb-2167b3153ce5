import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { handleApiError } from "@/lib/error-handler";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // First, get the faculty data from uw.faculty table using the email
    console.log('Fetching faculty data for email:', session.user.email);
    const facultyResult = await sql`
      SELECT faculty_id, sso_id
      FROM uw.faculty
      WHERE work_email = ${session.user.email}
        AND is_deleted = FALSE
      LIMIT 1
    `;
    console.log('Faculty result:', facultyResult);

    if (facultyResult.length === 0) {
      return NextResponse.json(null);
    }

    const ssoId = facultyResult[0].sso_id;
    console.log('Using SSO ID for EngRecords lookup:', ssoId);

    // Use case-insensitive match on nexus field
    const engRecordsResult = await sql`
      WITH latest_ranks AS (
        SELECT
          fac_nexus,
          fac_rank_desc,
          ROW_NUMBER() OVER (PARTITION BY fac_nexus ORDER BY fac_eff_from_dt DESC) as rn
        FROM engrecords.appts_fac
      )
      SELECT
        f.nexus,
        f.first_name,
        f.last_name,
        f.appt_title,
        f.descr,
        f.fac_org_unit,
        f.appt_type,
        f.track_type,
        f.fac_group,
        f.active,
        lr.fac_rank_desc
      FROM engrecords.eng_fac f
      LEFT JOIN latest_ranks lr ON f.nexus = lr.fac_nexus AND lr.rn = 1
      WHERE LOWER(f.nexus) = LOWER(${ssoId})
    `;

    console.log('EngRecords result:', engRecordsResult);
    if (engRecordsResult.length === 0) {
      console.log('No EngRecords data found for SSO ID:', ssoId);
      return NextResponse.json(null);
    }

    // Get unit full name from uw.unit table
    let unitFullName = '';
    if (engRecordsResult[0].fac_org_unit) {
      try {
        const unitResults = await sql`
          SELECT full_name
          FROM uw.unit
          WHERE unit_id = ${engRecordsResult[0].fac_org_unit}::integer
        `;

        if (unitResults.length > 0) {
          unitFullName = unitResults[0].full_name;
        }
      } catch (error) {
        console.error('Error fetching unit name:', error);
      }
    }

    // Combine faculty data with unit full name
    const faculty = {
      ...engRecordsResult[0],
      unit_full_name: unitFullName,
      fac_rank_desc: engRecordsResult[0].fac_rank_desc || '',
      is_active: engRecordsResult[0].active === 'Y'
    };

    return NextResponse.json(faculty);
  } catch (error) {
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
