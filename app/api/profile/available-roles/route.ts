import { NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || !session?.user?.roles?.includes('system_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get system roles
    const systemRoles = await sql`
      SELECT role_id::text as id, name, 'system' as type
      FROM common.role
      WHERE deleted_at IS NULL
      ORDER BY name
    `;

    // Get institution roles
    const institutionRoles = await sql`
      SELECT role_id::text as id, role_name as name, 'institution' as type
      FROM uw.institution_role
      WHERE is_deleted = FALSE
      ORDER BY role_name
    `;

    // Combine both role sets
    const roles = [...systemRoles, ...institutionRoles];

    return NextResponse.json({ roles });
  } catch (error) {
    console.error('Error fetching available roles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch available roles' },
      { status: 500 }
    );
  }
}
