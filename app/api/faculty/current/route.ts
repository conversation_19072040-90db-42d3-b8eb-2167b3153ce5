import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Fetch faculty data for the logged-in user
    const result = await sql`
      SELECT
        f.faculty_id,
        f.first_name,
        f.last_name,
        f.work_email,
        f.primary_unit_id,
        u.full_name as unit_name
      FROM uw.faculty f
      LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
      WHERE f.work_email = ${session.user.email}
        AND f.is_deleted = FALSE
      LIMIT 1
    `;

    if (result.length === 0) {
      return NextResponse.json(null);
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error("Error fetching faculty data:", error);
    return NextResponse.json(
      { error: "Failed to fetch faculty data" },
      { status: 500 }
    );
  }
}
