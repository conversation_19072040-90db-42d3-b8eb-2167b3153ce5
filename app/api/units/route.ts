import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

// Get all units
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.facultyId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const parentUnitId = searchParams.get("parent_unit_id");
    const level = searchParams.get("level");

    // Build the query based on parameters
    let query = sql`
      SELECT unit_id, full_name, level_number, parent_unit_id
      FROM uw.unit
      WHERE 1=1
    `;

    if (parentUnitId) {
      query = sql`${query} AND parent_unit_id = ${parseInt(parentUnitId)}`;
    }

    if (level) {
      query = sql`${query} AND level_number = ${parseInt(level)}`;
    }

    query = sql`${query} ORDER BY full_name ASC`;

    const units = await query;

    return NextResponse.json(units);
  } catch (error) {
    console.error("Error fetching units:", error);
    return NextResponse.json(
      { error: "Failed to fetch units" },
      { status: 500 }
    );
  }
}
