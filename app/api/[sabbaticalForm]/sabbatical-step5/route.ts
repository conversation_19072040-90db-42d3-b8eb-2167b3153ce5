// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';
import { sql } from "@/app/lib/db";
import { cookies } from 'next/headers'; // to store requestId in cookie

await sql `SET search_path TO uw`;


// Zod schema validation
const SabbaticalSchema = z.object({

  applicant_name: z.string().optional(),
  dept_name: z.string().optional(),
  administrative_appointment: z.string().optional(),
  from_date: z.string().optional(),
  to_date: z.string().optional(),
  arrangement_made: z.string().optional(),
  no_administrative_appointment: z.string().optional(),

});

// POST handler
export async function POST(request: Request) {

      // Get Cookies Value for Appl ID
      const cookieStore = await cookies();
      const formData = cookieStore.get('formData')?.value;
      if (!formData) {
        throw new Error("formData cookie is missing.");
      }
      const parsed = JSON.parse(formData);
      const requestId = parsed.requestId;
      const grtLieuSalary = parsed.grtLieuSalary;
      // Get Cookies Value for Appl ID End

  try {
            const session = await getServerSession(authOptions);
            if (!session?.user?.id) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            const body = await request.json();
            // Validate incoming request
            const result = SabbaticalSchema.safeParse(body);

            if (!result.success) {
              return NextResponse.json({ success: false, error: "Validation failed", details: result.error.flatten() }, { status: 400 });
            }
            const data = result.data;
            const { applicant_name, dept_name, administrative_appointment, from_date, to_date, arrangement_made, no_administrative_appointment } = data;
            let insertedId: string | undefined;

            try {

              await sql.begin(async (sql) => {

                  const res = await sql `
                    INSERT INTO uw.subbatical_administrative_appointment (
                      appl_id, applicant_name, dept_name, administrative_appointment, from_date, to_date, arrangement_made, no_administrative_appointment
                    )
                    VALUES (
                      ${requestId ?? null}, ${applicant_name ?? null}, ${dept_name ?? null}, ${administrative_appointment ?? null},
                      ${from_date ?? null}, ${to_date ?? null}, ${arrangement_made ?? null}, ${no_administrative_appointment ?? null}
                    ) RETURNING id
                      `;

                    insertedId = res[0]?.id; // store inserted ID
              });

              if (!insertedId) {
                return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
              }
    
              return NextResponse.json({ success: true, requestId: insertedId });
    
            } catch (err) {
              console.error("DB Error:", err);
              return NextResponse.json({ success: false, error: "Database error" }, { status: 500 });
            }


      } catch (error) {
              console.error('Error fetching roles:', error);
              return NextResponse.json(
                { error: 'Failed to fetch roles' },
                { status: 500 }
                );
      }
}