// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';
import { cookies } from 'next/headers'; // to store requestId in cookie
import { sql } from "@/app/lib/db";

await sql `SET search_path TO uw`;

const PersonnelSchema = z.object({
  name_position: z.string(),
  rate_per_annum: z.string(),
  hours_per_week: z.string(),
  calc_rate: z.string(),
  fringe_benefit: z.string(),
  est_expenses: z.string(),
  cost: z.string(),
});

const TravelSchema = z.object({
  location: z.string(),
  duration: z.string(),
  travel_mode: z.string(),
  related_cost: z.string(),
  cost: z.string(),
});

const OtherExpensesSchema = z.object({
  quantity: z.string(),
  description: z.string(),
  unit_cost: z.string(),
  cost: z.string(),
});

// Zod schema validation
const SabbaticalSchema = z.object({

  applicant_name: z.string().optional(),
  department: z.string().optional(),
  date: z.string().optional(),
  no_of_month: z.string().optional(),
  starting_term: z.string().optional(),
  ending_term: z.string().optional(),
  project_title: z.string().optional(),
  research_location: z.string().optional(),
  amount_requested: z.string().optional(),
  research_description: z.string().optional(),
  personnel: z.array(PersonnelSchema).optional(),
  travel: z.array(TravelSchema).optional(),
  equipment: z.array(OtherExpensesSchema).optional(),
  supplies: z.array(OtherExpensesSchema).optional(),
  otherexpenses: z.array(OtherExpensesSchema).optional(),

});

// POST handler
export async function POST(request: Request) {

    // Get Cookies Value for Appl ID
    const cookieStore = await cookies();
    const formData = cookieStore.get('formData')?.value;
    if (!formData) {
      throw new Error("formData cookie is missing.");
    }
    const parsed = JSON.parse(formData);
    const requestId = parsed.requestId;
    // Get Cookies Value for Appl ID End

  try {
            const session = await getServerSession(authOptions);
            if (!session?.user?.id) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            const body = await request.json();
            // Validate incoming request
            const result = SabbaticalSchema.safeParse(body);

            if (!result.success) {
              return NextResponse.json({ success: false, error: "Validation failed", details: result.error.flatten() }, { status: 400 });
            }
            const data = result.data;
            const { applicant_name, department, date, no_of_month, starting_term, ending_term, project_title, research_location, amount_requested, research_description, personnel, travel, equipment, supplies, otherexpenses } = data;
            let insertedId: string | undefined;

            try {

              await sql.begin(async (sql) => {

                  const res = await sql `
                    INSERT INTO uw.sabatical_research_grant (
                      appl_id, applicant_name, department, date, no_of_month, starting_term, ending_term, project_title, research_location, amount_requested, research_description, personnel, travel, equipment, supplies, otherexpenses
                    )
                    VALUES (
                      ${requestId ?? null}, ${applicant_name || null}, ${department || null}, ${date || null}, ${no_of_month || null}, ${starting_term || null}, ${ending_term || null}, ${project_title || null}, ${research_location || null}, ${amount_requested || null}, ${research_description || null}, ${personnel ? JSON.stringify(personnel) : null}, ${travel ? JSON.stringify(travel) : null}, ${equipment ? JSON.stringify(equipment) : null}, ${supplies ? JSON.stringify(supplies) : null}, ${otherexpenses ? JSON.stringify(otherexpenses) : null}
                    ) RETURNING id
                      `;

                    insertedId = res[0]?.id; // store inserted ID
              });

              if (!insertedId) {
                return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
              }
    
              return NextResponse.json({ success: true, requestId: insertedId });
    
            } catch (err) {
              console.error("DB Error:", err);
              return NextResponse.json({ success: false, error: "Database error" }, { status: 500 });
            }


      } catch (error) {
              console.error('Error fetching roles:', error);
              return NextResponse.json(
                { error: 'Failed to fetch roles' },
                { status: 500 }
                );
      }
}