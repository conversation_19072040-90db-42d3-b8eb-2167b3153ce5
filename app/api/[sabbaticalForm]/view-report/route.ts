import { getServerSession } from "next-auth/next";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";


export async function GET(req: NextRequest) {

  const session = await getServerSession(authOptions);
  
  if (!session?.user?.roles?.includes("system_admin") && !session?.user?.roles?.includes("faculty_admin")) {
    throw forbidden("You don't have permission to access.");
  }

  const { searchParams } = new URL(req.url);
  const userReport = searchParams.get("userReport");

  if (!userReport) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
  }
  await sql `SET search_path TO uw`;

  try {

      const result = await sql`
      SELECT * FROM uw.sabbatical_reports
      WHERE appl_id = ${userReport}
    `;

    return NextResponse.json({ data: result}, { status: 200 });
  
  } catch (error) {

    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });

  }

}

