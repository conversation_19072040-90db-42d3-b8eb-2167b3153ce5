// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';
import { sql } from "@/app/lib/db";
import { cookies } from 'next/headers'; // to store requestId in cookie

await sql `SET search_path TO uw`;

const SupervisorEntrySchema = z.object({
  student_name: z.string(),
  supervisor: z.string(),
});

// Zod schema validation
const SabbaticalSchema = z.object({

  dept_name: z.string().optional(),
  applicant_name: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  student_name: z.string().optional(),
  delegated_supervisor: z.array(SupervisorEntrySchema).optional(),
  no_grad_student: z.string().optional(),

});


// POST handler
export async function POST(request: Request) {

    // Get Cookies Value for Appl ID
    const cookieStore = await cookies();
    const formData = cookieStore.get('formData')?.value;
    if (!formData) {
      throw new Error("formData cookie is missing.");
    }
    const parsed = JSON.parse(formData);
    const requestId = parsed.requestId;
    // Get Cookies Value for Appl ID End

  try {
            const session = await getServerSession(authOptions);
            if (!session?.user?.id) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            const body = await request.json();
            // Validate incoming request
            const result = SabbaticalSchema.safeParse(body);

            if (!result.success) {
              return NextResponse.json({ success: false, error: "Validation failed", details: result.error.flatten() }, { status: 400 });
            }
            const data = result.data;
            const { dept_name, applicant_name, date_from, date_to, delegated_supervisor, no_grad_student} = data;
            let insertedId: string | undefined;
            
            try {

              await sql.begin(async (sql) => {

                  const res = await sql `
                    INSERT INTO uw.sabbatical_student_supervision (
                      appl_id, dept_name, applicant_name, date_from, date_to, delegated_supervisor, no_grad_student
                    )
                    VALUES (
                      ${requestId ?? null}, ${dept_name ?? null}, ${applicant_name ?? null}, ${date_from ?? null},
                      ${date_to ?? null}, ${sql.json(delegated_supervisor ?? null)}, ${no_grad_student ?? null}
                    ) RETURNING id
                      `;

                    insertedId = res[0]?.id; // store inserted ID
              });

              if (!insertedId) {
                return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
              }
    
              return NextResponse.json({ success: true, requestId: insertedId });
    
            } catch (err) {
              console.error("DB Error:", err);
              return NextResponse.json({ success: false, error: "Database error" }, { status: 500 });
            }


      } catch (error) {
              console.error('Error fetching roles:', error);
              return NextResponse.json(
                { error: 'Failed to fetch roles' },
                { status: 500 }
                );
      }
}