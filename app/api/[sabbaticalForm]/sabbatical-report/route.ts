// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from "next-auth/next";
import { authOptions } from '@/app/lib/auth';
import { sql } from "@/app/lib/db";

await sql `SET search_path TO uw`;

const LeaveEntrySchema = z.object({
  type: z.string(),
  from: z.string(), // ideally also validated as a date format
  to: z.string(),
  salaryArrangement: z.string(),
});

const SabbaticalSchema = z.object({

  appl_id: z.string().min(1),
  first_name: z.string().min(1),
  last_name: z.string().min(1),
  name: z.string().min(1),
  department: z.string().min(1),
  rank: z.string().min(1),
  tenure: z.string(),
  first_rank: z.string().optional(),
  first_date: z.string().optional(),
  pastLeaves: z.array(LeaveEntrySchema).optional(),
  currentLeaves: z.array(LeaveEntrySchema).optional(),
  leaveOutline: z.string().optional(),
  currentRequestTimeDate: z.string().optional(),
  serviceCreditRemaining: z.string().optional(),
  grantYouPlanToApply: z.string().optional(),
  listOfPublication: z.string().optional(),
  subbaticalTravelPurpose: z.string().optional(),
  researchProject: z.string().optional(),
  involvedInResearch: z.string().optional(),
  sabbaticalContribution: z.string().optional(),

});

export async function POST(request: NextRequest) {

  const session = await getServerSession(authOptions);
  //const facultySsoId = session?.user?.facultySsoId;

  try {
            //const session = await getServerSession(authOptions);
            if (!session?.user?.id) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }
              
            const body = await request.json();
            const result = SabbaticalSchema.safeParse(body);

            if (!result.success) {
              return NextResponse.json({ success: false, error: "Validation failed", details: result.error.flatten() }, { status: 400 });
            }

            const data = result.data;
            const { appl_id, name, department, rank, tenure, first_rank, first_date, pastLeaves, currentLeaves, leaveOutline, currentRequestTimeDate, serviceCreditRemaining, grantYouPlanToApply, listOfPublication, subbaticalTravelPurpose, researchProject, involvedInResearch, sabbaticalContribution } = data;

            let insertedId: string | undefined;

            try {
            
              await sql.begin(async (sql) => {
                const res = await sql `
                  INSERT INTO uw.sabbatical_reports (
                    appl_id, name, department, rank, tenure, first_rank, first_date, past_leaves, current_leaves, leave_outline, current_request_timedate, service_credit_remaining, grant_you_plan_to_apply, list_of_publication, subbatical_travel_purpose, research_project, involved_in_research, sabbatical_contribution
                  )
                  VALUES (

                   ${appl_id ?? null},  ${name ?? null}, ${department ?? null}, ${rank ?? null}, ${tenure ?? null}, ${first_rank ?? null}, ${first_date ?? null}, ${sql.json(pastLeaves ?? null)}, ${sql.json(currentLeaves ?? null)}, ${leaveOutline ?? null}, ${currentRequestTimeDate ?? null}, ${serviceCreditRemaining ?? null}, ${grantYouPlanToApply ?? null}, ${listOfPublication ?? null}, ${subbaticalTravelPurpose ?? null}, ${researchProject ?? null}, ${involvedInResearch ?? null}, ${sabbaticalContribution ?? null}

                  )
                  RETURNING id
                `;

                insertedId = res[0]?.id; // store inserted ID
                
              });

              if (!insertedId) {
                return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
              }

              const response = NextResponse.json({ success: true });

              return response;

            } catch (err) {
              console.error("DB Error:", err);
              return NextResponse.json({ success: false, error: "Database error" }, { status: 500 });
            }

    } catch (error) {
        console.error('Error fetching roles:', error);
        return NextResponse.json(
          { error: 'Failed to fetch roles' },
          { status: 500 }
          );
    }

}