import { NextRequest, NextResponse } from 'next/server';
import { Readable as NodeReadable } from 'stream';
import formidable, { Fields, Files } from 'formidable';
import { readFile } from 'fs/promises';
import { cookies } from 'next/headers';
import { sql } from '@/app/lib/db';
import type { IncomingMessage } from 'http';

// Disable built-in body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

//  Convert Web ReadableStream to Node.js Readable
function webReadableStreamToNodeReadable(stream: ReadableStream<Uint8Array>): NodeReadable {
  const reader = stream.getReader();

  return new NodeReadable({
    async read() {
      try {
        const { done, value } = await reader.read();
        if (done) this.push(null);
        else this.push(Buffer.from(value));
      } catch (err) {
        this.destroy(err as Error);
      }
    }
  });
}

function createMockIncomingMessage(stream: NodeReadable, headers: Headers): IncomingMessage {
  const mockReq = stream as unknown as IncomingMessage;
  mockReq.headers = Object.fromEntries(headers.entries());
  mockReq.method = 'POST';
  mockReq.url = '/';
  mockReq.socket = {} as any;
  return mockReq;
}


async function parseFormData(req: NextRequest) {
  const webStream = req.body as ReadableStream<Uint8Array>;
  const nodeStream = webReadableStreamToNodeReadable(webStream);
  const mockReq = createMockIncomingMessage(nodeStream, req.headers);

  const form = formidable({ multiples: false, keepExtensions: true });

  return new Promise<{ fields: Fields; files: Files }>((resolve, reject) => {
    form.parse(mockReq, (err, fields, files) => {
      if (err) reject(err);
      else resolve({ fields, files });
    });
  });
}

export async function POST(req: NextRequest) {
  try {

    // Get Cookies Value for Appl ID
    const cookieStore = await cookies();
    const formData = cookieStore.get('formData')?.value;
    if (!formData) {
      throw new Error("formData cookie is missing.");
    }
    const parsed = JSON.parse(formData);
    const requestId = parsed.requestId;
    // Get Cookies Value for Appl ID End
    
    const { fields, files } = await parseFormData(req);

    const getField = (val: any) => (Array.isArray(val) ? val[0] : val);

    const leave_values = getField(fields.leave_values);
    const hold_funding = getField(fields.hold_funding);
    const era_funding = getField(fields.era_funding);
    const era_supervision = getField(fields.era_supervision);
    const out_rsrch_funding = getField(fields.out_rsrch_funding);
    const decl_out_emply = getField(fields.decl_out_emply);
    const pl_report = getField(files.pl_report);

    let fileBuffer = null;

    if (pl_report?.filepath) {
      fileBuffer = await readFile(pl_report.filepath);
    }

    await sql`SET search_path TO uw`;

    const insertedId = await sql.begin(async (sql) => {
      const result = await sql`
        INSERT INTO uw.sabbatical_details (
          appl_id, stp_2_leave_values, stp_2_hold_funding, stp_2_era_funding,
          stp_2_era_supervision, stp_2_out_rsrch_funding, stp_2_decl_out_emply, stp_2_pl_report
        ) VALUES (
          ${requestId}, ${leave_values ?? null}, ${hold_funding ?? null},
          ${era_funding ?? null}, ${era_supervision ?? null},
          ${out_rsrch_funding ?? null}, ${decl_out_emply ?? null}, ${fileBuffer ?? null}
        ) RETURNING id
      `;

      return result[0]?.id;
    });

    if (!insertedId) {
      return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
    }

    return NextResponse.json({ success: true, requestId });

  } catch (err) {
    console.error('Error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}