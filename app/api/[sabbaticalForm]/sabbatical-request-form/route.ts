// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from "next-auth/next";
import { authOptions } from '@/app/lib/auth';
import { sql } from "@/app/lib/db";
import { cookies } from 'next/headers'; // to store requestId in cookie
import crypto from 'crypto';

await sql `SET search_path TO uw`;

const LeaveEntrySchema = z.object({
  type: z.string(),
  from: z.string(), // ideally also validated as a date format
  to: z.string(),
  salaryArrangement: z.string(),
});

const SabbaticalSchema = z.object({
  first_name: z.string().min(1),
  last_name: z.string().min(1),
  name: z.string().min(1),
  department: z.string().min(1),
  rank: z.string().min(1),
  tenure: z.string(),
  first_rank: z.string().optional(),
  first_date: z.string().optional(),
  pastLeaves: z.array(LeaveEntrySchema).optional(),
  currentLeaves: z.array(LeaveEntrySchema).optional(),
  nonTeachTerm: z.array(LeaveEntrySchema).optional(),
  leaveOutline: z.string().optional(),
  currentRequestTimeDate: z.string().optional(),
  serviceCreditRemaining: z.string().optional(),
  grtLieuSalary: z.string().optional(),
});

export async function POST(request: NextRequest) {

  const session = await getServerSession(authOptions);
  const facultySsoId = session?.user?.facultySsoId;

  try {
            //const session = await getServerSession(authOptions);
            if (!session?.user?.id) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }
              
            const body = await request.json();
            const result = SabbaticalSchema.safeParse(body);

            //const facultySsoId = session?.user?.facultySsoId;

            if (!result.success) {
              return NextResponse.json({ success: false, error: "Validation failed", details: result.error.flatten() }, { status: 400 });
            }

            const data = result.data;
            const { first_name, last_name, department, tenure, rank, first_rank, first_date, pastLeaves, currentLeaves, nonTeachTerm, leaveOutline, currentRequestTimeDate, serviceCreditRemaining, grtLieuSalary } = data;
           
            
            const requestId = crypto.randomUUID();
            let insertedId: string | undefined;

            try {
            
              await sql.begin(async (sql) => {
                const res = await sql`
                  INSERT INTO uw.sabbatical_requests (
                    appl_id, first_name, last_name, department, tenure, rank, first_rank, first_date,
                    past_leaves, current_leaves, non_teach_term, leave_outline, 
                    current_request_time_date, service_credit_remaining, grt_lieu_salary, facultyssoid
                  )
                  VALUES (
                    ${requestId ?? null}, ${first_name ?? null}, ${last_name ?? null}, ${department ?? null},
                    ${tenure ?? null}, ${rank ?? null}, ${first_rank ?? null}, ${first_date ?? null},
                    ${sql.json(pastLeaves ?? null)}, ${sql.json(currentLeaves ?? null)}, ${sql.json(nonTeachTerm ?? null)},
                    ${leaveOutline ?? null}, ${currentRequestTimeDate ?? null},
                    ${serviceCreditRemaining ?? null}, ${grtLieuSalary ?? null}, ${session?.user?.facultySsoId ?? null}
                  )
                  RETURNING id
                `;
                insertedId = res[0]?.id; // store inserted ID
                

              });

              if (!insertedId) {
                return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
              }
              const cookieStore = await cookies();
              cookieStore.set('requestId', requestId, {
                path: '/',
                httpOnly: true,
                sameSite: 'lax',
              });

              const response = NextResponse.json({ success: true, requestId: requestId });

              response.cookies.set('formData', JSON.stringify({ requestId, grtLieuSalary }), {
                path: '/',
                httpOnly: false, // must be false if you want to read it on the client
                maxAge: 60 * 60 * 24, // 1 day
              });


              return response;

            } catch (err) {
              console.error("DB Error:", err);
              return NextResponse.json({ success: false, error: "Database error" }, { status: 500 });
            }

    } catch (error) {
        console.error('Error fetching roles:', error);
        return NextResponse.json(
          { error: 'Failed to fetch roles' },
          { status: 500 }
          );
    }

}