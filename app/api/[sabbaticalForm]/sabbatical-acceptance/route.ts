// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';
import { sql } from "@/app/lib/db";
import { cookies } from 'next/headers'; // to store requestId in cookie

await sql `SET search_path TO uw`;


// Zod schema validation
const SabbaticalSchema = z.object({

  emp_signature: z.string().min(1),
  emp_location: z.string().min(1),
  emp_sign_date: z.string().min(1),
  administrative_appointment: z.string().min(1),
  laboratory_safty_supervision: z.string().min(1),
  graduate_student_supervision: z.string().min(1),
  plan_sabbatical_leave: z.string().min(1),
  request_for_leave: z.string().min(1),

});

// POST handler
export async function POST(request: Request) {

    // Get Cookies Value for Appl ID
    const cookieStore = await cookies();
    const formData = cookieStore.get('formData')?.value;
    if (!formData) {
      throw new Error("formData cookie is missing.");
    }
    const parsed = JSON.parse(formData);
    const requestId = parsed.requestId;
    // Get Cookies Value for Appl ID End

  try {
            const session = await getServerSession(authOptions);
            if (!session?.user?.id) {
              return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            const body = await request.json();
            // Validate incoming request
            const result = SabbaticalSchema.safeParse(body);

            if (!result.success) {
              return NextResponse.json({ success: false, error: "Validation failed", details: result.error.flatten() }, { status: 400 });
            }
            const data = result.data;
            const { emp_signature, emp_location, emp_sign_date, administrative_appointment, laboratory_safty_supervision, graduate_student_supervision, plan_sabbatical_leave, request_for_leave } = data;
            let insertedId: string | undefined;

            try {

              await sql.begin(async (sql) => {
                // First insert
                const res = await sql`
                  INSERT INTO uw.subbatical_declaration_acceptance (
                    appl_id, emp_signature, emp_location, emp_sign_date,
                    administrative_appointment, laboratory_safty_supervision,
                    graduate_student_supervision, plan_sabbatical_leave, request_for_leave
                  ) VALUES (
                    ${requestId ?? null}, ${emp_signature ?? null}, ${emp_location ?? null}, ${emp_sign_date ?? null},
                    ${administrative_appointment ?? null}, ${laboratory_safty_supervision ?? null},
                    ${graduate_student_supervision ?? null}, ${plan_sabbatical_leave ?? null}, ${request_for_leave ?? null}
                  ) RETURNING id
                `;
            
                insertedId = res[0]?.id;
            
                // Second insert (same transaction)
                await sql`
                  INSERT INTO uw.subbatical_approvals (
                    appl_id
                  ) VALUES (
                    ${requestId ?? null}
                  )
                `;
              });

              if (!insertedId) {
                return NextResponse.json({ success: false, error: 'Failed to insert record.' }, { status: 500 });
              }
    
              return NextResponse.json({ success: true, requestId: insertedId });
    
            } catch (err) {
              console.error("DB Error:", err);
              return NextResponse.json({ success: false, error: "Database error" }, { status: 500 });
            }


      } catch (error) {
              console.error('Error fetching roles:', error);
              return NextResponse.json(
                { error: 'Failed to fetch roles' },
                { status: 500 }
                );
      }
}