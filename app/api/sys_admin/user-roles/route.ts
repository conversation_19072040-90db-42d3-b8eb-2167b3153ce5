import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';

// Add a role to a user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.roles?.includes('system_admin') && !session?.user?.roles?.includes('faculty_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, roleId } = await request.json();

    if (!userId || !roleId) {
      return NextResponse.json(
        { error: 'User ID and Role ID are required' },
        { status: 400 }
      );
    }

    // Check if this is a system role or institution role
    const [systemRole] = await sql`
      SELECT role_id, name FROM common.role WHERE role_id = ${roleId} AND deleted_at IS NULL
    `;

    const [institutionRole] = await sql`
      SELECT role_id, role_name FROM uw.institution_role WHERE role_id = ${roleId} AND is_deleted = FALSE
    `;

    if (systemRole) {
      // Add system role
      await sql`
        INSERT INTO common.user_role (user_id, role_id)
        VALUES (${userId}, ${systemRole.role_id})
        ON CONFLICT (user_id, role_id) DO NOTHING
      `;
      return NextResponse.json({ roleName: systemRole.name });
    } else if (institutionRole) {
      // Get faculty ID for the user
      const [faculty] = await sql`
        SELECT f.faculty_id
        FROM uw.faculty f
        JOIN common.user u ON f.work_email = u.email
        WHERE u.user_id = ${userId} AND f.is_deleted = FALSE
      `;

      if (!faculty) {
        return NextResponse.json(
          { error: 'User is not a faculty member' },
          { status: 400 }
        );
      }

      // Get primary unit for the faculty
      const [primaryUnit] = await sql`
        SELECT primary_unit_id
        FROM uw.faculty
        WHERE faculty_id = ${faculty.faculty_id}
      `;

      // Add institution role
      await sql`
        INSERT INTO uw.faculty_institution_role (faculty_id, institution_role_id, unit_id)
        VALUES (${faculty.faculty_id}, ${institutionRole.role_id}, ${primaryUnit.primary_unit_id})
        ON CONFLICT DO NOTHING
      `;
      return NextResponse.json({ roleName: institutionRole.role_name });
    } else {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }
  } catch (error) {
    console.error('Error adding role:', error);
    return NextResponse.json(
      { error: 'Failed to add role' },
      { status: 500 }
    );
  }
}

// Remove a role from a user
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.roles?.includes('system_admin') && !session?.user?.roles?.includes('faculty_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, roleName } = await request.json();

    if (!userId || !roleName) {
      return NextResponse.json(
        { error: 'User ID and Role Name are required' },
        { status: 400 }
      );
    }

    // Check if this is a system role
    const [systemRole] = await sql`
      SELECT role_id FROM common.role WHERE name = ${roleName} AND deleted_at IS NULL
    `;

    if (systemRole) {
      // Soft delete the user role
      await sql`
        UPDATE common.user_role
        SET deleted_at = NOW()
        WHERE user_id = ${userId} AND role_id = ${systemRole.role_id} AND deleted_at IS NULL
      `;
      return NextResponse.json({ success: true });
    }

    // Check if this is an institution role
    const [institutionRole] = await sql`
      SELECT role_id FROM uw.institution_role WHERE role_name = ${roleName} AND is_deleted = FALSE
    `;

    if (institutionRole) {
      // Get faculty ID for the user
      const [faculty] = await sql`
        SELECT f.faculty_id
        FROM uw.faculty f
        JOIN common.user u ON f.work_email = u.email
        WHERE u.user_id = ${userId} AND f.is_deleted = FALSE
      `;

      if (faculty) {
        // Soft delete the faculty institution role
        await sql`
          UPDATE uw.faculty_institution_role
          SET is_deleted = TRUE
          WHERE faculty_id = ${faculty.faculty_id} AND institution_role_id = ${institutionRole.role_id} AND is_deleted = FALSE
        `;
      }
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Role not found' }, { status: 404 });
  } catch (error) {
    console.error('Error removing role:', error);
    return NextResponse.json(
      { error: 'Failed to remove role' },
      { status: 500 }
    );
  }
}
