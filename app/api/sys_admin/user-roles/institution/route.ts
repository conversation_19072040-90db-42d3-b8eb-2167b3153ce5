import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';

// Add an institution role to a user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.roles?.includes('system_admin') && !session?.user?.roles?.includes('faculty_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, userEmail, roleId, facultyId, primaryUnitId } = await request.json();

    if (!userId || !userEmail || !roleId) {
      return NextResponse.json(
        { error: 'User ID, User Email, and Role ID are required' },
        { status: 400 }
      );
    }

    // Check if this is a valid institution role
    const [institutionRole] = await sql`
      SELECT role_id, role_name FROM uw.institution_role
      WHERE role_id = ${roleId} AND is_deleted = FALSE
    `;

    if (!institutionRole) {
      return NextResponse.json({ error: 'Institution role not found' }, { status: 404 });
    }

    // If facultyId and primaryUnitId are provided, use them directly
    let facultyIdToUse = facultyId;
    let primaryUnitIdToUse = primaryUnitId;

    // If not provided, try to look them up
    if (!facultyIdToUse || !primaryUnitIdToUse) {
      const [faculty] = await sql`
        SELECT f.faculty_id, f.primary_unit_id
        FROM uw.faculty f
        WHERE f.work_email = ${userEmail} AND f.is_deleted = FALSE
      `;

      if (!faculty) {
        return NextResponse.json(
          { error: 'User is not a faculty member' },
          { status: 400 }
        );
      }

      facultyIdToUse = faculty.faculty_id;
      primaryUnitIdToUse = faculty.primary_unit_id;
    }

    // Check if the role already exists but is marked as deleted
    const existingRole = await sql`
      SELECT faculty_institution_role_id
      FROM uw.faculty_institution_role
      WHERE faculty_id = ${facultyIdToUse}
        AND institution_role_id = ${institutionRole.role_id}
        AND unit_id = ${primaryUnitIdToUse}
    `;

    if (existingRole.length > 0) {
      // Update existing role
      await sql`
        UPDATE uw.faculty_institution_role
        SET is_deleted = FALSE
        WHERE faculty_id = ${facultyIdToUse}
          AND institution_role_id = ${institutionRole.role_id}
          AND unit_id = ${primaryUnitIdToUse}
      `;
    } else {
      // Insert new role
      await sql`
        INSERT INTO uw.faculty_institution_role (faculty_id, institution_role_id, unit_id)
        VALUES (${facultyIdToUse}, ${institutionRole.role_id}, ${primaryUnitIdToUse})
      `;
    }

    return NextResponse.json({ roleName: institutionRole.role_name });
  } catch (error) {
    console.error('Error adding institution role:', error);
    return NextResponse.json(
      { error: 'Failed to add institution role' },
      { status: 500 }
    );
  }
}

// Remove an institution role from a user
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.roles?.includes('system_admin') && !session?.user?.roles?.includes('faculty_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, userEmail, roleName, facultyId } = await request.json();

    if (!userId || !userEmail || !roleName) {
      return NextResponse.json(
        { error: 'User ID, User Email, and Role Name are required' },
        { status: 400 }
      );
    }

    // Check if this is a valid institution role
    const [institutionRole] = await sql`
      SELECT role_id FROM uw.institution_role
      WHERE role_name = ${roleName} AND is_deleted = FALSE
    `;

    if (!institutionRole) {
      return NextResponse.json({ error: 'Institution role not found' }, { status: 404 });
    }

    // If facultyId is provided, use it directly
    let facultyIdToUse = facultyId;

    // If not provided, try to look it up
    if (!facultyIdToUse) {
      const [faculty] = await sql`
        SELECT f.faculty_id
        FROM uw.faculty f
        WHERE f.work_email = ${userEmail} AND f.is_deleted = FALSE
      `;

      if (!faculty) {
        return NextResponse.json(
          { error: 'User is not a faculty member' },
          { status: 400 }
        );
      }

      facultyIdToUse = faculty.faculty_id;
    }

    // Soft delete the faculty institution role
    await sql`
      UPDATE uw.faculty_institution_role
      SET is_deleted = TRUE
      WHERE faculty_id = ${facultyIdToUse}
      AND institution_role_id = ${institutionRole.role_id}
      AND is_deleted = FALSE
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing institution role:', error);
    return NextResponse.json(
      { error: 'Failed to remove institution role' },
      { status: 500 }
    );
  }
}
