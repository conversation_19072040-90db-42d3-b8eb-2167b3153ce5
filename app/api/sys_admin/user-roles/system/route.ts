import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';

// Add a system role to a user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.roles?.includes('system_admin') && !session?.user?.roles?.includes('faculty_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, roleId } = await request.json();

    if (!userId || !roleId) {
      return NextResponse.json(
        { error: 'User ID and Role ID are required' },
        { status: 400 }
      );
    }

    // Check if this is a valid system role
    const [systemRole] = await sql`
      SELECT role_id, name FROM common.role WHERE role_id = ${roleId} AND deleted_at IS NULL
    `;

    if (!systemRole) {
      return NextResponse.json({ error: 'System role not found' }, { status: 404 });
    }

    // Add system role
    await sql`
      INSERT INTO common.user_role (user_id, role_id)
      VALUES (${userId}, ${systemRole.role_id})
      ON CONFLICT (user_id, role_id)
      DO UPDATE SET deleted_at = NULL
      WHERE common.user_role.deleted_at IS NOT NULL
    `;

    return NextResponse.json({ roleName: systemRole.name });
  } catch (error) {
    console.error('Error adding system role:', error);
    return NextResponse.json(
      { error: 'Failed to add system role' },
      { status: 500 }
    );
  }
}

// Remove a system role from a user
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.roles?.includes('system_admin') && !session?.user?.roles?.includes('faculty_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, roleName } = await request.json();

    if (!userId || !roleName) {
      return NextResponse.json(
        { error: 'User ID and Role Name are required' },
        { status: 400 }
      );
    }

    // Check if this is a valid system role
    const [systemRole] = await sql`
      SELECT role_id FROM common.role WHERE name = ${roleName} AND deleted_at IS NULL
    `;

    if (!systemRole) {
      return NextResponse.json({ error: 'System role not found' }, { status: 404 });
    }

    // Soft delete the user role
    await sql`
      UPDATE common.user_role
      SET deleted_at = NOW()
      WHERE user_id = ${userId} AND role_id = ${systemRole.role_id} AND deleted_at IS NULL
    `;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing system role:', error);
    return NextResponse.json(
      { error: 'Failed to remove system role' },
      { status: 500 }
    );
  }
}
