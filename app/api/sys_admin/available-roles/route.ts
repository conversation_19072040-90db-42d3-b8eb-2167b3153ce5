import { NextResponse } from 'next/server';
import { sql } from '@/app/lib/db';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/lib/auth';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.roles?.includes('system_admin') && !session?.user?.roles?.includes('faculty_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the URL parameters
    const url = new URL(request.url);
    const userEmail = url.searchParams.get('userEmail');

    // Get system roles
    const systemRoles = await sql`
      SELECT role_id::text as id, name, 'system' as type
      FROM common.role
      WHERE deleted_at IS NULL
      ORDER BY name
    `;

    // Get institution roles
    const institutionRoles = await sql`
      SELECT role_id::text as id, role_name as name, 'institution' as type
      FROM uw.institution_role
      WHERE is_deleted = FALSE
      ORDER BY role_name
    `;

    // Check if the user is a faculty member (only if userEmail is provided)
    let isFaculty = false;
    let facultyId = null;
    let primaryUnitId = null;

    if (userEmail) {
      const facultyCheck = await sql`
        SELECT faculty_id, primary_unit_id, job_family
        FROM uw.faculty
        WHERE work_email = ${userEmail} AND is_deleted = FALSE
      `;

      if (facultyCheck.length > 0) {
        isFaculty = true;
        facultyId = facultyCheck[0].faculty_id;
        primaryUnitId = facultyCheck[0].primary_unit_id;
      }
    }

    return NextResponse.json({
      systemRoles,
      institutionRoles,
      isFaculty,
      facultyId,
      primaryUnitId
    });
  } catch (error) {
    console.error('Error fetching available roles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch available roles' },
      { status: 500 }
    );
  }
}
