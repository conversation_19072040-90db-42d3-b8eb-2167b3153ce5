import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import * as XLSX from 'xlsx';

type FacultyMapping = {
  [key: string]: string | undefined;
};

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("system_admin")) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const mapping = JSON.parse(formData.get('mapping') as string) as FacultyMapping;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Read the file content
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(worksheet) as Record<string, any>[];

    if (!data || data.length === 0) {
      return NextResponse.json({ error: "No data found in the file" }, { status: 400 });
    }

    // Validate required fields
    const requiredFields = ['intelicampus_id', 'employee_id', 'sso_id', 'first_name', 'last_name', 'work_email'];
    const missingFields = requiredFields.filter(field => !mapping[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required field mappings: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Process each row
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const [index, row] of data.entries()) {
      try {
        const mappedData: Record<string, any> = {};
        
        // Map the data according to the provided mapping
        Object.entries(mapping).forEach(([excelColumn, facultyColumn]) => {
          if (facultyColumn && (row as Record<string, any>)[excelColumn] !== undefined) {
            mappedData[facultyColumn] = (row as Record<string, any>)[excelColumn];
          }
        });

        // Validate required fields have values
        const missingValues = requiredFields.filter(field => !mappedData[field]);
        if (missingValues.length > 0) {
          throw new Error(`Row ${index + 1}: Missing required values for ${missingValues.join(', ')}`);
        }

        // Insert into the faculty table
        await sql`
          INSERT INTO uw.faculty (
            ${Object.keys(mappedData).join(', ')}
          ) VALUES (
            ${Object.values(mappedData).join(', ')}
          )
        `;
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Row ${index + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      message: `Import completed. Success: ${results.success}, Failed: ${results.failed}`,
      details: results.errors.length > 0 ? results.errors : undefined
    });
  } catch (error) {
    console.error('Import error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to import data" },
      { status: 500 }
    );
  }
} 