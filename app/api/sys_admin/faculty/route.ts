import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";

/**
 * GET handler for faculty data
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    // Check authorization
    if (!session?.user?.roles?.includes("system_admin") && !session?.user?.roles?.includes("faculty_admin")) {
      throw forbidden("You don't have permission to access faculty data");
    }

    // Fetch faculty data
    const result = await sql`
      SELECT
        f.intelicampus_id,
        f.sso_id,
        f.first_name,
        f.last_name,
        f.work_email,
        f.primary_unit_id,
        u.full_name as unit_name,
        f.primary_unit_percentage,
        f.tenure_status,
        f.position_id,
        f.job_family
      FROM uw.faculty f
      LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
      ORDER BY f.last_name, f.first_name
    `;

    return NextResponse.json(result);
  } catch (error) {
    // Handle errors consistently
    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });
  }
}