import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { v4 as uuidv4 } from 'uuid';
import PDFParser from 'pdf2json';
import { processLongText } from '@/app/lib/processLongText';

export async function POST(req: NextRequest) {
  const formData: FormData = await req.formData();
  const uploadedFiles = formData.getAll('filepond');
  let fileName = '';
  let parsedText = '';

  if (uploadedFiles && uploadedFiles.length > 0) {
    const uploadedFile = uploadedFiles[1]; // Adjust index if needed
    console.log('Uploaded file:', uploadedFile);

    if (uploadedFile instanceof File) {
      fileName = uuidv4();
      const tempFilePath = `/tmp/${fileName}.pdf`;
      const fileBuffer = Buffer.from(await uploadedFile.arrayBuffer());

      await fs.writeFile(tempFilePath, fileBuffer);

      // Wrap pdf2json parsing in a Promise to wait for the result
      parsedText = await new Promise((resolve, reject) => {
        const pdfParser = new (PDFParser as any)(null, 1);

        pdfParser.on('pdfParser_dataError', (errData: any) => {
          console.error('PDF parsing error:', errData.parserError);
          reject(errData.parserError);
        });

        pdfParser.on('pdfParser_dataReady', () => {
          const text = (pdfParser as any).getRawTextContent();
          console.log('Parsed text length:', text.length);
          resolve(text);
        });

        pdfParser.loadPDF(tempFilePath);
      });

      // Clean up the temporary file (optional)
      await fs.unlink(tempFilePath).catch((err) => console.error('Error deleting temp file:', err));
    } else {
      console.log('Uploaded file is not in the expected format.');
      return NextResponse.json({ error: 'Invalid file format' }, { status: 400 });
    }
  } else {
    console.log('No files found.');
    return NextResponse.json({ error: 'No files uploaded' }, { status: 400 });
  }

  try {
    // Process the parsed text to extract publications and activities
    console.log('Processing text with AI...');
    const extractionResult = await processLongText(parsedText);
    
    if (extractionResult.error) {
      console.error('Error during extraction:', extractionResult.error);
      return NextResponse.json(
        { 
          error: 'Failed to extract data', 
          details: extractionResult.error 
        }, 
        { status: 500 }
      );
    }
    
    console.log(`Extracted ${extractionResult.publications.length} publications and ${extractionResult.activities.length} activities`);
    
    // Return the extracted data and original text
    const response = NextResponse.json(
      { 
        publications: extractionResult.publications,
        activities: extractionResult.activities,
        rawText: parsedText,
        fileName: fileName
      },
      { 
        headers: { 'FileName': fileName } 
      }
    );
    console.log(response);
    return response;
  } catch (error) {
    console.error('Error in API route:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process data',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}