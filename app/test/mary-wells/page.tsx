'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";

interface Faculty {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
}

interface ScholarProfile {
  scholar_id: string;
  name: string;
  affiliation: string;
  areas_of_interest: string;
  citations_all: number;
  h_index_all: number;
  profile_url: string;
  profile_image_url: string;
}

interface ProfileData {
  faculty: Faculty;
  scholarProfile: ScholarProfile;
}

export default function MaryWellsPage() {
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/test/mary-wells');
        if (!response.ok) {
          throw new Error(`Failed to fetch profile: ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Profile data:', data);
        setProfileData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  if (error) {
    return <div className="p-8 text-red-500">Error: {error}</div>;
  }

  if (!profileData) {
    return <div className="p-8">No profile data found</div>;
  }

  const { faculty, scholarProfile } = profileData;

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Mary Wells Profile Test</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-6">
          {/* Profile Photo on the left */}
          <div className="flex-shrink-0">
            {scholarProfile && scholarProfile.profile_image_url ? (
              <div className="h-24 w-24 rounded-full overflow-hidden border border-gray-300">
                {!imageError ? (
                  <img
                    src={scholarProfile.profile_image_url}
                    alt={`${faculty.firstName} ${faculty.lastName}`}
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      console.error('Error loading image:', e);
                      setImageError(true);
                    }}
                  />
                ) : (
                  <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                    <span className="text-2xl font-bold text-gray-500">
                      {faculty.firstName.charAt(0)}
                    </span>
                  </div>
                )}
              </div>
            ) : (
              <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center border border-gray-300">
                <span className="text-2xl font-bold text-gray-500">
                  {faculty.firstName.charAt(0)}
                </span>
              </div>
            )}
          </div>

          {/* User Information on the right */}
          <div className="flex flex-col space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p>{faculty.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Name</p>
              <p>{faculty.firstName} {faculty.lastName}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Google Scholar Profile</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Scholar ID</p>
            <p>{scholarProfile.scholar_id}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Name</p>
            <p>{scholarProfile.name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Affiliation</p>
            <p>{scholarProfile.affiliation}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Citations</p>
            <p>{scholarProfile.citations_all}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">H-Index</p>
            <p>{scholarProfile.h_index_all}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Profile Image URL</p>
            <p className="break-all">{scholarProfile.profile_image_url}</p>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-2">Image URL Test</h2>
        <p>Try opening the image directly:</p>
        <a
          href={scholarProfile.profile_image_url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-500 underline"
        >
          Open Image in New Tab
        </a>
      </div>
    </div>
  );
}
