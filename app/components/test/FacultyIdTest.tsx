'use client';

import { useSession } from 'next-auth/react';

export default function FacultyIdTest() {
  const { data: session, status } = useSession();
  
  if (status === 'loading') {
    return <div>Loading session...</div>;
  }
  
  if (status === 'unauthenticated') {
    return <div>Not authenticated</div>;
  }
  
  return (
    <div className="p-4 border rounded-md bg-gray-50">
      <h2 className="text-lg font-semibold mb-2">Session Information</h2>
      <div className="space-y-2">
        <p><strong>User ID:</strong> {session?.user?.id || 'Not available'}</p>
        <p><strong>Email:</strong> {session?.user?.email || 'Not available'}</p>
        <p><strong>Faculty SSO ID:</strong> {session?.user?.facultySsoId || 'Not available'}</p>
        <p><strong>Faculty ID:</strong> {session?.user?.facultyId !== undefined && session?.user?.facultyId !== null ? session.user.facultyId : 'Not available'}</p>
        <p><strong>Roles:</strong> {session?.user?.roles?.join(', ') || 'No roles'}</p>
      </div>
    </div>
  );
}
