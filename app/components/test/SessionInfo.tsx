'use client';

import { useSession } from 'next-auth/react';

export default function SessionInfo() {
  const { data: session } = useSession();
  const environment = process.env.NODE_ENV || 'development';

  return (
    <div className="p-4 bg-gray-100 rounded-lg border-2 border-amber-500">
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-lg font-semibold">Session Information</h2>
        <span className="px-2 py-1 bg-amber-500 text-white text-xs rounded-full">
          {environment.toUpperCase()} ONLY
        </span>
      </div>
      <div className="text-sm text-gray-500 mb-3">
        This component is only visible in non-production environments.
      </div>
      <pre className="bg-white p-3 rounded overflow-auto max-h-96 text-sm">
        {JSON.stringify(session, null, 2)}
      </pre>
      {session?.user?.facultySsoId && (
        <div className="mt-4 p-3 bg-green-100 rounded">
          <p className="font-medium">Faculty SSO ID (lowercase): {session.user.facultySsoId}</p>
        </div>
      )}
    </div>
  );
}
