import <PERSON><PERSON><PERSON> from '@/app/ui/amelia-logo';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { lusitana } from './ui/fonts';
import  Image  from 'next/image';

export default function Page() {
  return (
    <main id="main-content" className="flex min-h-screen flex-col p-6" role="main">
      <header className="flex h-20 shrink-0 items-end rounded-lg bg-gray-50 p-4 md:h-52" role="banner">
        <AmeliaLogo />
      </header>
      <section className="mt-4 flex grow flex-col gap-4 md:flex-row" aria-labelledby="welcome-heading">
        <div className="flex flex-col justify-center gap-6 rounded-lg bg-gray-50 px-6 py-10 md:w-2/5 md:px-20">
          <h1
            id="welcome-heading"
            className={`${lusitana.className} text-xl text-gray-800 md:text-3xl md:leading-normal`}
          >
            <strong>Welcome to <PERSON>.</strong>
          </h1>
          <Link
            href="/login"
            className="flex items-center gap-5 self-start rounded-lg bg-blue-500 px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-blue-400 md:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Log in to Amelia"
          >
            <span>Log in</span> <ArrowRightIcon className="w-5 md:w-6" aria-hidden="true" />
          </Link>
        </div>
      </section>
    </main>
  );
}
