'use client';

import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import { useEffect, useState } from 'react';
import { ScrollToTop } from '@/app/lib/scroll-to-top';

export function Providers({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <SessionProvider>
        <ScrollToTop />
        <div className="flex flex-1 flex-col w-full m-0 font-sans antialiased font-normal text-left leading-default text-base bg-gray-50 text-slate-500">
          {children}
        </div>
      </SessionProvider>
    );
  }

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      disableTransitionOnChange
      storageKey="theme-preference"
      forcedTheme="light"
    >
      <SessionProvider>
        <ScrollToTop />
        {children}
      </SessionProvider>
    </ThemeProvider>
  );
}