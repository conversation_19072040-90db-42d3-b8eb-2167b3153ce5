'use client';

import { useEffect } from 'react';
import { WorkflowCanvas } from '@/components/workflow/WorkflowCanvas';
import { WorkflowToolbar } from '@/components/workflow/WorkflowToolbar';
import { useWorkflowStore } from '@/lib/store/workflowStore';

export default function WorkflowPage() {
  const { initializeSampleWorkflow } = useWorkflowStore();

  useEffect(() => {
    initializeSampleWorkflow();
  }, [initializeSampleWorkflow]);

  return (
    <div className="flex flex-col h-screen">
      <WorkflowToolbar />
      <div className="flex-1 p-4">
        <WorkflowCanvas />
      </div>
    </div>
  );
} 