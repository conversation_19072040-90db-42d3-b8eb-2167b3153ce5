import <PERSON><PERSON><PERSON> from "@/app/ui/amelia-logo";
import LoginForm from "@/app/login/login-form";
import { Suspense } from "react";

export default function LoginPage() {
  return (
    <main id="main-content" className="flex items-center justify-center md:h-screen" role="main">
      <div className="relative mx-auto flex w-full max-w-[400px] flex-col space-y-2.5 p-4 md:-mt-32">
        <header className="flex h-20 w-full items-end justify-center rounded-lg bg-grey-50 p-3 md:h-36" role="banner">
          <div className="w-36 text-white md:w-48">
            <AmeliaLogo />
          </div>
        </header>
        <section aria-labelledby="login-heading">
          <Suspense fallback={<div role="status" aria-live="polite">Loading...</div>}>
            <LoginForm />
          </Suspense>
        </section>
      </div>
    </main>
  );
}