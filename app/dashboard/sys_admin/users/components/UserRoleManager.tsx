'use client';

import { useState, useEffect } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';

// Simple X icon component
const XIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

interface RoleOption {
  id: string;
  name: string;
  type: 'system' | 'institution';
}

interface UserRoleManagerProps {
  userId: string;
  userEmail: string;
  currentSystemRoles: string[];
  currentInstitutionRoles: string[];
  onRolesUpdated: () => void;
}

export default function UserRoleManager({
  userId,
  userEmail,
  currentSystemRoles = [],
  currentInstitutionRoles = [],
  onRolesUpdated
}: UserRoleManagerProps) {
  const [loading, setLoading] = useState(false);
  const [availableSystemRoles, setAvailableSystemRoles] = useState<RoleOption[]>([]);
  const [availableInstitutionRoles, setAvailableInstitutionRoles] = useState<RoleOption[]>([]);
  const [selectedSystemRole, setSelectedSystemRole] = useState<string>('');
  const [selectedInstitutionRole, setSelectedInstitutionRole] = useState<string>('');
  const [isFaculty, setIsFaculty] = useState<boolean>(false);
  const [facultyId, setFacultyId] = useState<string | null>(null);
  const [primaryUnitId, setPrimaryUnitId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Ensure we have arrays and filter out any null or undefined roles
  const filteredSystemRoles = Array.isArray(currentSystemRoles)
    ? currentSystemRoles.filter(Boolean)
    : [];

  const filteredInstitutionRoles = Array.isArray(currentInstitutionRoles)
    ? currentInstitutionRoles.filter(Boolean)
    : [];

  useEffect(() => {
    const fetchAvailableRoles = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/sys_admin/available-roles?userEmail=${encodeURIComponent(userEmail)}`);
        if (response.ok) {
          const { systemRoles, institutionRoles, isFaculty: userIsFaculty, facultyId: userFacultyId, primaryUnitId: userPrimaryUnitId } = await response.json();
          setAvailableSystemRoles(systemRoles);
          setAvailableInstitutionRoles(institutionRoles);
          setIsFaculty(userIsFaculty);
          setFacultyId(userFacultyId);
          setPrimaryUnitId(userPrimaryUnitId);
        } else {
          const error = await response.json();
          setError(error.error || 'Failed to fetch available roles');
        }
      } catch (error) {
        setError('An error occurred while fetching available roles');
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchAvailableRoles();
  }, [userEmail]);

  const handleAddSystemRole = async () => {
    if (!selectedSystemRole) return;

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/sys_admin/user-roles/system', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          roleId: selectedSystemRole,
        }),
      });

      if (response.ok) {
        const { roleName } = await response.json();
        setSuccess(`System role "${roleName}" added successfully`);
        setSelectedSystemRole('');
        // Notify parent component to refresh user data
        onRolesUpdated();
      } else {
        const error = await response.json();
        setError(error.error || 'Failed to add system role');
      }
    } catch (error) {
      setError('An error occurred while adding the system role');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddInstitutionRole = async () => {
    if (!selectedInstitutionRole) return;

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/sys_admin/user-roles/institution', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userEmail,
          roleId: selectedInstitutionRole,
          facultyId,
          primaryUnitId,
        }),
      });

      if (response.ok) {
        const { roleName } = await response.json();
        setSuccess(`Institution role "${roleName}" added successfully`);
        setSelectedInstitutionRole('');
        // Notify parent component to refresh user data
        onRolesUpdated();
      } else {
        const error = await response.json();
        setError(error.error || 'Failed to add institution role');
      }
    } catch (error) {
      setError('An error occurred while adding the institution role');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveSystemRole = async (roleName: string) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/sys_admin/user-roles/system', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          roleName,
        }),
      });

      if (response.ok) {
        setSuccess(`System role "${roleName}" removed successfully`);
        // Notify parent component to refresh user data
        onRolesUpdated();
      } else {
        const error = await response.json();
        setError(error.error || 'Failed to remove system role');
      }
    } catch (error) {
      setError('An error occurred while removing the system role');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveInstitutionRole = async (roleName: string) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/sys_admin/user-roles/institution', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userEmail,
          roleName,
          facultyId,
        }),
      });

      if (response.ok) {
        setSuccess(`Institution role "${roleName}" removed successfully`);
        // Notify parent component to refresh user data
        onRolesUpdated();
      } else {
        const error = await response.json();
        setError(error.error || 'Failed to remove institution role');
      }
    } catch (error) {
      setError('An error occurred while removing the institution role');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Filter out roles the user already has
  const filteredAvailableSystemRoles = availableSystemRoles.filter(
    role => !filteredSystemRoles.includes(role.name)
  );

  const filteredAvailableInstitutionRoles = availableInstitutionRoles.filter(
    role => !filteredInstitutionRoles.includes(role.name)
  );

  return (
    <div className="space-y-4">
      <div className="space-y-6">
        {/* System Roles Section */}
        <div>
          <h3 className="text-sm font-medium mb-2">System Roles</h3>
          <div className="flex flex-wrap gap-2 mb-4">
            {filteredSystemRoles.length > 0 ? (
              filteredSystemRoles.map(role => (
                <span key={role} className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
                  {role}
                  <button
                    onClick={() => handleRemoveSystemRole(role)}
                    className="ml-1 text-xs text-blue-400 hover:text-blue-600"
                    aria-label={`Remove ${role} role`}
                    disabled={loading}
                  >
                    <XIcon />
                  </button>
                </span>
              ))
            ) : (
              <p className="text-sm text-gray-500">No system roles assigned</p>
            )}
          </div>

          {filteredAvailableSystemRoles.length > 0 && (
            <div className="flex items-end gap-2">
              <div className="flex-1">
                <label htmlFor="system-role-select" className="text-sm font-medium mb-2 block">
                  Add System Role
                </label>
                <select
                  id="system-role-select"
                  value={selectedSystemRole}
                  onChange={(e) => setSelectedSystemRole(e.target.value)}
                  className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-sm"
                  disabled={loading}
                >
                  <option value="">Select a system role</option>
                  {filteredAvailableSystemRoles.map(role => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>
              <button
                onClick={handleAddSystemRole}
                disabled={!selectedSystemRole || loading}
                className="flex items-center gap-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PlusIcon className="h-4 w-4" />
                Add
              </button>
            </div>
          )}
        </div>

        {/* Institution Roles Section */}
        <div>
          <h3 className="text-sm font-medium mb-2">Institution Roles</h3>
          <div className="flex flex-wrap gap-2 mb-4">
            {filteredInstitutionRoles.length > 0 ? (
              filteredInstitutionRoles.map(role => (
                <span key={role} className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800">
                  {role}
                  <button
                    onClick={() => handleRemoveInstitutionRole(role)}
                    className="ml-1 text-xs text-green-400 hover:text-green-600"
                    aria-label={`Remove ${role} role`}
                    disabled={loading}
                  >
                    <XIcon />
                  </button>
                </span>
              ))
            ) : (
              <p className="text-sm text-gray-500">No institution roles assigned</p>
            )}
          </div>

          {isFaculty && filteredAvailableInstitutionRoles.length > 0 ? (
            <div className="flex items-end gap-2">
              <div className="flex-1">
                <label htmlFor="institution-role-select" className="text-sm font-medium mb-2 block">
                  Add Institution Role
                </label>
                <select
                  id="institution-role-select"
                  value={selectedInstitutionRole}
                  onChange={(e) => setSelectedInstitutionRole(e.target.value)}
                  className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-sm"
                  disabled={loading}
                >
                  <option value="">Select an institution role</option>
                  {filteredAvailableInstitutionRoles.map(role => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>
              <button
                onClick={handleAddInstitutionRole}
                disabled={!selectedInstitutionRole || loading}
                className="flex items-center gap-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PlusIcon className="h-4 w-4" />
                Add
              </button>
            </div>
          ) : !isFaculty ? (
            <p className="text-sm text-gray-500 italic">
              User is not a faculty member. Institution roles can only be assigned to faculty members.
            </p>
          ) : null}
        </div>
      </div>



      {error && (
        <div className="text-sm text-red-500 mt-2">{error}</div>
      )}

      {success && (
        <div className="text-sm text-green-500 mt-2">{success}</div>
      )}
    </div>
  );
}
