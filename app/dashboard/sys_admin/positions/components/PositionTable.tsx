"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import SearchBar from "./SearchBar";

interface Position {
  position_id: number;
  title: string;
  description: string | null;
}

export default function PositionTable() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    const fetchPositions = async () => {
      try {
        const response = await fetch('/api/admin/positions');
        if (!response.ok) {
          throw new Error('Failed to fetch position data');
        }
        const data = await response.json();
        setPositions(data);
        setFilteredPositions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchPositions();
  }, []);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredPositions(positions);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = positions.filter(position => 
      (position.title?.toLowerCase() || '').includes(query) ||
      (position.description?.toLowerCase() || '').includes(query)
    );
    setFilteredPositions(filtered);
  }, [searchQuery, positions]);

  if (loading) return <div>Loading position data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="space-y-4">
      <div className="max-w-md">
        <SearchBar 
          onSearch={setSearchQuery}
          placeholder="Search by title or description..."
        />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Description</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPositions.map((position) => (
              <TableRow key={position.position_id}>
                <TableCell>{position.position_id}</TableCell>
                <TableCell>{position.title}</TableCell>
                <TableCell>{position.description || ''}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <div className="text-sm text-gray-500">
        Showing {filteredPositions.length} of {positions.length} positions
      </div>
    </div>
  );
} 