"use client";

import { useEffect, useState } from "react";
import SearchBar from "./SearchBar";

interface Position {
  position_id: number;
  title: string;
  description: string | null;
}

export default function PositionHierarchy() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    const fetchPositions = async () => {
      try {
        const response = await fetch('/api/admin/positions');
        if (!response.ok) {
          throw new Error('Failed to fetch position data');
        }
        const data = await response.json();
        setPositions(data);
        setFilteredPositions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchPositions();
  }, []);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredPositions(positions);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = positions.filter(position =>
      (position.title?.toLowerCase() || '').includes(query) ||
      (position.description?.toLowerCase() || '').includes(query)
    );
    setFilteredPositions(filtered);
  }, [searchQuery, positions]);

  if (loading) return <div>Loading data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="space-y-4">
      <div className="max-w-md">
        <SearchBar 
          onSearch={setSearchQuery}
          placeholder="Search by title or description..."
        />
      </div>
      <div className="border rounded-md p-4">
        <div className="space-y-4">
          {filteredPositions.map((position) => (
            <div key={position.position_id} className="p-3 hover:bg-gray-50 rounded-md">
              <div className="font-medium">{position.title}</div>
              {position.description && (
                <div className="text-sm text-gray-500 mt-1">{position.description}</div>
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="text-sm text-gray-500">
        Showing {filteredPositions.length} of {positions.length} positions
      </div>
    </div>
  );
} 