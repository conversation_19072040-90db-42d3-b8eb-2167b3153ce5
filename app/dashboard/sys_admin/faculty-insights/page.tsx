"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { <PERSON><PERSON><PERSON>, Line } from 'recharts';
import { <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';

// Placeholder data for charts
const teachingLoadData = [
  { name: 'Jan', value: 12 },
  { name: 'Feb', value: 15 },
  { name: 'Mar', value: 13 },
  { name: 'Apr', value: 14 },
  { name: 'May', value: 16 },
  { name: 'Jun', value: 11 },
];

const researchOutputData = [
  { name: '2020', publications: 45, citations: 120 },
  { name: '2021', publications: 52, citations: 150 },
  { name: '2022', publications: 60, citations: 180 },
  { name: '2023', publications: 65, citations: 200 },
];

const facultyDistributionData = [
  { name: 'Full Professor', value: 35 },
  { name: 'Associate Professor', value: 45 },
  { name: 'Assistant Professor', value: 20 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28'];

export default function FacultyInsightsPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Faculty Insights</h1>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Teaching Load Card */}
        <Card>
          <CardHeader>
            <CardTitle>Average Teaching Load</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={teachingLoadData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Research Output Card */}
        <Card>
          <CardHeader>
            <CardTitle>Research Output Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={researchOutputData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="publications" stroke="#8884d8" />
                  <Line type="monotone" dataKey="citations" stroke="#82ca9d" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Faculty Distribution Card */}
        <Card>
          <CardHeader>
            <CardTitle>Faculty Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={facultyDistributionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {facultyDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle>Total Faculty</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">1,234</p>
            <p className="text-sm text-gray-500">+12% from last year</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Average Tenure</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">8.5 years</p>
            <p className="text-sm text-gray-500">+0.5 years from last year</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Research Funding</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">$45.2M</p>
            <p className="text-sm text-gray-500">+15% from last year</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Student Satisfaction</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">92%</p>
            <p className="text-sm text-gray-500">+2% from last year</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 