import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import FacultyTable from "./components/FacultyTable";
import FacultyHierarchy from "./components/FacultyHierarchy";

export default function FacultyPage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Faculty Data</h1>
      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="hierarchy">Hierarchy View</TabsTrigger>
        </TabsList>
        <TabsContent value="table">
          <FacultyTable />
        </TabsContent>
        <TabsContent value="hierarchy">
          <FacultyHierarchy />
        </TabsContent>
      </Tabs>
    </div>
  );
} 