"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import SearchBar from "./SearchBar";

interface Faculty {
  intelicampus_id: string;
  sso_id: string;
  first_name: string;
  last_name: string;
  work_email: string;
  primary_unit_id: number;
  unit_name: string;
  primary_unit_percentage: number;
  tenure_status: string;
  position_id: number;
  job_family: string;
}

export default function FacultyTable() {
  const [faculty, setFaculty] = useState<Faculty[]>([]);
  const [filteredFaculty, setFilteredFaculty] = useState<Faculty[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    const fetchFaculty = async () => {
      try {
        const response = await fetch('/api/sys_admin/faculty');
        if (!response.ok) {
          throw new Error('Failed to fetch faculty data');
        }
        const data = await response.json();
        setFaculty(data);
        setFilteredFaculty(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchFaculty();
  }, []);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredFaculty(faculty);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = faculty.filter(member => 
      (member.first_name?.toLowerCase() || '').includes(query) ||
      (member.last_name?.toLowerCase() || '').includes(query) ||
      (member.work_email?.toLowerCase() || '').includes(query) ||
      (member.unit_name?.toLowerCase() || '').includes(query) ||
      (member.tenure_status?.toLowerCase() || '').includes(query) ||
      (member.job_family?.toLowerCase() || '').includes(query)
    );
    setFilteredFaculty(filtered);
  }, [searchQuery, faculty]);

  if (loading) return <div>Loading faculty data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="space-y-4">
      <div className="max-w-md">
        <SearchBar 
          onSearch={setSearchQuery}
          placeholder="Search by name, email, unit, tenure status, or job family..."
        />
      </div>
      <div className="rounded-md border">
        <Table aria-label="Faculty members list">
          <caption className="sr-only">
            List of faculty members with their ID, names, email, unit, FTE, tenure status, position ID, and job family
          </caption>
          <TableHeader>
            <TableRow>
              <TableHead scope="col">ID</TableHead>
              <TableHead scope="col">First Name</TableHead>
              <TableHead scope="col">Last Name</TableHead>
              <TableHead scope="col">Email</TableHead>
              <TableHead scope="col">Unit</TableHead>
              <TableHead scope="col">FTE</TableHead>
              <TableHead scope="col">Tenure Status</TableHead>
              <TableHead scope="col">Position ID</TableHead>
              <TableHead scope="col">Job Family</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredFaculty.map((member) => (
              <TableRow key={member.intelicampus_id}>
                <TableCell>{member.intelicampus_id}</TableCell>
                <TableCell>{member.first_name}</TableCell>
                <TableCell>{member.last_name}</TableCell>
                <TableCell>{member.work_email}</TableCell>
                <TableCell>{member.unit_name}</TableCell>
                <TableCell>{member.primary_unit_percentage}</TableCell>
                <TableCell>{member.tenure_status}</TableCell>
                <TableCell>{member.position_id}</TableCell>
                <TableCell>{member.job_family}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <div className="text-sm text-gray-500">
        Showing {filteredFaculty.length} of {faculty.length} faculty members
      </div>
    </div>
  );
} 