import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { UnitTable } from "./components/UnitTable";
import { UnitHierarchy } from "./components/UnitHierarchy";

export default function UnitsPage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Unit Management</h1>
      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="hierarchy">Hierarchy View</TabsTrigger>
        </TabsList>
        <TabsContent value="table">
          <UnitTable />
        </TabsContent>
        <TabsContent value="hierarchy">
          <UnitHierarchy />
        </TabsContent>
      </Tabs>
    </div>
  );
} 