{"position": {"position_id": "Reference ID", "position_title": "Business Title", "position_description": "Position and Job - All Staffing Models"}, "faculty": {"intelicampus_id": "Operator ID", "sso_id": "Operator ID", "first_name": "First Name", "last_name": "Last Name", "work_email": "Email - Work", "primary_unit_id": "Department ID", "primary_unit_percentage": "FTE", "date_started": "Hire Date", "tenure_status": "Tenure Status", "position_id": "Position ID", "job_family": "Job Family"}, "unit": {"unit_id": "Unit_ID", "full_name": "Unit Full Name", "short_name": "Unit Short Name", "abbreviation": "Unit Abbreviation", "level_number": "Level Number", "parent_unit_id": "Parent Unit", "unit_type": "Type", "previous_name_1": "Unit Previous Name #1"}}