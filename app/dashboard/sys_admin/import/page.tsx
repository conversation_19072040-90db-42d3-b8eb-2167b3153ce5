import { lusitana } from '@/app/ui/fonts';
import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/lib/auth";
import ImportForm from '@/app/ui/dashboard/sys_admin/import-form';

export default async function ImportPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  if (!roles.includes("system_admin") && !roles.includes("faculty_admin")) {
    redirect("/dashboard");
  }

  return (
    <div className="w-full">
      <div className="flex w-full items-center justify-between">
        <h1 className={`${lusitana.className} text-2xl`}>Import Data</h1>
      </div>
      <div className="mt-4">
        <ImportForm />
      </div>
    </div>
  );
}