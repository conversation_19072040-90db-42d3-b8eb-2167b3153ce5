import { Metada<PERSON> } from "next";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Import Data",
  description: "Import data for various tables",
};

export default function ImportLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <CardTitle>Import Data</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="faculty" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="faculty" asChild>
                <Link href="/dashboard/sys_admin/import/faculty">Faculty</Link>
              </TabsTrigger>
              <TabsTrigger value="position" asChild>
                <Link href="/dashboard/sys_admin/import/position">Position</Link>
              </TabsTrigger>
              <TabsTrigger value="unit" asChild>
                <Link href="/dashboard/sys_admin/import/unit">Unit</Link>
              </TabsTrigger>
            </TabsList>
            <TabsContent value="faculty" className="mt-6">
              {children}
            </TabsContent>
            <TabsContent value="position" className="mt-6">
              {children}
            </TabsContent>
            <TabsContent value="unit" className="mt-6">
              {children}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
} 