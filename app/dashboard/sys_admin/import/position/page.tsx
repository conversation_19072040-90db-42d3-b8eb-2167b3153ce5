import { ImportForm } from "../components/ImportForm";

const positionColumnMapping = {
  position_id: "Reference ID",
  position_title: "Business Title",
  position_description: "Position and Job - All Staffing Models",
};

export default function PositionImportPage() {
  return (
    <ImportForm
      title="Import Position Data"
      description="Import position information from an Excel file. The file should contain columns matching the mapping below. Do not include any other text in the file, for example, do not include a header row."
      columnMapping={positionColumnMapping}
      endpoint="/api/admin/import/position"
    />
  );
} 