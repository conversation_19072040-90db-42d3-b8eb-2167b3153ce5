import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { sql } from "@/app/lib/db";
import { authOptions } from "@/app/lib/auth";
import { lusitana } from '@/app/ui/fonts';
import { formatDistanceToNow } from 'date-fns';

export default async function ChangelogPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  if (!roles.includes("system_admin") && !roles.includes("faculty_admin")) {
    redirect("/dashboard");
  }

  // Fetch changelog entries
  const changelogEntries = await sql`
    SELECT
      c.changelog_id,
      c.timestamp,
      c.page_url,
      c.table_name,
      c.record_id,
      c.sql_query,
      u.email as user_email,
      u.display_name as user_name
    FROM uw.changelog c
    JOIN common.user u ON c.user_id = u.user_id
    WHERE c.is_deleted = false
    ORDER BY c.timestamp DESC
    LIMIT 100
  `;

  return (
    <div className="w-full">
      <div className="flex w-full items-center justify-between">
        <h1 className={`${lusitana.className} text-2xl`}>System Change Log</h1>
      </div>

      <div className="mt-6">
        <div className="rounded-lg bg-white shadow">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Page
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Table
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Record ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SQL Query
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {changelogEntries.map((entry) => (
                <tr key={entry.changelog_id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="text-sm text-gray-900">
                      {new Date(entry.timestamp).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatDistanceToNow(new Date(entry.timestamp), { addSuffix: true })}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{entry.user_name || 'Unknown'}</div>
                    <div className="text-sm text-gray-500">{entry.user_email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {entry.page_url}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {entry.table_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {entry.record_id}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    <code className="bg-gray-100 p-1 rounded">{entry.sql_query}</code>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
