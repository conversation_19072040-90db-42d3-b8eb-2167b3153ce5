import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import CommitteeTable from "./components/CommitteeTable";
import CommitteeHierarchy from "./components/CommitteeHierarchy";

export default function CommitteePage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Committee Data</h1>
      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="hierarchy">Hierarchy View</TabsTrigger>
        </TabsList>
        <TabsContent value="table">
          <CommitteeTable />
        </TabsContent>
        <TabsContent value="hierarchy">
          <CommitteeHierarchy />
        </TabsContent>
      </Tabs>
    </div>
  );
} 