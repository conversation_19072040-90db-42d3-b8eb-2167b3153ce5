"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface Committee {
  committee_id: number;
  name: string;
  short_name: string;
  primary_unit_id: number;
  effective_date: string;
  previous_name: string | null;
  description: string | null;
  is_deleted: boolean;
}

export default function CommitteeTable() {
  const [committees, setCommittees] = useState<Committee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCommittees = async () => {
      try {
        const response = await fetch('/api/admin/committees');
        if (!response.ok) {
          throw new Error('Failed to fetch committee data');
        }
        const data = await response.json();
        setCommittees(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCommittees();
  }, []);

  if (loading) return <div>Loading data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Short Name</TableHead>
            <TableHead>Primary Unit</TableHead>
            <TableHead>Effective Date</TableHead>
            <TableHead>Previous Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {committees.map((committee) => (
            <TableRow key={committee.committee_id}>
              <TableCell>{committee.name}</TableCell>
              <TableCell>{committee.short_name}</TableCell>
              <TableCell>{committee.primary_unit_id}</TableCell>
              <TableCell>{new Date(committee.effective_date).toLocaleDateString()}</TableCell>
              <TableCell>{committee.previous_name || '-'}</TableCell>
              <TableCell>{committee.description || '-'}</TableCell>
              <TableCell>{committee.is_deleted ? 'Deleted' : 'Active'}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 