import Pagination from '@/app/ui/common/pagination';
import Search from '@/app/ui/search';
import FacultyTable from '@/app/ui/engrecords/faculty-table';
import FacultyFilters from '@/app/ui/engrecords/faculty-filters';
import { lusitana } from '@/app/ui/fonts';
import { InvoicesTableSkeleton } from '@/app/ui/skeletons';
import { Suspense } from 'react';
import { sql } from '@/app/lib/db';

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{
    query?: string;
    page?: string;
    facGroup?: string;
    orgUnit?: string;
  }>;
}) {
  const { query = '', page = '1', facGroup = '', orgUnit = '' } = await searchParams;
  const currentPage = Number(page);
  let totalPages = 1;

  try {
    // Build the WHERE clause for filters
    let whereClause = sql`active = 'Y'`;

    // Add search condition
    if (query) {
      whereClause = sql`${whereClause} AND (
        first_name ILIKE ${`%${query}%`} OR
        last_name ILIKE ${`%${query}%`} OR
        nexus ILIKE ${`%${query}%`} OR
        CONCAT(first_name, ' ', last_name) ILIKE ${`%${query}%`}
      )`;
    }

    // Add faculty group filter
    if (facGroup) {
      whereClause = sql`${whereClause} AND fac_group = ${facGroup}`;
    }

    // Add organization unit filter
    if (orgUnit) {
      whereClause = sql`${whereClause} AND fac_org_unit = ${orgUnit}`;
    }

    // Get total count for pagination with filters
    const countResult = await sql`
      SELECT COUNT(*) as count
      FROM engrecords.eng_fac
      WHERE ${whereClause}
    `;
    const totalCount = Number(countResult[0]?.count || 0);
    totalPages = Math.ceil(totalCount / 10);
  } catch (error) {
    console.error('Error fetching faculty count:', error);
    // If there's an error, we'll just show one page
    totalPages = 1;
  }

  return (
    <div className="w-full px-4 md:px-6 max-w-7xl mx-auto">
      <div className="flex w-full items-center justify-between">
        <h1 className={`${lusitana.className} text-2xl`}>Faculty Roster - Engineering</h1>
      </div>
      <div className="mt-4 md:mt-8 flex flex-col md:flex-row items-center gap-4 w-full">
        <div className="flex w-full gap-4">
          <Search placeholder="Search faculty..." />
          <FacultyFilters defaultFacGroup="Faculty" />
        </div>
      </div>
      <Suspense key={`${query}-${currentPage}-${facGroup}-${orgUnit}`} fallback={<InvoicesTableSkeleton />}>
        <FacultyTable
          query={query}
          currentPage={currentPage}
          facGroup={facGroup}
          orgUnit={orgUnit}
        />
      </Suspense>
      <div className="mt-5 flex w-full justify-center">
        <Pagination totalPages={totalPages} />
      </div>
    </div>
  );
}