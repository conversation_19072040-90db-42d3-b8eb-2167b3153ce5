import { notFound } from 'next/navigation';
import { lusitana } from '@/app/ui/fonts';
import { sql } from '@/app/lib/db';
import AppointmentsTabs from '@/app/ui/engrecords/appointments-tabs';
import EditableFacultyInfo from '@/app/ui/engrecords/editable-faculty-info';
import EditableDegrees from '@/app/ui/engrecords/editable-degrees';
import EditableAwards from '@/app/ui/engrecords/editable-awards';
import EditableLicensing from '@/app/ui/engrecords/editable-licensing';
import EditableLeaves from '@/app/ui/engrecords/editable-leaves';
import ChangeHistoryButton from '@/app/ui/engrecords/change-history-button';

interface Faculty {
  nexus: string;
  first_name: string;
  last_name: string;
  appt_title: string;
  descr: string;
  fac_org_unit: string;
  unit_full_name: string;
  appt_type: string;
  track_type: string;
  fac_group: string;
  fac_rank_desc: string;
}

interface FacultyAppointment {
  auto_nbr: number;
  fac_category: string;
  fac_stage: string;
  fac_type: string;
  fac_rank_desc: string;
  fac_eff_from_dt: string;
  fac_eff_to_dt: string | null;
  fac_teaching_perc: number;
  fac_research_perc: number;
  fac_service_perc: number;
  fac_merit_cycle: string;
  fac_notes: string | null;
}

interface AdminAppointment {
  auto_nbr: number;
  admin_title_prefix: string | null;
  admin_title: string;
  admin_portfolio: string | null;
  admin_program: string | null;
  admin_eff_from_dt: string;
  admin_eff_to_dt: string | null;
  admin_stipend_duration: string;
  admin_stipend: number;
  admin_teaching_release: number;
  admin_research_supp: number;
  admin_teaching_perc: number;
  admin_research_perc: number;
  admin_service_perc: number;
  admin_notes: string | null;
}

interface CrossAppointment {
  auto_nbr: number;
  cross_eff_from_dt: string;
  cross_eff_to_dt: string | null;
  cross_other_org_unit: string;
  unit_full_name?: string;
  cross_notes: string | null;
}

interface JointAppointment {
  auto_nbr: number;
  joint_eff_from_dt: string;
  joint_eff_to_dt: string | null;
  joint_home_org_unit: string;
  joint_home_dept_fte: number;
  joint_org_unit_1: string;
  joint_dept_fte_1: number;
  joint_org_unit_2: string | null;
  joint_dept_fte_2: number;
  joint_org_unit_3: string | null;
  joint_dept_fte_3: number;
  joint_notes: string | null;
}

interface OverloadAppointment {
  auto_nbr: number;
  over_eff_from_dt: string;
  over_eff_to_dt: string | null;
  over_notes: string | null;
}

interface Degree {
  auto_nbr: number;
  deg_type: string;
  deg_yr: string;
  deg_name: string;
  deg_institution: string;
  deg_province_name: string;
}

interface Award {
  auto_nbr: number;
  award_category: string;
  award_name: string;
  award_nom_dt: string;
  award_status: string;
  award_yr: string;
}

interface License {
  auto_nbr: number;
  lcnse_name: string;
  lcnse_nbr: string;
  lcnse_province_name: string;
  lcnse_regn_dt: string;
  lcnse_ppe_dt: string;
  lcnse_status: string;
  lcnse_regn_status: string;
  lcnse_notes: string;
}

interface Leave {
  auto_nbr: number;
  leave_type: string;
  leave_eff_from_dt: string;
  leave_eff_to_dt: string;
  leave_salary_perc: string;
  leave_board_approved: string;
  leave_notes: string;
}

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  try {
    // Get faculty data with latest rank from appts_fac
    const facultyResult = await sql<{ nexus: string; first_name: string; last_name: string; appt_title: string; descr: string; fac_org_unit: string; appt_type: string; track_type: string; fac_group: string; fac_rank_desc?: string; }[]>`
      WITH latest_ranks AS (
        SELECT
          fac_nexus,
          fac_rank_desc,
          ROW_NUMBER() OVER (PARTITION BY fac_nexus ORDER BY fac_eff_from_dt DESC) as rn
        FROM engrecords.appts_fac
      )
      SELECT
        f.nexus,
        f.first_name,
        f.last_name,
        f.appt_title,
        f.descr,
        f.fac_org_unit,
        f.appt_type,
        f.track_type,
        f.fac_group,
        lr.fac_rank_desc
      FROM engrecords.eng_fac f
      LEFT JOIN latest_ranks lr ON f.nexus = lr.fac_nexus AND lr.rn = 1
      WHERE f.nexus = ${id}
    `;

    if (facultyResult.length === 0) {
      notFound();
    }

    // Get unit full name from uw.unit table
    let unitFullName = '';
    if (facultyResult[0].fac_org_unit) {
      try {
        const unitResults = await sql`
          SELECT full_name
          FROM uw.unit
          WHERE unit_id = ${facultyResult[0].fac_org_unit}::integer
        `;

        if (unitResults.length > 0) {
          unitFullName = unitResults[0].full_name;
        }
      } catch (error) {
        console.error('Error fetching unit name:', error);
      }
    }

    // Combine faculty data with unit full name
    const faculty: Faculty = {
      ...facultyResult[0],
      unit_full_name: unitFullName,
      fac_rank_desc: facultyResult[0].fac_rank_desc || ''
    };

    if (!faculty) {
      notFound();
    }

    const facultyAppointments = await sql<FacultyAppointment[]>`
      SELECT
        auto_nbr,
        fac_category,
        fac_stage,
        fac_type,
        fac_rank_desc,
        fac_eff_from_dt,
        fac_eff_to_dt,
        fac_teaching_perc,
        fac_research_perc,
        fac_service_perc,
        fac_merit_cycle,
        fac_notes
      FROM engrecords.appts_fac
      WHERE fac_nexus = ${id}
      ORDER BY fac_eff_from_dt DESC
    `;

    const adminAppointments = await sql<AdminAppointment[]>`
      SELECT
        auto_nbr,
        admin_title_prefix,
        admin_title,
        admin_portfolio,
        admin_program,
        admin_eff_from_dt,
        admin_eff_to_dt,
        admin_stipend_duration,
        admin_stipend,
        admin_teaching_release,
        admin_research_supp,
        admin_teaching_perc,
        admin_research_perc,
        admin_service_perc,
        admin_notes
      FROM engrecords.appts_admin
      WHERE fac_nexus = ${id}
      ORDER BY admin_eff_from_dt DESC
    `;

    const crossAppointments = await sql<CrossAppointment[]>`
      SELECT
        ac.auto_nbr,
        ac.cross_eff_from_dt,
        ac.cross_eff_to_dt,
        ac.cross_other_org_unit,
        u.full_name as unit_full_name,
        ac.cross_notes
      FROM engrecords.appts_cross ac
      LEFT JOIN uw.unit u ON u.unit_id = ac.cross_other_org_unit::integer
      WHERE ac.fac_nexus = ${id}
      ORDER BY ac.cross_eff_from_dt DESC
    `;

    const jointAppointments = await sql<JointAppointment[]>`
      SELECT
        auto_nbr,
        joint_eff_from_dt,
        joint_eff_to_dt,
        joint_home_org_unit,
        joint_home_dept_fte,
        joint_org_unit_1,
        joint_dept_fte_1,
        joint_org_unit_2,
        joint_dept_fte_2,
        joint_org_unit_3,
        joint_dept_fte_3,
        joint_notes
      FROM engrecords.appts_joint
      WHERE fac_nexus = ${id}
      ORDER BY joint_eff_from_dt DESC
    `;

    const overloadAppointments = await sql<OverloadAppointment[]>`
      SELECT
        auto_nbr,
        over_eff_from_dt,
        over_eff_to_dt,
        over_notes
      FROM engrecords.appts_over
      WHERE fac_nexus = ${id}
      ORDER BY over_eff_from_dt DESC
    `;

    const degrees = await sql<Degree[]>`
      SELECT
        auto_nbr,
        deg_type,
        deg_yr,
        deg_name,
        deg_institution,
        deg_province_name
      FROM engrecords.degrees
      WHERE fac_nexus = ${id}
      ORDER BY deg_yr DESC
    `;

    const awards = await sql<Award[]>`
      SELECT
        auto_nbr,
        award_category,
        award_name,
        award_nom_dt,
        award_status,
        award_yr
      FROM engrecords.awards
      WHERE fac_nexus = ${id}
      ORDER BY award_yr DESC
    `;

    const licenses = await sql<License[]>`
      SELECT
        auto_nbr,
        lcnse_name,
        lcnse_nbr,
        lcnse_province_name,
        lcnse_regn_dt,
        lcnse_ppe_dt,
        lcnse_status,
        lcnse_regn_status,
        lcnse_notes
      FROM engrecords.licensing
      WHERE fac_nexus = ${id}
      ORDER BY lcnse_regn_dt DESC
    `;

    const leaves = await sql<Leave[]>`
      SELECT
        auto_nbr,
        leave_type,
        leave_eff_from_dt,
        leave_eff_to_dt,
        leave_salary_perc,
        leave_board_approved,
        leave_notes
      FROM engrecords.leaves
      WHERE fac_nexus = ${id}
      ORDER BY leave_eff_from_dt DESC
    `;

    return (
      <div className="w-full px-4 md:px-6 max-w-7xl mx-auto">
        <div className="mb-8 flex justify-between items-center">
          <h1 className={`${lusitana.className} text-2xl`}>
            {faculty.first_name} {faculty.last_name}
          </h1>
          <ChangeHistoryButton />
        </div>

        <div className="grid gap-6 overflow-x-auto">
          <EditableFacultyInfo faculty={faculty} />

          <div className="overflow-x-auto">
            <AppointmentsTabs
              facultyAppointments={facultyAppointments}
              adminAppointments={adminAppointments}
              crossAppointments={crossAppointments}
              jointAppointments={jointAppointments}
              overloadAppointments={overloadAppointments}
            />
          </div>

          <div className="overflow-x-auto">
            <EditableDegrees degrees={degrees} />
          </div>

          <div className="overflow-x-auto">
            <EditableAwards awards={awards} />
          </div>

          <div className="overflow-x-auto">
            <EditableLicensing licenses={licenses} />
          </div>

          <div className="overflow-x-auto">
            <EditableLeaves leaves={leaves} />
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching faculty details:', error);
    notFound();
  }
}