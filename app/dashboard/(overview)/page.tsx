'use client';

import { lusitana } from '@/app/ui/fonts';
import { useState, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Send,
  Calendar,
  Clock,
  BookOpen,
  Award,
  FileText,
  Lightbulb,
  Search,
  User,
  BarChart,
  Star
} from 'lucide-react';

// Suggested prompts for the chat interface
const suggestedPrompts = [
  "Show me my upcoming deadlines",
  "What are my recent publications?",
  "Update my profile information",
  "Show me my teaching schedule",
  "Help me find research collaborators",
  "How do I submit a merit review?",
  "Show me my research metrics"
];

// Upcoming tasks example data (in a real app, this would come from an API)
const upcomingTasks = [
  { id: 1, title: "Submit Merit Review Report", dueDate: "2023-12-15", priority: "High" },
  { id: 2, title: "Complete Course Evaluations", dueDate: "2023-12-10", priority: "Medium" },
  { id: 3, title: "Update Research Profile", dueDate: "2023-12-20", priority: "Low" }
];

// Research highlights example data
const researchHighlights = [
  { id: 1, title: "Machine Learning", papers: 12, citations: 245 },
  { id: 2, title: "Artificial Intelligence", papers: 8, citations: 187 },
  { id: 3, title: "Data Science", papers: 5, citations: 92 }
];

export default function Page() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [messages, setMessages] = useState<{role: string, content: string}[]>([
    { role: 'system', content: 'Welcome to Amelia! How can I help you today?' }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Redirect institutional admins
  useEffect(() => {
    const roles = session?.user?.roles || [];
    if (roles.includes("institutional_admin")) {
      router.push("/dashboard/sys_admin");
    }
  }, [session, router]);

  // Scroll to bottom of messages container, not the entire page
  useEffect(() => {
    if (messagesEndRef.current) {
      const chatContainer = messagesEndRef.current.parentElement;
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }
  }, [messages]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // Add user message to chat
    const userMessage = { role: 'user', content: input };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    // Simulate AI response (in a real app, this would call an API)
    setTimeout(() => {
      let response = '';

      // Simple pattern matching for demo purposes
      if (input.toLowerCase().includes('deadline')) {
        response = "I found these upcoming deadlines for you:\n- Merit Review submission due on December 15\n- Course Evaluations due on December 10";
      } else if (input.toLowerCase().includes('publication')) {
        response = "Your recent publications include:\n- 'Machine Learning Applications in Education' (2023)\n- 'Data Science for Academic Research' (2022)";
      } else if (input.toLowerCase().includes('profile')) {
        response = "You can update your profile by going to the Profile section in the sidebar menu.";
      } else if (input.toLowerCase().includes('merit review')) {
        response = "To submit a merit review, navigate to Merit Review > My Submission in the sidebar menu.";
      } else {
        response = "I'm here to help you navigate the system. You can ask about deadlines, publications, profile updates, and more.";
      }

      setMessages(prev => [...prev, { role: 'system', content: response }]);
      setIsLoading(false);
    }, 1000);
  };

  // Handle clicking a suggested prompt
  const handlePromptClick = (prompt: string) => {
    setInput(prompt);
  };

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  return (
    <main className="space-y-6">
      <h1 className={`${lusitana.className} text-xl md:text-2xl font-bold`}>
        Welcome, {session?.user?.name || 'User'}
      </h1>

      {/* Chat interface */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Amelia Assistant</CardTitle>
          <CardDescription>
            Ask me anything about your academic profile, research, or university services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] overflow-y-auto mb-4 space-y-4 p-4 rounded-lg bg-slate-50">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  {message.content.split('\n').map((line, i) => (
                    <p key={i} className={i > 0 ? 'mt-2' : ''}>{line}</p>
                  ))}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="max-w-[80%] rounded-lg p-3 bg-white border border-gray-200">
                  <div className="flex space-x-2">
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-100"></div>
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-200"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
          <form onSubmit={handleSubmit} className="flex space-x-2">
            <Input
              placeholder="What would you like to accomplish today?"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" size="icon" disabled={isLoading}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <p className="text-sm text-muted-foreground w-full mb-2">Suggested:</p>
          {suggestedPrompts.slice(0, 4).map((prompt, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => handlePromptClick(prompt)}
              className="text-xs"
            >
              {prompt}
            </Button>
          ))}
        </CardFooter>
      </Card>

      {/* Dashboard content grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Upcoming Tasks */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-blue-500" />
              Upcoming Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {upcomingTasks.map(task => (
                <li key={task.id} className="flex justify-between items-start border-b pb-2">
                  <div>
                    <p className="font-medium">{task.title}</p>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      Due: {task.dueDate}
                    </div>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    task.priority === 'High'
                      ? 'bg-red-100 text-red-800'
                      : task.priority === 'Medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                  }`}>
                    {task.priority}
                  </span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" className="w-full">
              <Calendar className="h-4 w-4 mr-2" />
              View All Tasks
            </Button>
          </CardFooter>
        </Card>

        {/* Research Highlights */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-blue-500" />
              Research Highlights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {researchHighlights.map(area => (
                <li key={area.id} className="border-b pb-2">
                  <p className="font-medium">{area.title}</p>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground flex items-center">
                      <FileText className="h-3 w-3 mr-1" />
                      {area.papers} papers
                    </span>
                    <span className="text-muted-foreground flex items-center">
                      <Award className="h-3 w-3 mr-1" />
                      {area.citations} citations
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" className="w-full">
              <BarChart className="h-4 w-4 mr-2" />
              View Research Metrics
            </Button>
          </CardFooter>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Lightbulb className="h-5 w-5 mr-2 text-blue-500" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                <User className="h-5 w-5 mb-1" />
                <span className="text-xs">Update Profile</span>
              </Button>
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                <FileText className="h-5 w-5 mb-1" />
                <span className="text-xs">New Publication</span>
              </Button>
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                <Search className="h-5 w-5 mb-1" />
                <span className="text-xs">Find Collaborators</span>
              </Button>
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
                <Star className="h-5 w-5 mb-1" />
                <span className="text-xs">Merit Review</span>
              </Button>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" className="w-full">
              View All Actions
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Latest Activities and Publications - Placeholder cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-blue-500" />
              Latest Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {[1, 2, 3].map(id => (
                <li key={id} className="flex justify-between border-b pb-2">
                  <div>
                    <p className="font-medium">Academic Conference</p>
                    <p className="text-sm text-gray-500">University of Waterloo</p>
                    <p className="text-sm text-gray-500">Engineering Building</p>
                  </div>
                  <p className="text-sm text-gray-500">
                    {new Date().toLocaleDateString()}
                  </p>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-blue-500" />
              Latest Publications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {[1, 2, 3].map(id => (
                <li key={id} className="flex justify-between border-b pb-2">
                  <div>
                    <p className="font-medium">Machine Learning Applications</p>
                    <p className="text-sm text-gray-500">Smith, J., Johnson, A.</p>
                    <p className="text-sm text-gray-500">Journal of AI Research</p>
                  </div>
                  <p className="text-sm text-gray-500">
                    {new Date().toLocaleDateString()}
                  </p>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>
    </main>
  );
}