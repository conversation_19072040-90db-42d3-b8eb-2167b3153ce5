"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { cn } from "@/lib/utils";
import {
  Users,
  FileSpreadsheet,
  Settings,
  Database,
  ChevronDown,
  ChevronRight,
  Upload,
  Building2,
} from "lucide-react";
import { useState } from "react";

const SideNav = () => {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleItem = (name: string) => {
    setExpandedItems(prev => {
      const next = new Set(prev);
      if (next.has(name)) {
        next.delete(name);
      } else {
        next.add(name);
      }
      return next;
    });
  };

  const isSystemAdmin = session?.user?.roles?.includes("system_admin");
  const isInstitutionAdmin = session?.user?.roles?.includes("institution_admin");

  return (
    <div className="flex flex-col h-full">
      {/* ... existing code ... */}
      {(isSystemAdmin || isInstitutionAdmin) && (
        <li>
          <Link href="/dashboard/admin/units" className="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50">
            <Building2 className="h-4 w-4" />
            <span className="whitespace-nowrap">View Data</span>
          </Link>
        </li>
      )}
      {isSystemAdmin && (
        <li>
          <Link href="/dashboard/admin/import" className="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50">
            <Upload className="h-4 w-4" />
            <span className="whitespace-nowrap">Import Data</span>
          </Link>
        </li>
      )}
      {/* ... existing code ... */}
    </div>
  );
};

export default SideNav;