import { Suspense } from 'react';
import Form from '@/app/ui/sabbatical/request-form';
import SuccessMessage from '@/app/ui/sabbatical/success-message';
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export default async function Page() {

  const fac_res = await fetch(`/api/sabbaticalForm/options/faculty-dept`, {
    cache: 'no-store',
  });
  const facultyInfo = await fac_res.json();


  type RankInfo = {
    rank_desc: string; // or number, depending on DB column type
    rank_id: string; 
  };
  
  type LeaveType = {
    leave_type: string;
  };

  await sql `SET search_path TO engrecords`;
  
    const rankInfo = await sql `
    SELECT * FROM fac_ranks where rank IS NOT NULL
    `;
  
    const leaveType = await sql `
    SELECT * FROM leave_types
    `;

    // Convert raw SQL result to plain arrays
    const rankInfoFormatted: RankInfo[] = rankInfo.map((row) => ({
      rank_id: row.rank_id,
      rank_desc: row.rank_desc,
    }));

    const leaveTypeFormatted: LeaveType[] = leaveType.map((row) => ({
      leave_type: row.leave_type,
    }));

  const initialData = {

    first_name: facultyInfo[0]?.first_name || '',
    last_name: facultyInfo[0]?.last_name || '',
    name: `${facultyInfo[0]?.first_name || ''} ${facultyInfo[0]?.last_name || ''}`.trim(),
    department: facultyInfo[0]?.dept_full_name || '',
    tenure: facultyInfo[0]?.tenure_status || '',
    rankInfo: rankInfoFormatted,
    leaveType: leaveTypeFormatted,
    //leaveOutline: "<p>Fetched rich text...</p>",
  };


  return (
        <main>
          <div className='grid grid-cols-1 gap-1 text-center font-medium'>
          <h1 className='font-bold text-2xl'>REQUEST FOR LEAVE OF ABSENCE</h1>
          <p>IN ACCORDANCE WITH POLICY #3 ON LEAVES OF ABSENCE FOR FACULTY MEMBERS</p>

            <Suspense fallback={<div>Loading...</div>}>
                <SuccessMessage />
            </Suspense>


          </div>  
             {/* <img src='/_next/image?url=/assets/img/curved-images/white-curved.jpg' />*/}
              <Suspense fallback={<div>Loading...</div>}>
                <Form initialData={initialData} />
              </Suspense>
              
              {/* Use the Message component under the form */}
             
           
        </main>
  );
}