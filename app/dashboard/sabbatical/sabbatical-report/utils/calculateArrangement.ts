import { differenceInMonths } from 'date-fns';



export function calculateArrangement(pastLeaves: any[], currentFrom: string, currentTo: string, nonTeachTerm: any[], firstDate: string){
  if (!currentFrom || !currentTo || !pastLeaves.length) return '';

  const sorted = [...pastLeaves].sort((a, b) => new Date(b.to).getTime() - new Date(a.to).getTime());
  const recent = sorted[0];

  const months = differenceInMonths(new Date(recent.to), new Date(recent.from));
  let bonus = 0;
  if (months > 72) {
    const carry = Math.min(months - 72, 36);
    bonus = carry * 0.7;
  }

  return `${Math.min(100, 85 + bonus).toFixed(1)}%`;
};