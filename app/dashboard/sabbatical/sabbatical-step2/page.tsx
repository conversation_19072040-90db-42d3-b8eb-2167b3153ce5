import { Suspense } from 'react';
import Form from '@/app/ui/sabbatical/sabbatical-step2';
import SuccessMessage from '@/app/ui/sabbatical/success-message';

export default async function Page() {

  const initialData = {

  };

  return (

            <div className='grid grid-cols-1 gap-1 '>

                <div className="w-full max-w-full p-5 mr-auto md:flex-0 shrink-0 md:w-11/12 text-center font-medium">
                  <h1 className="font-bold">This sabbatical is conditional upon approval from the Board of Governors</h1>
                  <p>Copies: Provost, HR/Payroll, Office of Research, Dean, Chair/Director, GSO, Applicant</p>
                  <h1 className='font-bold p-5 md:mt-3 z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 text-xl'>PLAN FOR SABBATICAL LEAVE</h1>
                  <p>As stated in Policy 3 - Sabbatical and Other Leaves for Faculty Members, “the granting of a leave, with or without pay, depends on the University's assessment of the value of such leave to the institution as well as to the individual, and on whether teaching and other responsibilities of the applicant can be adequately provided for in her / his absence. A faculty member who is granted a sabbatical or other leave is expected to return to duties in the University for at least one year and upon return will be expected to submit a brief report to the Department Chair regarding scholarly activities while on leave.”</p>
                </div>

                    <Suspense fallback={<div>Loading...</div>}>
                            <SuccessMessage />
                    </Suspense>

                    <Suspense fallback={<div>Loading...</div>}>
                            <Form initialData={initialData} />
                    </Suspense>


            </div>

  );
}