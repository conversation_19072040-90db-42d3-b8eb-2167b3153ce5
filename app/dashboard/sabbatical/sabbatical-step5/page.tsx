import { Suspense } from 'react';
import Form from '@/app/ui/sabbatical/sabbatical-step5';
import SuccessMessage from '@/app/ui/sabbatical/success-message';
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";

export default async function Page() {
  
  const session = await getServerSession(authOptions);
      if (!session || !session.user?.id) {
        redirect("/login");
        return null;
      }

  const facultySsoId = session?.user?.facultySsoId;

  if (typeof facultySsoId !== "string") {
    throw new Error("Invalid session user id");
  }
  await sql `SET search_path TO uw`;

  const facultyInfo = await sql `
  SELECT faculty_id, intelicampus_id, sso_id, first_name, last_name, work_email, tenure_status, full_name as dept_full_name, short_name as dept_short_name, unit_type as dept_unit_type FROM faculty
  Join unit on unit.unit_id = faculty.primary_unit_id
  WHERE sso_id = ${facultySsoId.toUpperCase()}
`;

  const initialData = {
    first_name: facultyInfo[0]?.first_name || '',
    last_name: facultyInfo[0]?.last_name || '',
    applicant_name: `${facultyInfo[0]?.first_name || ''} ${facultyInfo[0]?.last_name || ''}`.trim(),
    department: facultyInfo[0]?.dept_full_name || '',

  };
   

  return (
        <div className='grid grid-cols-1 gap-1 '>
           

            <div className="w-full max-w-full p-5 mr-auto md:flex-0 shrink-0 md:w-11/12 text-center font-medium">
              <h1 className="font-bold">ADMINISTRATIVE APPOINTMENT DURING LEAVE OF ABSENCE</h1>
              <p>{ initialData.department }</p>
            </div>
          
             <Suspense fallback={<div>Loading...</div>}>
                    <SuccessMessage />
               </Suspense>
         

              <Suspense fallback={<div>Loading...</div>}>
                    <Form initialData={initialData} />
               </Suspense>

        </div>
        
  );
}