"use client";
import { Suspense } from 'react';
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import SuccessMessage from '@/app/ui/sabbatical/success-message';


import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/app/components/ui/skeleton";
import SearchBar from "@/app/dashboard/sys_admin/faculty/components/SearchBar";
import UserReportManager from "./components/UserReportManager";
import { SimpleDialog } from "./components/SimpleDialog";

interface LeaveType {
  type: string;
  from: string;
  to: string;
  salaryArrangement: string;
}

interface SabbRequest {
  user_id: string;
  work_email: string;
  display_name: string;
  created_at: string;
  leave_type?: LeaveType[];
  from_Date: string;
  to_Date: string;
  first_name:string;
  last_name:string;
  unit_head_approve:string;
  dean_approve:string;
  prov_office_approve:string;
  sr_appl_id:string;
  userReport: string;
}


interface SabbRole {
  email: string;
  facultyId: string;
  facultySsoId: string;
  id: string;
  roles: string[];
}

export default function ApprovedListPage() {
  const router = useRouter();
  const [role, setRole] = useState<SabbRole | null>(null);
  const [users, setUsers] = useState<SabbRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<SabbRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<SabbRequest | null>(null);

  const submitReport = (userId: string) => {
    window.open(`/dashboard/sabbatical/sabbatical-report?id=${encodeURIComponent(userId)}`, '_Self');
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/sabatical/approved-list");

      if (!response.ok) {
        throw new Error("Failed to fetch Approved List");
      }
      const data = await response.json();

      if (data?.role?.user) {
        setRole(data.role.user);
        console.log("Role state updated:", data.role.user);
      } else {
        console.warn("No role user in API response.");
      }

      setUsers(data.users);
      setFilteredRequests(data.users);

    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredRequests(users);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = users.filter(user =>
      (user.display_name?.toLowerCase() || '').includes(query) ||
      (user.work_email?.toLowerCase() || '').includes(query)
    );
    setFilteredRequests(filtered);
  }, [searchQuery, users]);

  useEffect(() => {
        console.log("Updated role:", role);
  }, [role]);

  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-4 w-[300px]" />
        <Skeleton className="h-4 w-[250px]" />
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (<div className="container mx-auto py-6">
    <div>
       <Suspense fallback={<div>Loading...</div>}>
                    <SuccessMessage />
        </Suspense>

    </div>
    
               
      <Card>

        <CardContent>
          <div className="mb-4 max-w-md float-left -ml-3"><CardHeader>
          <CardTitle>Approved Sabbatical Leave Requests</CardTitle>
          </CardHeader></div>
          <div className="mt-4 max-w-md float-right">
            <SearchBar
              onSearch={setSearchQuery}
              placeholder="Search by display name or email..."
            />
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                
                <TableHead>Faculty Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Leave Type</TableHead>
                <TableHead>Date From</TableHead>
                <TableHead>Date To</TableHead>
                <TableHead>Approvals Status</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRequests.map((request) => {
                // Extract all `to` values from leave_type
                const toDates = request.leave_type?.map((item) => item.to) || [];
                const fromDates = request.leave_type?.map((item) => item.from) || [];
                // Optionally get just the first toDate
                const toDate = toDates[0];
                const fromDate = fromDates[0];

                return (
                                  <TableRow key={request.user_id}>
                  <TableCell> {request.display_name}</TableCell>
                  <TableCell>{request.work_email}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                    {request.leave_type?.map((item, index) => (
                      <span key={index} >
                        {item.type}
                      </span>
                    ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {request.leave_type?.map((item, index) => {
                      const fromDate = item.from;
                      return (
                        <span key={index}>
                          {fromDate}
                        </span>
                      );
                    })}

                    </div>
                    
                  </TableCell>
                  <TableCell> <div className="flex flex-wrap gap-1">
                    
                    {request.leave_type?.map((item, index) => {
                      
                      const toDate = item.to;
                      return (
                        <span key={index}>
                          {toDate}
                        </span>
                      );
                    })}

                    
                    </div>
                  </TableCell>
                  <TableCell> <div className="flex flex-wrap gap-1">
                        {request.unit_head_approve?.trim() && request.unit_head_approve === 'Yes' ? (
                           <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                           Chair Approved
                         </span>
                        ):(<span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                          Chair Pending
                        </span>)}
                     
                     {request.dean_approve?.trim() && request.unit_head_approve === 'Yes' ? (
                           <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                           Dean's Approved
                         </span>
                        ):(<span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                          Dean's Pending
                        </span>)}

                      {request.prov_office_approve?.trim() && request.unit_head_approve === 'Yes' ? (
                           <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                           Provost Approved
                         </span>
                        ):(<span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                          Provost Pending
                        </span>)}

                    </div>
                  </TableCell>
                 
                  <TableCell>
                    {new Date(request.created_at).toLocaleDateString()}
                  </TableCell>

                  {role?.roles?.includes("regular_user") || role?.roles?.includes("public_user") ? (
                    
                    <TableCell>
                      <div className="flex flex-wrap gap-1"> 
                            { request.sr_appl_id == '' || request?.sr_appl_id == null ? (
                              <button 
                                className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm flex flex-wrap"
                                onClick={() => submitReport(request.user_id)} > 
                               
                                Submit Report
                              </button>
                            ):(<span className='text-blue-700 font-semibold'>Report Submitted Successfully.</span>)}
                      </div>

                    </TableCell>
                    
                  ) : (
                   
                    <TableCell>
                    <div className="flex flex-wrap gap-1"> 

                        { request.sr_appl_id !== '' || request?.sr_appl_id !== null ? (
                          
                          <SimpleDialog
                              isOpen={selectedUser?.user_id === request.user_id}
                              onClose={() => setSelectedUser(null)}
                              title={`View Report for ${request.display_name} Leave from ${fromDate} - to ${toDate}`}
                              trigger={
                                    <button
                                      className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm flex flex-wrap"
                                      onClick={() => setSelectedUser(request)}
                                    >
                                     View Report
                                    </button>
                                    }
                             
                          ><div className="w-[100%] max-w-none h-[80%] overflow-auto p-4">
                            <UserReportManager
                                userReport={request.sr_appl_id ?? ''} // Replace with real field
                            />
                            </div>
                          </SimpleDialog>

                      ):('Waiting Report')}

                      </div>

                    </TableCell>
                  )}
                    
                </TableRow>
                );
              })}
              
            </TableBody>
          </Table>
          <div className="mt-4 text-sm text-gray-500">
            Showing {filteredRequests.length} of {users.length} users
          </div>
        </CardContent>
      </Card>
    </div>
  );
}