import Breadcrumbs from '@/app/ui/common/breadcrumbs';
import Form from '@/app/ui/activities/create-form';
import ExtractForm from '@/app/ui/activities/extract-form';
import Message from '@/app/ui/common/message';
 
export default async function Page(props: {
  searchParams?: Promise<{
    date?: string;
    venue?: string;
    attendee?: string;
    publication?: string;
    summary?: string;
    message?: string; // for displaying success or error message
    status?: string;  // for displaying success or error message
  }>;
}) {

  const searchParams = await props.searchParams;

  const initialData = {
    date: searchParams?.date || "",
    venue: searchParams?.venue || "",
    attendee: searchParams?.attendee || "",
    publication: searchParams?.publication || "",
    summary: searchParams?.summary || "",
  };

  const message = searchParams?.message?.replace(/\+/g, ' '); // Decode spaces
  const status = searchParams?.status || '';

  return (
    <main>
      <Breadcrumbs
        breadcrumbs={[
          { label: 'Activities', href: '/dashboard/activities' },
          {
            label: 'Create Activity',
            href: '/dashboard/invoices/create',
            active: true,
          },
        ]}
      />
      <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
        <ExtractForm />
        <div>
          <Form initialData={initialData} />
          {/* Use the Message component under the form */}
          {message && <Message message={message} status={status} page='activities'/>}
        </div>
      </div>
    </main>
  );
}