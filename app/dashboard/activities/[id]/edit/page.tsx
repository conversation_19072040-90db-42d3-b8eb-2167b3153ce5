import EditActivityForm from '@/app/ui/activities/edit-form';
import Breadcrumbs from '@/app/ui/common/breadcrumbs';
import { fetchActivityById } from '@/app/lib/data';
 
export default async function Page(props: { params: Promise<{ id: string }> }) {
    const params = await props.params;
    const id = params.id;

    const activity = await fetchActivityById(id);
    console.log(activity);
  return (
    <main>
      <Breadcrumbs
        breadcrumbs={[
          { label: 'Activities', href: '/dashboard/activities' },
          {
            label: 'Edit Activity',
            href: `/dashboard/activities/${id}/edit`,
            active: true,
          },
        ]}
      />
      <EditActivityForm activity={activity}/>  
    </main>
  );
}