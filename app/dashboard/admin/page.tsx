import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { sql } from "@/app/lib/db";
import { authOptions } from "@/app/lib/auth";

export default async function AdminDashboardPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  if (!roles.includes("system_admin") && !roles.includes("institution_admin")) {
    redirect("/dashboard");
  }

  const facultyCountResult = await sql`
    SELECT COUNT(*) as total 
    FROM common.user u
    JOIN common.user_role ur ON u.user_id = ur.user_id
    JOIN common.role r ON ur.role_id = r.role_id
    WHERE r.name = 'professor' AND u.deleted_at IS NULL AND ur.deleted_at IS NULL AND r.deleted_at IS NULL
  `;
  const totalFaculties = facultyCountResult[0].total;

  return (
    <div>
      <h1>Welcome, {session.user.email} ({roles.includes("system_admin") ? "System Admin" : "Institution Admin"})</h1>
      <p>Roles: {roles.join(", ")}</p>
      <div className="mt-4">
        <h2>Dashboard Gadgets</h2>
        <div className="rounded-md bg-gray-100 p-4">
          <h3>Total Faculties in Institution</h3>
          <p className="text-2xl font-bold">{totalFaculties}</p>
        </div>
      </div>
    </div>
  );
} 