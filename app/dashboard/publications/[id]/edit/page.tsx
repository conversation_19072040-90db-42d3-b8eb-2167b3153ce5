import EditPublicationForm from '@/app/ui/publications/edit-form';
import Breadcrumbs from '@/app/ui/common/breadcrumbs';
import { fetchPublicationById } from '@/app/lib/data';
 
export default async function Page(props: { params: Promise<{ id: string }> }) {
    const params = await props.params;
    const id = params.id;

    const publication = await fetchPublicationById(id);
    console.log(publication);
  return (
    <main>
      <Breadcrumbs
        breadcrumbs={[
          { label: 'Publications', href: '/dashboard/publications' },
          {
            label: 'Edit Activity',
            href: `/dashboard/publications/${id}/edit`,
            active: true,
          },
        ]}
      />
      <EditPublicationForm publication={publication}/>  
    </main>
  );
}