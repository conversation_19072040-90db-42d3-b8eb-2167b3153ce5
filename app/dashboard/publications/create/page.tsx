import Breadcrumbs from '@/app/ui/common/breadcrumbs';
import Form from '@/app/ui/publications/create-form';
import ExtractForm from '@/app/ui/publications/extract-form';
import Message from '@/app/ui/common/message';
 
export default async function Page(props: {
  searchParams?: Promise<{
    authors?: string;
    title?: string;
    journal?: string;
    editors?: string;
    year?: string;
    pages?: string;
    message?: string; // for displaying success or error message
    status?: string;  // for displaying success or error message
  }>;
}) {

  const searchParams = await props.searchParams;

  const initialData = {
    authors: searchParams?.authors || "",
    title: searchParams?.title || "",
    journal: searchParams?.journal || "",
    editors: searchParams?.editors || "",
    year: searchParams?.year || "",
    pages: searchParams?.pages || "",
  };

  const message = searchParams?.message?.replace(/\+/g, ' '); // Decode spaces
  const status = searchParams?.status || '';

  return (
    <main>
      <Breadcrumbs
        breadcrumbs={[
          { label: 'Publications', href: '/dashboard/publications' },
          {
            label: 'Create Publication',
            href: '/dashboard/publications/create',
            active: true,
          },
        ]}
      />
      <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
        <ExtractForm />
        <div>
          <Form initialData={initialData} />
          {/* Use the Message component under the form */}
          {message && <Message message={message} status={status} page='publications' />}
        </div>
      </div>
    </main>
  );
}