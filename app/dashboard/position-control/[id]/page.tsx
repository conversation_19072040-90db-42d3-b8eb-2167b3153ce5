import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { format } from "date-fns";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default async function PositionDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  if (!roles.includes("system_admin") && !roles.includes("faculty_admin")) {
    redirect("/dashboard");
  }

  const resolvedParams = await params;
  const id = resolvedParams.id;

  // Fetch position details
  const positionResult = await sql`
    SELECT *
    FROM workday.position_details
    WHERE id = ${id}
  `;

  if (positionResult.length === 0) {
    return (
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Position Not Found</h1>
        <p>The requested position could not be found.</p>
        <Link href="/dashboard/position-control">
          <Button className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Position Control
          </Button>
        </Link>
      </div>
    );
  }

  const position = positionResult[0];

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Link href="/dashboard/position-control">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </Link>
        <h1 className="text-2xl font-bold ml-4">Position Details</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <p className="text-sm text-gray-500">Position Name</p>
              <p className="font-medium">{position.position_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Reference ID</p>
              <p className="font-medium">{position.reference_id}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Business Title</p>
              <p className="font-medium">{position.business_title}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Job Profile</p>
              <p className="font-medium">{position.job_profile}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Job Family</p>
              <p className="font-medium">{position.job_family}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Job Family Groups</p>
              <p className="font-medium">{position.job_family_groups}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Status Information</h2>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <p className="text-sm text-gray-500">Worker Type</p>
              <p className="font-medium">{position.worker_type}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Employee Type</p>
              <p className="font-medium">{position.employee_type}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Time Type</p>
              <p className="font-medium">{position.time_type}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Staffing Status</p>
              <p className="font-medium">{position.staffing_status}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Available for Hire</p>
              <p className="font-medium">{position.available_for_hire}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">FTE</p>
              <p className="font-medium">{position.fte}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Worker Information</h2>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <p className="text-sm text-gray-500">Current Worker</p>
              <p className="font-medium">{position.worker || 'Vacant'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Previous Incumbent</p>
              <p className="font-medium">{position.previous_incumbent || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Position Vacate Date</p>
              <p className="font-medium">{formatDate(position.position_vacate_date)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Contract End Date</p>
              <p className="font-medium">{formatDate(position.contract_end_date)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Manager</p>
              <p className="font-medium">{position.manager}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Organization Information</h2>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <p className="text-sm text-gray-500">Supervisory Organization</p>
              <p className="font-medium">{position.supervisory_organization}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Level 03</p>
              <p className="font-medium">{position.level_03}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Level 04</p>
              <p className="font-medium">{position.level_04}</p>
            </div>
            {position.level_05 && (
              <div>
                <p className="text-sm text-gray-500">Level 05</p>
                <p className="font-medium">{position.level_05}</p>
              </div>
            )}
            {position.level_06 && (
              <div>
                <p className="text-sm text-gray-500">Level 06</p>
                <p className="font-medium">{position.level_06}</p>
              </div>
            )}
            {position.level_07 && (
              <div>
                <p className="text-sm text-gray-500">Level 07</p>
                <p className="font-medium">{position.level_07}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
