
import PositionTable from "./components/PositionTable";
import Search from "@/app/ui/search";
import PositionFilters from "./components/PositionFilters";
import Pagination from "@/app/ui/common/pagination";
import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { Suspense } from "react";
import { InvoicesTableSkeleton } from "@/app/ui/skeletons";

export default async function PositionControlPage({
  searchParams,
}: {
  searchParams: Promise<{
    query?: string;
    page?: string;
    job_family?: string;
    level_04?: string;
    staffing_status?: string;
  }>;
}) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  if (!roles.includes("system_admin") && !roles.includes("faculty_admin")) {
    redirect("/dashboard");
  }

  const {
    query = '',
    page = '1',
    job_family = '',
    level_04 = '',
    staffing_status = ''
  } = await searchParams;

  const currentPage = Number(page);
  let totalPages = 1;

  try {
    // Build the WHERE clause for filters
    let whereClause = sql`1=1`;

    // Add search condition
    if (query) {
      whereClause = sql`${whereClause} AND (
        position_name ILIKE ${`%${query}%`} OR
        worker ILIKE ${`%${query}%`} OR
        business_title ILIKE ${`%${query}%`} OR
        job_profile ILIKE ${`%${query}%`}
      )`;
    }

    // Add job family filter
    if (job_family) {
      whereClause = sql`${whereClause} AND job_family = ${job_family}`;
    }

    // Add level 04 filter
    if (level_04) {
      whereClause = sql`${whereClause} AND level_04 = ${level_04}`;
    }

    // Add staffing status filter
    if (staffing_status) {
      whereClause = sql`${whereClause} AND staffing_status = ${staffing_status}`;
    }

    // Get total count for pagination with filters
    const countResult = await sql`
      SELECT COUNT(*) as count
      FROM workday.position_details
      WHERE ${whereClause}
    `;
    const totalCount = Number(countResult[0]?.count || 0);
    totalPages = Math.ceil(totalCount / 10);
  } catch (error) {
    console.error('Error fetching position count:', error);
    totalPages = 1;
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Position Control</h1>
      <div className="mt-4 md:mt-8 flex flex-col md:flex-row items-start gap-4 w-full">
        <div className="w-full md:w-1/4">
          <Search placeholder="Search positions..." />
        </div>
        <div className="w-full md:w-3/4">
          <PositionFilters />
        </div>
      </div>
      <div className="mt-8">
        <Suspense
          key={`${query}-${currentPage}-${job_family}-${level_04}-${staffing_status}`}
          fallback={<InvoicesTableSkeleton />}
        >
        <PositionTable
          query={query}
          currentPage={currentPage}
          jobFamily={job_family}
          level04={level_04}
          staffingStatus={staffing_status}
          />
        </Suspense>
      </div>
      <div className="mt-5 flex w-full justify-center">
        <Pagination totalPages={totalPages} />
      </div>
    </div>
  );
}
