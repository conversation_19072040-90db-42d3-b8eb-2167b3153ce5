import { notFound } from "next/navigation";
import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/lib/auth";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Edit, FileText, Clock, CheckCircle, XCircle } from "lucide-react";
import Link from "next/link";

interface PositionRequest {
  id: number;
  request_number: string;
  status: string;
  position_title: string;
  career_path: string;
  position_type: string;
  home_department_name: string;
  second_department_name?: string;
  created_by_name: string;
  created_at: string;
  is_joint_appointment: boolean;
  home_department_percentage: number;
  second_department_percentage?: number;
  new_position_type?: string;
  hiring_plan_reference?: string;
  existing_position_number?: string;
  replacement_reason?: string;
  incumbent_name?: string;
  funding_sources?: string;
  position_notes?: string;
  advertisement_body?: string;
}

interface ApprovalHistory {
  id: number;
  approval_step: string;
  approver_name: string;
  approved_at?: string;
  rejected_at?: string;
  comments?: string;
  created_at: string;
}

const STATUS_COLORS: Record<string, string> = {
  'draft': 'bg-gray-100 text-gray-800',
  'submitted': 'bg-blue-100 text-blue-800',
  'unit_head_review': 'bg-yellow-100 text-yellow-800',
  'unit_head_approved': 'bg-green-100 text-green-800',
  'donna_review': 'bg-purple-100 text-purple-800',
  'donna_approved': 'bg-green-100 text-green-800',
  'veronica_review': 'bg-purple-100 text-purple-800',
  'veronica_approved': 'bg-green-100 text-green-800',
  'dean_review': 'bg-orange-100 text-orange-800',
  'dean_approved': 'bg-green-100 text-green-800',
  'provost_review': 'bg-red-100 text-red-800',
  'provost_approved': 'bg-green-100 text-green-800',
  'completed': 'bg-green-500 text-white',
  'rejected': 'bg-red-500 text-white'
};

const STATUS_LABELS: Record<string, string> = {
  'draft': 'Draft',
  'submitted': 'Submitted',
  'unit_head_review': 'Unit Head Review',
  'unit_head_approved': 'Unit Head Approved',
  'donna_review': 'Donna Review',
  'donna_approved': 'Donna Approved',
  'veronica_review': 'Veronica Review',
  'veronica_approved': 'Veronica Approved',
  'dean_review': 'Dean Review',
  'dean_approved': 'Dean Approved',
  'provost_review': 'Provost Review',
  'provost_approved': 'Provost Approved',
  'completed': 'Completed',
  'rejected': 'Rejected'
};

export default async function PositionRequestDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  const hasAccess = roles.some(role =>
    ['system_admin', 'faculty_admin', 'department_admin', 'department_support', 'faculty_support'].includes(role)
  );

  if (!hasAccess) {
    redirect("/dashboard");
  }

  const resolvedParams = await params;

  // Mock data for now - replace with actual API call
  const mockRequest: PositionRequest = {
    id: parseInt(resolvedParams.id),
    request_number: "2024-0001",
    status: "unit_head_review",
    position_title: "Assistant Professor in Chemical Engineering",
    career_path: "tenure_stream",
    position_type: "new_position",
    home_department_name: "Chemical Engineering",
    created_by_name: "John Smith",
    created_at: "2024-01-15T10:30:00Z",
    is_joint_appointment: false,
    home_department_percentage: 100,
    new_position_type: "addition_to_operating_complement",
    hiring_plan_reference: "CHE-2024-01",
    funding_sources: "Operating funds, Work Order #12345",
    position_notes: "Priority hire for process systems engineering area",
    advertisement_body: "The Department of Chemical Engineering at the University of Waterloo invites applications for a tenure-track faculty position at the Assistant Professor level in process systems engineering..."
  };

  const mockApprovals: ApprovalHistory[] = [
    {
      id: 1,
      approval_step: "submitted",
      approver_name: "John Smith",
      approved_at: "2024-01-15T10:30:00Z",
      created_at: "2024-01-15T10:30:00Z"
    }
  ];

  if (!mockRequest) {
    notFound();
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCareerPath = (path: string) => {
    const paths: Record<string, string> = {
      'tenure_stream': 'Tenure Stream',
      'teaching_stream': 'Teaching Stream',
      'research_faculty': 'Research Faculty'
    };
    return paths[path] || path;
  };

  const canEdit = mockRequest.status === 'draft' && session.user.id === mockRequest.created_by_name;
  const canApprove = roles.includes('system_admin') || roles.includes('faculty_admin');

  return (
    <div className="container mx-auto py-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/position-control/faculty-search">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Faculty Search
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Position Request {mockRequest.request_number}</h1>
            <p className="text-gray-600">{mockRequest.position_title}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge className={STATUS_COLORS[mockRequest.status]}>
            {STATUS_LABELS[mockRequest.status]}
          </Badge>
          {canEdit && (
            <Link href={`/dashboard/position-control/faculty-search/${resolvedParams.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </Link>
          )}
          {canApprove && (
            <Link href={`/dashboard/position-control/faculty-search/${resolvedParams.id}/approve`}>
              <Button size="sm">
                <CheckCircle className="mr-2 h-4 w-4" />
                Review & Approve
              </Button>
            </Link>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Request Details */}
          <Card>
            <CardHeader>
              <CardTitle>Request Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium">Career Path</h4>
                  <p className="text-gray-600">{formatCareerPath(mockRequest.career_path)}</p>
                </div>
                <div>
                  <h4 className="font-medium">Position Type</h4>
                  <p className="text-gray-600">
                    {mockRequest.position_type === 'new_position' ? 'New Position' : 'Replacement'}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium">Home Department</h4>
                  <p className="text-gray-600">
                    {mockRequest.home_department_name} ({mockRequest.home_department_percentage}%)
                  </p>
                </div>
                <div>
                  <h4 className="font-medium">Created By</h4>
                  <p className="text-gray-600">{mockRequest.created_by_name}</p>
                </div>
              </div>

              {mockRequest.new_position_type && (
                <div>
                  <h4 className="font-medium">New Position Type</h4>
                  <p className="text-gray-600">
                    {mockRequest.new_position_type === 'addition_to_operating_complement'
                      ? 'Addition to Operating Complement'
                      : 'Not in Complement'}
                  </p>
                </div>
              )}

              {mockRequest.hiring_plan_reference && (
                <div>
                  <h4 className="font-medium">Hiring Plan Reference</h4>
                  <p className="text-gray-600">{mockRequest.hiring_plan_reference}</p>
                </div>
              )}

              {mockRequest.funding_sources && (
                <div>
                  <h4 className="font-medium">Funding Sources</h4>
                  <p className="text-gray-600 whitespace-pre-wrap">{mockRequest.funding_sources}</p>
                </div>
              )}

              {mockRequest.position_notes && (
                <div>
                  <h4 className="font-medium">Position Notes</h4>
                  <p className="text-gray-600 whitespace-pre-wrap">{mockRequest.position_notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Advertisement */}
          <Card>
            <CardHeader>
              <CardTitle>Advertisement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Position Title</h4>
                  <p className="text-lg text-gray-900">{mockRequest.position_title}</p>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Advertisement Body</h4>
                  <div className="bg-gray-50 p-4 rounded border max-h-64 overflow-y-auto">
                    <div className="text-sm text-gray-700 whitespace-pre-wrap">
                      {mockRequest.advertisement_body}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Status & Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium">Current Status</h4>
                <Badge className={STATUS_COLORS[mockRequest.status]}>
                  {STATUS_LABELS[mockRequest.status]}
                </Badge>
              </div>

              <div>
                <h4 className="font-medium">Created</h4>
                <p className="text-sm text-gray-600">{formatDate(mockRequest.created_at)}</p>
              </div>

              <Separator />

              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Forms
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Clock className="mr-2 h-4 w-4" />
                  View Timeline
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Approval History */}
          <Card>
            <CardHeader>
              <CardTitle>Approval History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mockApprovals.map((approval) => (
                  <div key={approval.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {approval.approved_at ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : approval.rejected_at ? (
                        <XCircle className="h-5 w-5 text-red-500" />
                      ) : (
                        <Clock className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {approval.approval_step.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                      <p className="text-sm text-gray-500">{approval.approver_name}</p>
                      <p className="text-xs text-gray-400">
                        {formatDate(approval.approved_at || approval.rejected_at || approval.created_at)}
                      </p>
                      {approval.comments && (
                        <p className="text-sm text-gray-600 mt-1">{approval.comments}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
