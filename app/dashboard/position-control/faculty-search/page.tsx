import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/lib/auth";
import Link from "next/link";
import PositionRequestsTable from "./components/PositionRequestsTable";
import HiringPlansTable from "./components/HiringPlansTable";
import ApprovalDashboard from "./components/ApprovalDashboard";

export default async function FacultySearchPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/login");
  }

  const roles = session.user.roles || [];
  
  // Check if user has access to position control
  const hasAccess = roles.some(role => 
    ['system_admin', 'faculty_admin', 'department_admin', 'department_support', 'faculty_support'].includes(role)
  );

  if (!hasAccess) {
    redirect("/dashboard");
  }

  // Determine which tabs to show based on user roles
  const canManageHiringPlans = roles.includes('system_admin') || roles.includes('faculty_admin');
  const canCreateRequests = true; // All users with access can create requests
  const canApprove = roles.some(role => 
    ['system_admin', 'faculty_admin', 'department_admin', 'department_support', 'faculty_support'].includes(role)
  );

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Faculty Complement Planning & Position Control</h1>
          <p className="text-gray-600 mt-1">
            Manage faculty position searches, approvals, and hiring plans
          </p>
        </div>
        {canCreateRequests && (
          <Link href="/dashboard/position-control/faculty-search/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Position Request
            </Button>
          </Link>
        )}
      </div>

      <Tabs defaultValue="requests" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="requests">Position Requests</TabsTrigger>
          {canApprove && (
            <TabsTrigger value="approvals">Pending Approvals</TabsTrigger>
          )}
          {canManageHiringPlans && (
            <TabsTrigger value="hiring-plans">Hiring Plans</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="requests" className="space-y-6">
          <div className="bg-white rounded-lg border">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Position Requests</h2>
              <p className="text-sm text-gray-600 mt-1">
                View and manage faculty position search requests
              </p>
            </div>
            <PositionRequestsTable />
          </div>
        </TabsContent>

        {canApprove && (
          <TabsContent value="approvals" className="space-y-6">
            <div className="bg-white rounded-lg border">
              <div className="p-6 border-b">
                <h2 className="text-lg font-semibold">Pending Approvals</h2>
                <p className="text-sm text-gray-600 mt-1">
                  Review and approve position requests requiring your attention
                </p>
              </div>
              <ApprovalDashboard />
            </div>
          </TabsContent>
        )}

        {canManageHiringPlans && (
          <TabsContent value="hiring-plans" className="space-y-6">
            <div className="bg-white rounded-lg border">
              <div className="p-6 border-b flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">Engineering Faculty Hiring Plans</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Manage hiring plans and position allocations
                  </p>
                </div>
                <Link href="/dashboard/position-control/faculty-search/hiring-plans/new">
                  <Button variant="outline" size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Hiring Plan
                  </Button>
                </Link>
              </div>
              <HiringPlansTable />
            </div>
          </TabsContent>
        )}
      </Tabs>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Requests</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed This Year</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
      </div>

      {/* Workflow Overview */}
      <div className="bg-white rounded-lg border mt-8">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">Approval Workflow</h2>
          <p className="text-sm text-gray-600 mt-1">
            Faculty position request approval process
          </p>
        </div>
        <div className="p-6">
          <div className="flex items-center space-x-4 overflow-x-auto">
            {[
              { step: 1, title: "Unit Submission", description: "Unit submits position request" },
              { step: 2, title: "Unit Head Review", description: "Unit head reviews and approves" },
              { step: 3, title: "Donna Review", description: "Director of Integrated Planning review" },
              { step: 4, title: "Veronica Review", description: "Director of Faculty Services review" },
              { step: 5, title: "Dean Approval", description: "Dean reviews and approves" },
              { step: 6, title: "Form Generation", description: "System generates required forms" },
              { step: 7, title: "Provost Approval", description: "Provost office review and approval" },
              { step: 8, title: "Distribution", description: "Approval package distributed" },
              { step: 9, title: "Hiring Process", description: "Unit can initiate hiring" }
            ].map((item, index) => (
              <div key={item.step} className="flex items-center">
                <div className="flex flex-col items-center min-w-0 flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                    {item.step}
                  </div>
                  <div className="mt-2 text-center">
                    <p className="text-sm font-medium text-gray-900 whitespace-nowrap">{item.title}</p>
                    <p className="text-xs text-gray-500 mt-1 max-w-24">{item.description}</p>
                  </div>
                </div>
                {index < 8 && (
                  <div className="w-8 h-px bg-gray-300 mx-2 flex-shrink-0"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
