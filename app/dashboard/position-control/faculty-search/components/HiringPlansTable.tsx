"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Edit, Trash2, Plus } from "lucide-react";

interface HiringPlan {
  id: number;
  plan_year: number;
  reference_code: string;
  department_name: string;
  position_description: string;
  career_path: string;
  priority_level: number;
  budget_allocation: number;
  status: string;
  active_requests: number;
  created_at: string;
}

const STATUS_COLORS: Record<string, string> = {
  'active': 'bg-green-100 text-green-800',
  'inactive': 'bg-gray-100 text-gray-800',
  'completed': 'bg-blue-100 text-blue-800',
  'cancelled': 'bg-red-100 text-red-800'
};

export default function HiringPlansTable() {
  const [hiringPlans, setHiringPlans] = useState<HiringPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [yearFilter, setYearFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('active');

  useEffect(() => {
    fetchHiringPlans();
  }, [yearFilter, departmentFilter, statusFilter]);

  const fetchHiringPlans = async () => {
    try {
      setLoading(true);

      // Mock data for hiring plans
      const mockPlans: HiringPlan[] = [
        {
          id: 1,
          plan_year: 2024,
          reference_code: "CHE-2024-01",
          department_name: "Chemical Engineering",
          position_description: "Assistant Professor in Chemical Engineering - Process Systems",
          career_path: "tenure_stream",
          priority_level: 1,
          budget_allocation: 150000,
          status: "active",
          active_requests: 1,
          created_at: "2024-01-01T00:00:00Z"
        },
        {
          id: 2,
          plan_year: 2024,
          reference_code: "ECE-2024-02",
          department_name: "Electrical and Computer Engineering",
          position_description: "Teaching Professor in Computer Engineering",
          career_path: "teaching_stream",
          priority_level: 2,
          budget_allocation: 120000,
          status: "active",
          active_requests: 1,
          created_at: "2024-01-01T00:00:00Z"
        },
        {
          id: 3,
          plan_year: 2024,
          reference_code: "MME-2024-03",
          department_name: "Mechanical and Mechatronics Engineering",
          position_description: "Research Faculty in Robotics and Automation",
          career_path: "research_faculty",
          priority_level: 1,
          budget_allocation: 140000,
          status: "active",
          active_requests: 0,
          created_at: "2024-01-01T00:00:00Z"
        },
        {
          id: 4,
          plan_year: 2023,
          reference_code: "CIVE-2023-01",
          department_name: "Civil and Environmental Engineering",
          position_description: "Associate Professor in Environmental Engineering",
          career_path: "tenure_stream",
          priority_level: 1,
          budget_allocation: 160000,
          status: "completed",
          active_requests: 0,
          created_at: "2023-01-01T00:00:00Z"
        }
      ];

      setHiringPlans(mockPlans);
    } catch (error) {
      console.error('Error fetching hiring plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPlans = hiringPlans.filter(plan => {
    const matchesSearch = searchQuery === '' ||
      plan.reference_code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.position_description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.department_name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesYear = yearFilter === '' || yearFilter === 'all' || plan.plan_year.toString() === yearFilter;
    const matchesDepartment = departmentFilter === '' || departmentFilter === 'all' || plan.department_name === departmentFilter;
    const matchesStatus = statusFilter === '' || statusFilter === 'all' || plan.status === statusFilter;

    return matchesSearch && matchesYear && matchesDepartment && matchesStatus;
  });

  const formatCareerPath = (path: string) => {
    const paths: Record<string, string> = {
      'tenure_stream': 'Tenure Stream',
      'teaching_stream': 'Teaching Stream',
      'research_faculty': 'Research Faculty'
    };
    return paths[path] || path;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getAvailableYears = () => {
    const years = [...new Set(hiringPlans.map(plan => plan.plan_year))];
    return years.sort((a, b) => b - a);
  };

  const getAvailableDepartments = () => {
    const departments = [...new Set(hiringPlans.map(plan => plan.department_name))];
    return departments.sort();
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{hiringPlans.filter(p => p.status === 'active').length}</div>
            <p className="text-sm text-gray-600">Active Plans</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{hiringPlans.reduce((sum, p) => sum + p.active_requests, 0)}</div>
            <p className="text-sm text-gray-600">Active Requests</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {formatCurrency(hiringPlans.filter(p => p.status === 'active').reduce((sum, p) => sum + p.budget_allocation, 0))}
            </div>
            <p className="text-sm text-gray-600">Total Budget</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{getAvailableDepartments().length}</div>
            <p className="text-sm text-gray-600">Departments</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search hiring plans..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={yearFilter} onValueChange={setYearFilter}>
          <SelectTrigger className="w-full sm:w-32">
            <SelectValue placeholder="Year" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Years</SelectItem>
            {getAvailableYears().map(year => (
              <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Department" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            {getAvailableDepartments().map(dept => (
              <SelectItem key={dept} value={dept}>{dept}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-32">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reference Code</TableHead>
              <TableHead>Year</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Position Description</TableHead>
              <TableHead>Career Path</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Budget</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Active Requests</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPlans.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                  No hiring plans found
                </TableCell>
              </TableRow>
            ) : (
              filteredPlans.map((plan) => (
                <TableRow key={plan.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{plan.reference_code}</TableCell>
                  <TableCell>{plan.plan_year}</TableCell>
                  <TableCell>{plan.department_name}</TableCell>
                  <TableCell className="max-w-xs truncate">{plan.position_description}</TableCell>
                  <TableCell>{formatCareerPath(plan.career_path)}</TableCell>
                  <TableCell>
                    <Badge variant={plan.priority_level === 1 ? "default" : "secondary"}>
                      {plan.priority_level}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatCurrency(plan.budget_allocation)}</TableCell>
                  <TableCell>
                    <Badge className={STATUS_COLORS[plan.status] || 'bg-gray-100 text-gray-800'}>
                      {plan.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {plan.active_requests}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="text-sm text-gray-500 px-4">
        Showing {filteredPlans.length} of {hiringPlans.length} hiring plans
      </div>
    </div>
  );
}
