"use client";

import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Department {
  unit_id: number;
  full_name: string;
  short_name: string;
}

interface UnitHead {
  faculty_id: number;
  first_name: string;
  last_name: string;
  work_email: string;
}

interface HiringUnitsFormProps {
  data: {
    is_joint_appointment: boolean;
    home_department_id: number | null;
    home_department_percentage: number;
    second_department_id: number | null;
    second_department_percentage: number | null;
  };
  onChange: (updates: any) => void;
  errors: Record<string, string>;
}

export default function HiringUnitsForm({ data, onChange, errors }: HiringUnitsFormProps) {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [homeUnitHead, setHomeUnitHead] = useState<UnitHead | null>(null);
  const [secondUnitHead, setSecondUnitHead] = useState<UnitHead | null>(null);
  const [loading, setLoading] = useState(true);
  const [userDepartment, setUserDepartment] = useState<number | null>(null);
  const [departmentError, setDepartmentError] = useState<string | null>(null);

  useEffect(() => {
    fetchDepartments();
    fetchUserDepartment();
  }, []);

  useEffect(() => {
    if (data.home_department_id) {
      fetchUnitHead(data.home_department_id, 'home');
    }
  }, [data.home_department_id]);

  useEffect(() => {
    if (data.second_department_id) {
      fetchUnitHead(data.second_department_id, 'second');
    }
  }, [data.second_department_id]);

  const fetchDepartments = async () => {
    try {
      setDepartmentError(null);
      // Fetch departments from the real API
      const response = await fetch('/api/units?level=4'); // Level 4 is typically departments
      if (response.ok) {
        const units = await response.json();
        // Transform to match our interface
        const departments = units.map((unit: any) => ({
          unit_id: unit.unit_id,
          full_name: unit.full_name,
          short_name: unit.full_name.split(' ').map((word: string) => word[0]).join('').toUpperCase()
        }));
        setDepartments(departments);
        if (departments.length === 0) {
          setDepartmentError('No departments found in the system.');
        }
      } else {
        console.error('Failed to fetch departments:', response.status, response.statusText);
        setDepartments([]);
        setDepartmentError(`Failed to load departments (${response.status}). Please contact support.`);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
      setDepartments([]);
      setDepartmentError('Unable to connect to the server. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserDepartment = async () => {
    try {
      // Fetch current user's faculty information to get their department
      const response = await fetch('/api/faculty/current');
      if (response.ok) {
        const faculty = await response.json();
        if (faculty && faculty.primary_unit_id) {
          setUserDepartment(faculty.primary_unit_id);
          // Auto-populate home department if not already set
          if (!data.home_department_id) {
            onChange({ home_department_id: faculty.primary_unit_id });
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user department:', error);
    }
  };

  const fetchUnitHead = async (unitId: number, type: 'home' | 'second') => {
    try {
      // Try to fetch unit head from API
      const response = await fetch(`/api/units/${unitId}/head`);
      if (response.ok) {
        const unitHead = await response.json();
        if (unitHead) {
          if (type === 'home') {
            setHomeUnitHead(unitHead);
          } else {
            setSecondUnitHead(unitHead);
          }
        }
      } else {
        console.error(`Failed to fetch unit head for unit ${unitId}:`, response.status, response.statusText);
        // Clear unit head if API fails
        if (type === 'home') {
          setHomeUnitHead(null);
        } else {
          setSecondUnitHead(null);
        }
      }
    } catch (error) {
      console.error('Error fetching unit head:', error);
      // Clear unit head if API fails
      if (type === 'home') {
        setHomeUnitHead(null);
      } else {
        setSecondUnitHead(null);
      }
    }
  };

  const handleJointAppointmentChange = (checked: boolean) => {
    onChange({
      is_joint_appointment: checked,
      second_department_id: checked ? data.second_department_id : null,
      second_department_percentage: checked ? data.second_department_percentage : null,
      home_department_percentage: checked ? data.home_department_percentage : 100,
    });
  };

  const handlePercentageChange = (field: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    const updates: any = { [field]: numValue };

    // Auto-calculate the other percentage if joint appointment
    if (data.is_joint_appointment) {
      if (field === 'home_department_percentage') {
        updates.second_department_percentage = 100 - numValue;
      } else if (field === 'second_department_percentage') {
        updates.home_department_percentage = 100 - numValue;
      }
    }

    onChange(updates);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Joint Appointment Question */}
      <div className="space-y-2">
        <Label className="text-base font-medium">Is this a joint appointment?</Label>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="joint-appointment"
            checked={data.is_joint_appointment}
            onCheckedChange={handleJointAppointmentChange}
          />
          <Label htmlFor="joint-appointment">Yes, this is a joint appointment</Label>
        </div>
        <p className="text-sm text-gray-600">
          Joint appointments involve multiple departments sharing the position
        </p>
      </div>

      {/* Home Department */}
      <div className="space-y-4 p-4 border rounded-lg">
        <h3 className="font-medium text-lg">
          {data.is_joint_appointment ? 'Home Department (Primary)' : 'Department'}
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="home-department">Department *</Label>
            <Select
              value={data.home_department_id?.toString() || "none"}
              onValueChange={(value) => onChange({ home_department_id: value === "none" ? null : parseInt(value) })}
            >
              <SelectTrigger className={errors.home_department_id ? "border-red-500" : ""}>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Select department</SelectItem>
                {departments.map((dept) => (
                  <SelectItem key={dept.unit_id} value={dept.unit_id.toString()}>
                    {dept.full_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.home_department_id && (
              <p className="text-red-500 text-sm">{errors.home_department_id}</p>
            )}
            {departmentError && (
              <p className="text-red-500 text-sm">{departmentError}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="home-percentage">Percentage of Position *</Label>
            <div className="relative">
              <Input
                id="home-percentage"
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={data.home_department_percentage}
                onChange={(e) => handlePercentageChange('home_department_percentage', e.target.value)}
                className={`pr-8 ${errors.percentage_total ? "border-red-500" : ""}`}
                disabled={!data.is_joint_appointment}
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
            </div>
          </div>
        </div>

        {/* Unit Head Display */}
        {homeUnitHead && (
          <div className="bg-gray-50 p-3 rounded">
            <Label className="text-sm font-medium">Unit Head</Label>
            <p className="text-sm text-gray-700">
              {homeUnitHead.first_name} {homeUnitHead.last_name}
            </p>
            <p className="text-sm text-gray-500">{homeUnitHead.work_email}</p>
          </div>
        )}
      </div>

      {/* Second Department (Joint Appointment) */}
      {data.is_joint_appointment && (
        <div className="space-y-4 p-4 border rounded-lg">
          <h3 className="font-medium text-lg">Second Department</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="second-department">Department *</Label>
              <Select
                value={data.second_department_id?.toString() || "none"}
                onValueChange={(value) => onChange({ second_department_id: value === "none" ? null : parseInt(value) })}
              >
                <SelectTrigger className={errors.second_department_id ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select second department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Select second department</SelectItem>
                  {departments
                    .filter(dept => dept.unit_id !== data.home_department_id)
                    .map((dept) => (
                      <SelectItem key={dept.unit_id} value={dept.unit_id.toString()}>
                        {dept.full_name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              {errors.second_department_id && (
                <p className="text-red-500 text-sm">{errors.second_department_id}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="second-percentage">Percentage of Position *</Label>
              <div className="relative">
                <Input
                  id="second-percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={data.second_department_percentage || ''}
                  onChange={(e) => handlePercentageChange('second_department_percentage', e.target.value)}
                  className={`pr-8 ${errors.second_department_percentage || errors.percentage_total ? "border-red-500" : ""}`}
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
              </div>
              {errors.second_department_percentage && (
                <p className="text-red-500 text-sm">{errors.second_department_percentage}</p>
              )}
            </div>
          </div>

          {/* Second Unit Head Display */}
          {secondUnitHead && (
            <div className="bg-gray-50 p-3 rounded">
              <Label className="text-sm font-medium">Unit Head</Label>
              <p className="text-sm text-gray-700">
                {secondUnitHead.first_name} {secondUnitHead.last_name}
              </p>
              <p className="text-sm text-gray-500">{secondUnitHead.work_email}</p>
            </div>
          )}
        </div>
      )}

      {/* Percentage Total Validation */}
      {data.is_joint_appointment && (
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Total Percentage:</span>
            <span className={`text-sm font-bold ${
              (data.home_department_percentage + (data.second_department_percentage || 0)) === 100
                ? 'text-green-600'
                : 'text-red-600'
            }`}>
              {data.home_department_percentage + (data.second_department_percentage || 0)}%
            </span>
          </div>
          {errors.percentage_total && (
            <p className="text-red-500 text-sm mt-2">{errors.percentage_total}</p>
          )}
        </div>
      )}

      {/* Help Text */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium text-sm mb-2">Guidelines:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Unit heads will be automatically notified and included in the approval workflow</li>
          <li>• For joint appointments, both departments must approve the position request</li>
          <li>• Percentage allocations determine budget and administrative responsibilities</li>
          <li>• The home department typically handles primary administrative duties</li>
        </ul>
      </div>
    </div>
  );
}
