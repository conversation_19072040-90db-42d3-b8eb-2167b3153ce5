"use client";

import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface HiringPlan {
  id: number;
  reference_code: string;
  position_description: string;
  career_path: string;
  department_name: string;
}

interface PositionDetailsFormProps {
  data: {
    career_path: 'tenure_stream' | 'teaching_stream' | 'research_faculty' | null;
    position_type: 'new_position' | 'replacement' | null;
    new_position_type: 'addition_to_operating_complement' | 'not_in_complement' | null;
    hiring_plan_reference: string;
    existing_position_number: string;
    replacement_reason: 'resignation' | 'retirement' | 'termination' | 'death' | 'other' | null;
    replacement_reason_other: string;
    incumbent_name: string;
    incumbent_employee_id: string;
    termination_date: string;
    is_bridge_position: boolean;
    bridge_position_number: string;
    bridge_end_date: string;
    funding_sources: string;
    position_notes: string;
  };
  onChange: (updates: any) => void;
  errors: Record<string, string>;
}

export default function PositionDetailsForm({ data, onChange, errors }: PositionDetailsFormProps) {
  const [hiringPlans, setHiringPlans] = useState<HiringPlan[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHiringPlans();
  }, []);

  const fetchHiringPlans = async () => {
    try {
      // TODO: Replace with actual API call to /api/position-control/hiring-plans
      // Mock hiring plans data for now
      const mockHiringPlans = [
        {
          id: 1,
          reference_code: "CHE-2024-01",
          position_description: "Assistant Professor in Chemical Engineering",
          career_path: "tenure_stream",
          department_name: "Chemical Engineering"
        },
        {
          id: 2,
          reference_code: "ECE-2024-02",
          position_description: "Teaching Professor in Computer Engineering",
          career_path: "teaching_stream",
          department_name: "Electrical and Computer Engineering"
        },
        {
          id: 3,
          reference_code: "MME-2024-03",
          position_description: "Research Faculty in Robotics",
          career_path: "research_faculty",
          department_name: "Mechanical and Mechatronics Engineering"
        }
      ];
      setHiringPlans(mockHiringPlans);
    } catch (error) {
      console.error('Error fetching hiring plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCareerPathChange = (value: string) => {
    onChange({ career_path: value as any });
  };

  const handlePositionTypeChange = (value: string) => {
    onChange({
      position_type: value as any,
      // Reset related fields when position type changes
      new_position_type: null,
      hiring_plan_reference: '',
      existing_position_number: '',
      replacement_reason: null,
      replacement_reason_other: '',
      incumbent_name: '',
      incumbent_employee_id: '',
      termination_date: '',
      is_bridge_position: false,
      bridge_position_number: '',
      bridge_end_date: ''
    });
  };

  return (
    <div className="space-y-6">
      {/* Career Path */}
      <div className="space-y-3">
        <Label className="text-base font-medium">What is the career path? *</Label>
        <RadioGroup
          value={data.career_path || ""}
          onValueChange={handleCareerPathChange}
          className="grid grid-cols-1 gap-3"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="tenure_stream" id="tenure_stream" />
            <Label htmlFor="tenure_stream" className="font-normal">
              Tenure Stream
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="teaching_stream" id="teaching_stream" />
            <Label htmlFor="teaching_stream" className="font-normal">
              Teaching Stream
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="research_faculty" id="research_faculty" />
            <Label htmlFor="research_faculty" className="font-normal">
              Research Faculty
            </Label>
          </div>
        </RadioGroup>
        {errors.career_path && (
          <p className="text-red-500 text-sm">{errors.career_path}</p>
        )}
      </div>

      {/* Position Type */}
      <div className="space-y-3">
        <Label className="text-base font-medium">Is this a new position or replacement? *</Label>
        <RadioGroup
          value={data.position_type || ""}
          onValueChange={handlePositionTypeChange}
          className="grid grid-cols-1 gap-3"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="new_position" id="new_position" />
            <Label htmlFor="new_position" className="font-normal">
              New Position
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="replacement" id="replacement" />
            <Label htmlFor="replacement" className="font-normal">
              Replacement for Permanent Loss
            </Label>
          </div>
        </RadioGroup>
        {errors.position_type && (
          <p className="text-red-500 text-sm">{errors.position_type}</p>
        )}
      </div>

      {/* New Position Details */}
      {data.position_type === 'new_position' && (
        <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
          <h3 className="font-medium text-lg">New Position Details</h3>

          <div className="space-y-3">
            <Label className="text-base font-medium">Position Type *</Label>
            <RadioGroup
              value={data.new_position_type || ""}
              onValueChange={(value) => onChange({ new_position_type: value as any })}
              className="grid grid-cols-1 gap-3"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="addition_to_operating_complement" id="addition_complement" />
                <Label htmlFor="addition_complement" className="font-normal">
                  Addition to Operating Complement
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="not_in_complement" id="not_complement" />
                <Label htmlFor="not_complement" className="font-normal">
                  Not in Complement
                </Label>
              </div>
            </RadioGroup>
            {errors.new_position_type && (
              <p className="text-red-500 text-sm">{errors.new_position_type}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="hiring-plan">Hiring Plan Reference *</Label>
            <Select
              value={data.hiring_plan_reference || "none"}
              onValueChange={(value) => onChange({ hiring_plan_reference: value === "none" ? "" : value })}
            >
              <SelectTrigger className={errors.hiring_plan_reference ? "border-red-500" : ""}>
                <SelectValue placeholder="Select hiring plan reference" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Select hiring plan reference</SelectItem>
                {hiringPlans.map((plan) => (
                  <SelectItem key={plan.id} value={plan.reference_code}>
                    {plan.reference_code} - {plan.position_description}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.hiring_plan_reference && (
              <p className="text-red-500 text-sm">{errors.hiring_plan_reference}</p>
            )}
          </div>
        </div>
      )}

      {/* Replacement Position Details */}
      {data.position_type === 'replacement' && (
        <div className="space-y-4 p-4 border rounded-lg bg-yellow-50">
          <h3 className="font-medium text-lg">Replacement Position Details</h3>

          <div className="space-y-2">
            <Label htmlFor="position-number">Position Number *</Label>
            <Input
              id="position-number"
              value={data.existing_position_number}
              onChange={(e) => onChange({ existing_position_number: e.target.value })}
              placeholder="Enter position number"
              className={errors.existing_position_number ? "border-red-500" : ""}
            />
            {errors.existing_position_number && (
              <p className="text-red-500 text-sm">{errors.existing_position_number}</p>
            )}
          </div>

          <div className="space-y-3">
            <Label className="text-base font-medium">Reason for Replacement *</Label>
            <RadioGroup
              value={data.replacement_reason || ""}
              onValueChange={(value) => onChange({ replacement_reason: value as any })}
              className="grid grid-cols-2 gap-3"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="resignation" id="resignation" />
                <Label htmlFor="resignation" className="font-normal">Resignation</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="retirement" id="retirement" />
                <Label htmlFor="retirement" className="font-normal">Retirement</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="termination" id="termination" />
                <Label htmlFor="termination" className="font-normal">Termination</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="death" id="death" />
                <Label htmlFor="death" className="font-normal">Death</Label>
              </div>
              <div className="flex items-center space-x-2 col-span-2">
                <RadioGroupItem value="other" id="other" />
                <Label htmlFor="other" className="font-normal">Other (explain)</Label>
              </div>
            </RadioGroup>
            {errors.replacement_reason && (
              <p className="text-red-500 text-sm">{errors.replacement_reason}</p>
            )}
          </div>

          {data.replacement_reason === 'other' && (
            <div className="space-y-2">
              <Label htmlFor="other-reason">Please explain *</Label>
              <Textarea
                id="other-reason"
                value={data.replacement_reason_other}
                onChange={(e) => onChange({ replacement_reason_other: e.target.value })}
                placeholder="Explain the reason for replacement"
                className={errors.replacement_reason_other ? "border-red-500" : ""}
              />
              {errors.replacement_reason_other && (
                <p className="text-red-500 text-sm">{errors.replacement_reason_other}</p>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="incumbent-name">Faculty Member Name</Label>
              <Input
                id="incumbent-name"
                value={data.incumbent_name}
                onChange={(e) => onChange({ incumbent_name: e.target.value })}
                placeholder="Last Name, First Name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="employee-id">Employee ID Number</Label>
              <Input
                id="employee-id"
                value={data.incumbent_employee_id}
                onChange={(e) => onChange({ incumbent_employee_id: e.target.value })}
                placeholder="Employee ID"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="termination-date">Termination/Retirement Date</Label>
              <Input
                id="termination-date"
                type="date"
                value={data.termination_date}
                onChange={(e) => onChange({ termination_date: e.target.value })}
              />
            </div>
          </div>

          {/* Bridge Position */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="bridge-position"
                checked={data.is_bridge_position}
                onCheckedChange={(checked) => onChange({ is_bridge_position: checked })}
              />
              <Label htmlFor="bridge-position">Is this a bridge position?</Label>
            </div>

            {data.is_bridge_position && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-6">
                <div className="space-y-2">
                  <Label htmlFor="bridge-position-number">Bridge Position Number</Label>
                  <Input
                    id="bridge-position-number"
                    value={data.bridge_position_number}
                    onChange={(e) => onChange({ bridge_position_number: e.target.value })}
                    placeholder="Bridge position number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bridge-end-date">Anticipated End Date</Label>
                  <Input
                    id="bridge-end-date"
                    type="date"
                    value={data.bridge_end_date}
                    onChange={(e) => onChange({ bridge_end_date: e.target.value })}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Common Fields */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="funding-sources">Funding Sources</Label>
          <Textarea
            id="funding-sources"
            value={data.funding_sources}
            onChange={(e) => onChange({ funding_sources: e.target.value })}
            placeholder="Include Work Order #(s) and funding details"
            rows={3}
          />
          <p className="text-sm text-gray-600">
            Include Work Order numbers and any specific funding information
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="position-notes">Notes About Position</Label>
          <Textarea
            id="position-notes"
            value={data.position_notes}
            onChange={(e) => onChange({ position_notes: e.target.value })}
            placeholder="Additional notes or special requirements"
            rows={3}
          />
        </div>
      </div>
    </div>
  );
}
