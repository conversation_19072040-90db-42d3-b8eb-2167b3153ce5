"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";

interface PendingApproval {
  id: number;
  request_number: string;
  position_title: string;
  department_name: string;
  created_by_name: string;
  status: string;
  days_pending: number;
  priority: 'high' | 'medium' | 'low';
  can_approve: boolean;
}

const STATUS_INFO: Record<string, { label: string; description: string; color: string }> = {
  'unit_head_review': {
    label: 'Unit Head Review',
    description: 'Waiting for unit head approval',
    color: 'bg-yellow-100 text-yellow-800'
  },
  'donna_review': {
    label: 'Donna Review',
    description: 'Director of Integrated Planning review',
    color: 'bg-purple-100 text-purple-800'
  },
  'veronica_review': {
    label: 'Veronica Review',
    description: 'Director of Faculty Services review',
    color: 'bg-purple-100 text-purple-800'
  },
  'dean_review': {
    label: 'Dean Review',
    description: 'Dean approval required',
    color: 'bg-orange-100 text-orange-800'
  },
  'provost_review': {
    label: 'Provost Review',
    description: 'Provost office review',
    color: 'bg-red-100 text-red-800'
  }
};

export default function ApprovalDashboard() {
  const router = useRouter();
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total_pending: 0,
    high_priority: 0,
    overdue: 0,
    my_queue: 0
  });

  useEffect(() => {
    fetchPendingApprovals();
  }, []);

  const fetchPendingApprovals = async () => {
    try {
      setLoading(true);
      
      // Mock data for pending approvals
      const mockApprovals: PendingApproval[] = [
        {
          id: 1,
          request_number: "2024-0001",
          position_title: "Assistant Professor in Chemical Engineering",
          department_name: "Chemical Engineering",
          created_by_name: "John Smith",
          status: "unit_head_review",
          days_pending: 3,
          priority: "medium",
          can_approve: true
        },
        {
          id: 2,
          request_number: "2024-0002",
          position_title: "Teaching Professor in Computer Engineering",
          department_name: "Electrical and Computer Engineering",
          created_by_name: "Jane Doe",
          status: "donna_review",
          days_pending: 7,
          priority: "high",
          can_approve: false
        },
        {
          id: 4,
          request_number: "2024-0004",
          position_title: "Research Faculty in Robotics",
          department_name: "Mechanical and Mechatronics Engineering",
          created_by_name: "Alice Brown",
          status: "veronica_review",
          days_pending: 12,
          priority: "high",
          can_approve: false
        },
        {
          id: 5,
          request_number: "2024-0005",
          position_title: "Associate Professor in Civil Engineering",
          department_name: "Civil and Environmental Engineering",
          created_by_name: "Charlie Wilson",
          status: "dean_review",
          days_pending: 5,
          priority: "medium",
          can_approve: false
        }
      ];

      setPendingApprovals(mockApprovals);

      // Calculate stats
      const total = mockApprovals.length;
      const highPriority = mockApprovals.filter(a => a.priority === 'high').length;
      const overdue = mockApprovals.filter(a => a.days_pending > 10).length;
      const myQueue = mockApprovals.filter(a => a.can_approve).length;

      setStats({
        total_pending: total,
        high_priority: highPriority,
        overdue: overdue,
        my_queue: myQueue
      });

    } catch (error) {
      console.error('Error fetching pending approvals:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityIcon = (priority: string, daysPending: number) => {
    if (daysPending > 10) {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    if (priority === 'high') {
      return <Clock className="h-4 w-4 text-orange-500" />;
    }
    return <Clock className="h-4 w-4 text-gray-400" />;
  };

  const getPriorityBadge = (priority: string, daysPending: number) => {
    if (daysPending > 10) {
      return <Badge className="bg-red-500 text-white">Overdue</Badge>;
    }
    if (priority === 'high') {
      return <Badge className="bg-orange-500 text-white">High Priority</Badge>;
    }
    if (priority === 'medium') {
      return <Badge variant="outline">Medium</Badge>;
    }
    return <Badge variant="secondary">Low</Badge>;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Pending</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertCircle className="w-6 h-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">High Priority</p>
                <p className="text-2xl font-bold text-gray-900">{stats.high_priority}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-gray-900">{stats.overdue}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">My Queue</p>
                <p className="text-2xl font-bold text-gray-900">{stats.my_queue}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Pending Approvals Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Priority</TableHead>
              <TableHead>Request #</TableHead>
              <TableHead>Position Title</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Days Pending</TableHead>
              <TableHead>Submitted By</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pendingApprovals.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                  No pending approvals
                </TableCell>
              </TableRow>
            ) : (
              pendingApprovals
                .sort((a, b) => {
                  // Sort by overdue first, then high priority, then days pending
                  if (a.days_pending > 10 && b.days_pending <= 10) return -1;
                  if (b.days_pending > 10 && a.days_pending <= 10) return 1;
                  if (a.priority === 'high' && b.priority !== 'high') return -1;
                  if (b.priority === 'high' && a.priority !== 'high') return 1;
                  return b.days_pending - a.days_pending;
                })
                .map((approval) => (
                  <TableRow 
                    key={approval.id}
                    className={`cursor-pointer hover:bg-gray-50 ${
                      approval.can_approve ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => router.push(`/dashboard/position-control/faculty-search/${approval.id}`)}
                  >
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getPriorityIcon(approval.priority, approval.days_pending)}
                        {getPriorityBadge(approval.priority, approval.days_pending)}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{approval.request_number}</TableCell>
                    <TableCell className="max-w-xs truncate">{approval.position_title}</TableCell>
                    <TableCell>{approval.department_name}</TableCell>
                    <TableCell>
                      <Badge className={STATUS_INFO[approval.status]?.color || 'bg-gray-100 text-gray-800'}>
                        {STATUS_INFO[approval.status]?.label || approval.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={approval.days_pending > 10 ? 'text-red-600 font-medium' : ''}>
                        {approval.days_pending} days
                      </span>
                    </TableCell>
                    <TableCell>{approval.created_by_name}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/dashboard/position-control/faculty-search/${approval.id}`);
                          }}
                        >
                          Review
                        </Button>
                        {approval.can_approve && (
                          <Button
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/dashboard/position-control/faculty-search/${approval.id}/approve`);
                            }}
                          >
                            Approve
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common approval workflow actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start">
              <CheckCircle className="mr-2 h-4 w-4" />
              Bulk Approve Selected
            </Button>
            <Button variant="outline" className="justify-start">
              <Clock className="mr-2 h-4 w-4" />
              View Overdue Items
            </Button>
            <Button variant="outline" className="justify-start">
              <AlertCircle className="mr-2 h-4 w-4" />
              Generate Reports
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
