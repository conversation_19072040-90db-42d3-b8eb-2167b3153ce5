"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Eye, Search, Filter } from "lucide-react";

interface PositionRequest {
  id: number;
  request_number: string;
  status: string;
  position_title: string;
  career_path: string;
  position_type: string;
  home_department_name: string;
  second_department_name?: string;
  created_by_name: string;
  created_at: string;
  is_joint_appointment: boolean;
}

const STATUS_COLORS: Record<string, string> = {
  'draft': 'bg-gray-100 text-gray-800',
  'submitted': 'bg-blue-100 text-blue-800',
  'unit_head_review': 'bg-yellow-100 text-yellow-800',
  'unit_head_approved': 'bg-green-100 text-green-800',
  'donna_review': 'bg-purple-100 text-purple-800',
  'donna_approved': 'bg-green-100 text-green-800',
  'veronica_review': 'bg-purple-100 text-purple-800',
  'veronica_approved': 'bg-green-100 text-green-800',
  'dean_review': 'bg-orange-100 text-orange-800',
  'dean_approved': 'bg-green-100 text-green-800',
  'provost_review': 'bg-red-100 text-red-800',
  'provost_approved': 'bg-green-100 text-green-800',
  'completed': 'bg-green-500 text-white',
  'rejected': 'bg-red-500 text-white'
};

const STATUS_LABELS: Record<string, string> = {
  'draft': 'Draft',
  'submitted': 'Submitted',
  'unit_head_review': 'Unit Head Review',
  'unit_head_approved': 'Unit Head Approved',
  'donna_review': 'Donna Review',
  'donna_approved': 'Donna Approved',
  'veronica_review': 'Veronica Review',
  'veronica_approved': 'Veronica Approved',
  'dean_review': 'Dean Review',
  'dean_approved': 'Dean Approved',
  'provost_review': 'Provost Review',
  'provost_approved': 'Provost Approved',
  'completed': 'Completed',
  'rejected': 'Rejected'
};

export default function PositionRequestsTable() {
  const router = useRouter();
  const [requests, setRequests] = useState<PositionRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');

  useEffect(() => {
    fetchRequests();
  }, [statusFilter, departmentFilter]);

  const fetchRequests = async () => {
    try {
      setLoading(true);

      // For now, use mock data since the API might not be fully connected
      const mockRequests: PositionRequest[] = [
        {
          id: 1,
          request_number: "2024-0001",
          status: "unit_head_review",
          position_title: "Assistant Professor in Chemical Engineering",
          career_path: "tenure_stream",
          position_type: "new_position",
          home_department_name: "Chemical Engineering",
          created_by_name: "John Smith",
          created_at: "2024-01-15T10:30:00Z",
          is_joint_appointment: false
        },
        {
          id: 2,
          request_number: "2024-0002",
          status: "donna_review",
          position_title: "Teaching Professor in Computer Engineering",
          career_path: "teaching_stream",
          position_type: "replacement",
          home_department_name: "Electrical and Computer Engineering",
          second_department_name: "Systems Design Engineering",
          created_by_name: "Jane Doe",
          created_at: "2024-01-20T14:15:00Z",
          is_joint_appointment: true
        },
        {
          id: 3,
          request_number: "2024-0003",
          status: "completed",
          position_title: "Associate Professor in Mechanical Engineering",
          career_path: "tenure_stream",
          position_type: "new_position",
          home_department_name: "Mechanical and Mechatronics Engineering",
          created_by_name: "Bob Johnson",
          created_at: "2024-01-10T09:00:00Z",
          is_joint_appointment: false
        }
      ];

      setRequests(mockRequests);
    } catch (error) {
      console.error('Error fetching requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredRequests = requests.filter(request => {
    const matchesSearch = searchQuery === '' ||
      request.request_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.position_title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.home_department_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.created_by_name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === '' || statusFilter === 'all' || request.status === statusFilter;

    const matchesDepartment = departmentFilter === '' || departmentFilter === 'all' ||
      request.home_department_name === departmentFilter ||
      request.second_department_name === departmentFilter;

    return matchesSearch && matchesStatus && matchesDepartment;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCareerPath = (path: string) => {
    const paths: Record<string, string> = {
      'tenure_stream': 'Tenure Stream',
      'teaching_stream': 'Teaching Stream',
      'research_faculty': 'Research Faculty'
    };
    return paths[path] || path;
  };

  const formatPositionType = (type: string) => {
    const types: Record<string, string> = {
      'new_position': 'New',
      'replacement': 'Replacement'
    };
    return types[type] || type;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search requests..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            {Object.entries(STATUS_LABELS).map(([value, label]) => (
              <SelectItem key={value} value={value}>{label}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by department" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            <SelectItem value="Chemical Engineering">Chemical Engineering</SelectItem>
            <SelectItem value="Civil and Environmental Engineering">Civil and Environmental Engineering</SelectItem>
            <SelectItem value="Electrical and Computer Engineering">Electrical and Computer Engineering</SelectItem>
            <SelectItem value="Management Sciences">Management Sciences</SelectItem>
            <SelectItem value="Mechanical and Mechatronics Engineering">Mechanical and Mechatronics Engineering</SelectItem>
            <SelectItem value="Systems Design Engineering">Systems Design Engineering</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Request #</TableHead>
              <TableHead>Position Title</TableHead>
              <TableHead>Department(s)</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Career Path</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created By</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRequests.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                  No position requests found
                </TableCell>
              </TableRow>
            ) : (
              filteredRequests.map((request) => (
                <TableRow
                  key={request.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => router.push(`/dashboard/position-control/faculty-search/${request.id}`)}
                >
                  <TableCell className="font-medium">{request.request_number}</TableCell>
                  <TableCell className="max-w-xs truncate">{request.position_title}</TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div>{request.home_department_name}</div>
                      {request.is_joint_appointment && request.second_department_name && (
                        <div className="text-sm text-gray-500">+ {request.second_department_name}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {formatPositionType(request.position_type)}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatCareerPath(request.career_path)}</TableCell>
                  <TableCell>
                    <Badge className={STATUS_COLORS[request.status] || 'bg-gray-100 text-gray-800'}>
                      {STATUS_LABELS[request.status] || request.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{request.created_by_name}</TableCell>
                  <TableCell>{formatDate(request.created_at)}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/dashboard/position-control/faculty-search/${request.id}`);
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="text-sm text-gray-500 px-4">
        Showing {filteredRequests.length} of {requests.length} requests
      </div>
    </div>
  );
}
