"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Check } from "lucide-react";
import Link from "next/link";
import HiringUnitsForm from "../components/HiringUnitsForm";
import PositionDetailsForm from "../components/PositionDetailsForm";
import AdvertisementForm from "../components/AdvertisementForm";
import ReviewSubmitForm from "../components/ReviewSubmitForm";

interface FormData {
  // Hiring Units
  is_joint_appointment: boolean;
  home_department_id: number | null;
  home_department_percentage: number;
  second_department_id: number | null;
  second_department_percentage: number | null;
  
  // Position Details
  career_path: 'tenure_stream' | 'teaching_stream' | 'research_faculty' | null;
  position_type: 'new_position' | 'replacement' | null;
  new_position_type: 'addition_to_operating_complement' | 'not_in_complement' | null;
  hiring_plan_reference: string;
  existing_position_number: string;
  replacement_reason: 'resignation' | 'retirement' | 'termination' | 'death' | 'other' | null;
  replacement_reason_other: string;
  incumbent_name: string;
  incumbent_employee_id: string;
  termination_date: string;
  is_bridge_position: boolean;
  bridge_position_number: string;
  bridge_end_date: string;
  funding_sources: string;
  position_notes: string;
  
  // Advertisement
  position_title: string;
  advertisement_body: string;
}

const INITIAL_FORM_DATA: FormData = {
  is_joint_appointment: false,
  home_department_id: null,
  home_department_percentage: 100,
  second_department_id: null,
  second_department_percentage: null,
  career_path: null,
  position_type: null,
  new_position_type: null,
  hiring_plan_reference: '',
  existing_position_number: '',
  replacement_reason: null,
  replacement_reason_other: '',
  incumbent_name: '',
  incumbent_employee_id: '',
  termination_date: '',
  is_bridge_position: false,
  bridge_position_number: '',
  bridge_end_date: '',
  funding_sources: '',
  position_notes: '',
  position_title: '',
  advertisement_body: '',
};

const STEPS = [
  {
    id: 1,
    title: "Hiring Unit(s)",
    description: "Specify the department(s) and appointment details"
  },
  {
    id: 2,
    title: "Position Details",
    description: "Define the position type, career path, and requirements"
  },
  {
    id: 3,
    title: "Advertisement",
    description: "Create the position title and advertisement text"
  },
  {
    id: 4,
    title: "Review & Submit",
    description: "Review all information and submit the request"
  }
];

export default function NewPositionRequestPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>(INITIAL_FORM_DATA);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateFormData = (updates: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors when data is updated
    const updatedFields = Object.keys(updates);
    setErrors(prev => {
      const newErrors = { ...prev };
      updatedFields.forEach(field => {
        delete newErrors[field];
      });
      return newErrors;
    });
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.home_department_id) {
          newErrors.home_department_id = "Home department is required";
        }
        if (formData.is_joint_appointment) {
          if (!formData.second_department_id) {
            newErrors.second_department_id = "Second department is required for joint appointments";
          }
          if (!formData.second_department_percentage) {
            newErrors.second_department_percentage = "Second department percentage is required";
          }
          if (formData.home_department_percentage + (formData.second_department_percentage || 0) !== 100) {
            newErrors.percentage_total = "Department percentages must total 100%";
          }
        }
        break;

      case 2:
        if (!formData.career_path) {
          newErrors.career_path = "Career path is required";
        }
        if (!formData.position_type) {
          newErrors.position_type = "Position type is required";
        }
        if (formData.position_type === 'new_position') {
          if (!formData.new_position_type) {
            newErrors.new_position_type = "New position type is required";
          }
          if (!formData.hiring_plan_reference) {
            newErrors.hiring_plan_reference = "Hiring plan reference is required";
          }
        }
        if (formData.position_type === 'replacement') {
          if (!formData.existing_position_number) {
            newErrors.existing_position_number = "Position number is required";
          }
          if (!formData.replacement_reason) {
            newErrors.replacement_reason = "Replacement reason is required";
          }
          if (formData.replacement_reason === 'other' && !formData.replacement_reason_other) {
            newErrors.replacement_reason_other = "Please explain the other reason";
          }
        }
        break;

      case 3:
        if (!formData.position_title.trim()) {
          newErrors.position_title = "Position title is required";
        }
        if (!formData.advertisement_body.trim()) {
          newErrors.advertisement_body = "Advertisement body is required";
        }
        // Count words for validation (same logic as in AdvertisementForm component)
        const words = formData.advertisement_body.trim().split(/\s+/).filter(word => word.length > 0);
        if (words.length > 1000) {
          newErrors.advertisement_body = "Advertisement body must be 1000 words or less";
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/position-control/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        router.push(`/dashboard/position-control/faculty-search/${result.id}`);
      } else {
        const error = await response.json();
        setErrors({ submit: error.error || 'Failed to submit request' });
      }
    } catch (error) {
      setErrors({ submit: 'An error occurred while submitting the request' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <HiringUnitsForm
            data={formData}
            onChange={updateFormData}
            errors={errors}
          />
        );
      case 2:
        return (
          <PositionDetailsForm
            data={formData}
            onChange={updateFormData}
            errors={errors}
          />
        );
      case 3:
        return (
          <AdvertisementForm
            data={formData}
            onChange={updateFormData}
            errors={errors}
          />
        );
      case 4:
        return (
          <ReviewSubmitForm
            data={formData}
            errors={errors}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex items-center mb-6">
        <Link href="/dashboard/position-control/faculty-search">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Position Control
          </Button>
        </Link>
      </div>

      <div className="mb-8">
        <h1 className="text-2xl font-bold">New Faculty Position Request</h1>
        <p className="text-gray-600 mt-1">
          Submit a request to start a faculty search process
        </p>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep > step.id
                      ? 'bg-green-500 text-white'
                      : currentStep === step.id
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {currentStep > step.id ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    step.id
                  )}
                </div>
                <div className="mt-2 text-center">
                  <p className="text-sm font-medium">{step.title}</p>
                  <p className="text-xs text-gray-500 max-w-32">{step.description}</p>
                </div>
              </div>
              {index < STEPS.length - 1 && (
                <div className="w-16 h-px bg-gray-300 mx-4"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Form Content */}
      <Card>
        <CardHeader>
          <CardTitle>{STEPS[currentStep - 1].title}</CardTitle>
          <CardDescription>{STEPS[currentStep - 1].description}</CardDescription>
        </CardHeader>
        <CardContent>
          {renderStepContent()}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>

            {currentStep < STEPS.length ? (
              <Button onClick={handleNext}>
                Next
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            )}
          </div>

          {errors.submit && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{errors.submit}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
