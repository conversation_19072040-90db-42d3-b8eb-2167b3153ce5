"use client";

import React, { useEffect, useState } from "react";
import { useSearchParams, usePathname, useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";

interface FilterOptions {
  jobFamilies: string[];
  level04Units: string[];
  staffingStatuses: string[];
}

const PositionFilters = React.memo(function PositionFilters() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    jobFamilies: [],
    level04Units: [],
    staffingStatuses: [],
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get current filter values from URL
  const currentJobFamily = searchParams.get('job_family') || '';
  const currentLevel04 = searchParams.get('level_04') || '';
  const currentStaffingStatus = searchParams.get('staffing_status') || '';

  // Fetch filter options
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const response = await fetch('/api/position-control/filters');
        if (!response.ok) {
          throw new Error('Failed to fetch filter options');
        }
        const data = await response.json();
        setFilterOptions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchFilterOptions();
  }, []);

  // Handle filter changes by updating URL parameters
  const handleFilterChange = (filterType: string, value: string) => {
    const params = new URLSearchParams(searchParams);

    if (value === "all" || !value) {
      params.delete(filterType);
    } else {
      params.set(filterType, value);
    }

    // Reset to page 1 when filters change
    params.set('page', '1');

    replace(`${pathname}?${params.toString()}`);
  };

  const handleJobFamilyChange = (value: string) => {
    handleFilterChange('job_family', value);
  };

  const handleLevel04Change = (value: string) => {
    handleFilterChange('level_04', value);
  };

  const handleStaffingStatusChange = (value: string) => {
    handleFilterChange('staffing_status', value);
  };

  const resetFilters = () => {
    const params = new URLSearchParams(searchParams);
    params.delete('job_family');
    params.delete('level_04');
    params.delete('staffing_status');
    params.delete('query'); // Also clear the search query
    params.set('page', '1');
    replace(`${pathname}?${params.toString()}`);
  };

  if (loading) return <div>Loading filters...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="flex flex-col md:flex-row gap-4">
      <div className="w-full md:w-1/3">
        <Select
          value={currentJobFamily || "all"}
          onValueChange={handleJobFamilyChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Job Family" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Job Families</SelectItem>
            {filterOptions.jobFamilies.map((jobFamily) => (
              <SelectItem key={jobFamily} value={jobFamily}>
                {jobFamily}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="w-full md:w-1/3">
        <Select
          value={currentLevel04 || "all"}
          onValueChange={handleLevel04Change}
        >
          <SelectTrigger>
            <SelectValue placeholder="Organization Unit" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Units</SelectItem>
            {filterOptions.level04Units.map((unit) => (
              <SelectItem key={unit} value={unit}>
                {unit}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="w-full md:w-1/4">
        <Select
          value={currentStaffingStatus || "all"}
          onValueChange={handleStaffingStatusChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Staffing Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            {filterOptions.staffingStatuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="w-full md:w-auto">
        <Button
          variant="outline"
          onClick={resetFilters}
          className="w-full md:w-auto px-4"
        >
          <X className="mr-2 h-4 w-4" />
          Reset
        </Button>
      </div>
    </div>
  );
});

export default PositionFilters;
