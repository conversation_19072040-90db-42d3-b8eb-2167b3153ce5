import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import Link from "next/link";
import { sql } from "@/app/lib/db";
import { EyeIcon } from "@heroicons/react/24/outline";

interface Position {
  id: number;
  position_name: string;
  reference_id: number;
  worker_type: string;
  employee_type: string;
  time_type: string;
  staffing_status: string;
  available_for_hire: string;
  worker: string;
  contract_end_date: string | null;
  business_title: string;
  job_profile: string;
  frozen: string;
  freeze_date: string | null;
  freeze_reason: string | null;
  previous_incumbent: string | null;
  position_vacate_date: string | null;
  fte: number;
  job_family: string;
  job_family_groups: string;
  cost_center: string;
  class_indicator: string;
  manager: string;
  supervisory_organization: string;
  level_03: string;
  level_04: string;
  level_05: string | null;
  level_06: string | null;
  level_07: string | null;
  import_date: string;
}

export default async function PositionTable({
  query,
  currentPage,
  jobFamily = '',
  level04 = '',
  staffingStatus = '',
}: {
  query: string;
  currentPage: number;
  jobFamily?: string;
  level04?: string;
  staffingStatus?: string;
}) {
  const ITEMS_PER_PAGE = 10;
  const offset = (currentPage - 1) * ITEMS_PER_PAGE;
  let positions: Position[] = [];

  try {
    // Build the query with filters
    let whereClause = sql`1=1`;

    // Add search condition
    if (query) {
      whereClause = sql`${whereClause} AND (
        position_name ILIKE ${`%${query}%`} OR
        worker ILIKE ${`%${query}%`} OR
        business_title ILIKE ${`%${query}%`} OR
        job_profile ILIKE ${`%${query}%`}
      )`;
    }

    // Add job family filter
    if (jobFamily) {
      whereClause = sql`${whereClause} AND job_family = ${jobFamily}`;
    }

    // Add level 04 filter
    if (level04) {
      whereClause = sql`${whereClause} AND level_04 = ${level04}`;
    }

    // Add staffing status filter
    if (staffingStatus) {
      whereClause = sql`${whereClause} AND staffing_status = ${staffingStatus}`;
    }

    // Fetch positions with pagination
    positions = await sql<Position[]>`
      SELECT
        id,
        position_name,
        reference_id,
        worker_type,
        employee_type,
        time_type,
        staffing_status,
        available_for_hire,
        worker,
        contract_end_date,
        business_title,
        job_profile,
        frozen,
        freeze_date,
        freeze_reason,
        previous_incumbent,
        position_vacate_date,
        fte,
        job_family,
        job_family_groups,
        cost_center,
        class_indicator,
        manager,
        supervisory_organization,
        level_03,
        level_04,
        level_05,
        level_06,
        level_07,
        import_date
      FROM workday.position_details
      WHERE ${whereClause}
      ORDER BY reference_id
      LIMIT ${ITEMS_PER_PAGE}
      OFFSET ${offset}
    `;
  } catch (error) {
    console.error('Error fetching positions:', error);
    positions = [];
  }

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="w-full">
      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reference ID</TableHead>
              <TableHead>Position Name</TableHead>
              <TableHead>Business Title</TableHead>
              <TableHead>Worker</TableHead>
              <TableHead>Job Family</TableHead>
              <TableHead>Staffing Status</TableHead>
              <TableHead>Organization Unit</TableHead>
              <TableHead>FTE</TableHead>
              <TableHead>Frozen</TableHead>
              <TableHead>Available for Hire</TableHead>
              <TableHead className="sr-only">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {positions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="text-center py-4">
                  No positions found matching the current filters
                </TableCell>
              </TableRow>
            ) : (
              positions.map((position) => (
                <TableRow key={position.id} className="hover:bg-gray-50">
                  <TableCell>{position.reference_id}</TableCell>
                  <TableCell>{position.position_name}</TableCell>
                  <TableCell>{position.business_title}</TableCell>
                  <TableCell>{position.worker || 'Vacant'}</TableCell>
                  <TableCell>{position.job_family}</TableCell>
                  <TableCell>{position.staffing_status}</TableCell>
                  <TableCell>{position.level_04}</TableCell>
                  <TableCell>{position.fte}</TableCell>
                  <TableCell>{position.frozen === 'Yes' ? 'Yes' : 'No'}</TableCell>
                  <TableCell>{position.available_for_hire}</TableCell>
                  <TableCell className="whitespace-nowrap py-3 pl-6 pr-3">
                    <div className="flex justify-end gap-3">
                      <Link
                        href={`/dashboard/position-control/${position.id}`}
                        className="rounded-md border p-2 hover:bg-gray-100"
                      >
                        <EyeIcon className="w-5" />
                      </Link>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      <div className="text-sm text-gray-500 mt-4">
        Showing {positions.length} positions
      </div>
    </div>
  );
}
