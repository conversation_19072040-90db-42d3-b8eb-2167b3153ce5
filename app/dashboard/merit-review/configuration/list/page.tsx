"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PencilIcon, EyeIcon, PlusIcon, Trash2Icon } from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

interface WorkflowConfig {
  id: number;
  unit_id: number;
  unit_name: string;
  level_number: number;
  start_dt: string;
  end_dt: string;
  status: string;
  description?: string;
  created_at: string;
  updated_at: string;
  can_edit: boolean;
}

export default function WorkflowConfigurationListPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [workflows, setWorkflows] = useState<WorkflowConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [unitFilter, setUnitFilter] = useState<string>("");
  const [units, setUnits] = useState<any[]>([]);

  // Redirect if not authenticated or not authorized
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
    } else if (authStatus === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("faculty_admin") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
      }
    }
  }, [authStatus, session, router]);

  // Fetch units
  useEffect(() => {
    const fetchUnits = async () => {
      try {
        const response = await fetch("/api/units");
        if (response.ok) {
          const data = await response.json();
          setUnits(data);
        } else {
          console.error("Failed to fetch units");
        }
      } catch (error) {
        console.error("Error fetching units:", error);
      }
    };

    if (authStatus === "authenticated") {
      fetchUnits();
    }
  }, [authStatus]);

  // Fetch workflows
  useEffect(() => {
    const fetchWorkflows = async () => {
      setIsLoading(true);
      try {
        // Get the current user's faculty data to determine their unit
        const facultyResponse = await fetch("/api/faculty/current");
        if (!facultyResponse.ok) {
          throw new Error("Failed to fetch faculty data");
        }
        const facultyData = await facultyResponse.json();

        let url = "/api/merit-review/workflow";
        const params = new URLSearchParams();
        params.append("user_unit_id", facultyData.primary_unit_id);

        if (statusFilter) {
          params.append("status", statusFilter);
        }

        if (unitFilter) {
          params.append("unit_id", unitFilter);
        }

        if (params.toString()) {
          url += `?${params.toString()}`;
        }

        const response = await fetch(url);
        if (response.ok) {
          const data = await response.json();
          setWorkflows(data);
        } else {
          console.error("Failed to fetch workflows");
        }
      } catch (error) {
        console.error("Error fetching workflows:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (authStatus === "authenticated") {
      fetchWorkflows();
    }
  }, [authStatus, statusFilter, unitFilter]);

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "draft":
        return "secondary";
      case "active":
        return "default";
      case "completed":
        return "outline";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (error) {
      return dateString;
    }
  };

  const canEditWorkflow = (workflow: WorkflowConfig) => {
    if (!session?.user?.roles) return false;
    
    // System admin can edit any workflow
    if (session.user.roles.includes("system_admin")) return true;
    
    // Faculty admin can edit faculty level workflows (level 3)
    if (session.user.roles.includes("faculty_admin") && workflow.level_number === 3) return true;
    
    // Department admin can only edit their own department's workflows (level 4)
    if (session.user.roles.includes("department_admin") && workflow.level_number === 4) {
      return workflow.can_edit;
    }
    
    return false;
  };

  const handleDeleteWorkflow = async (workflowId: number) => {
    if (!confirm("Are you sure you want to delete this workflow? This action cannot be undone.")) {
      return;
    }

    try {
      const response = await fetch(`/api/merit-review/workflow/${workflowId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete workflow");
      }

      toast.success("Workflow deleted successfully");
      // Refresh the workflows list
      const facultyResponse = await fetch("/api/faculty/current");
      if (!facultyResponse.ok) {
        throw new Error("Failed to fetch faculty data");
      }
      const facultyData = await facultyResponse.json();

      const workflowsResponse = await fetch(`/api/merit-review/workflow?user_unit_id=${facultyData.primary_unit_id}`);
      if (workflowsResponse.ok) {
        const data = await workflowsResponse.json();
        setWorkflows(data);
      }
    } catch (error) {
      console.error("Error deleting workflow:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete workflow");
    }
  };

  if (authStatus === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Merit Workflow Configurations</h1>
        <Button asChild>
          <Link href="/dashboard/merit-review/configuration">
            <PlusIcon className="h-4 w-4 mr-2" />
            Create New
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter workflow configurations by status or unit</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status-filter">
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="unit-filter">Academic Unit</Label>
              <Select value={unitFilter} onValueChange={setUnitFilter}>
                <SelectTrigger id="unit-filter">
                  <SelectValue placeholder="All units" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All units</SelectItem>
                  {units.map((unit) => (
                    <SelectItem key={unit.unit_id} value={unit.unit_id.toString()}>
                      {unit.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Workflow Configurations</CardTitle>
          <CardDescription>
            List of all merit review workflow configurations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">Loading...</div>
          ) : workflows.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No workflow configurations found.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workflows.map((workflow) => (
                    <TableRow key={workflow.id}>
                      <TableCell>{workflow.id}</TableCell>
                      <TableCell>{workflow.unit_name}</TableCell>
                      <TableCell>{formatDate(workflow.start_dt)}</TableCell>
                      <TableCell>{formatDate(workflow.end_dt)}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(workflow.status)}>
                          {workflow.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(workflow.created_at)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <Link href={`/dashboard/merit-review/configuration/view/${workflow.id}`}>
                              <EyeIcon className="h-4 w-4" />
                              <span className="sr-only">View</span>
                            </Link>
                          </Button>
                          {workflow.status === "draft" && canEditWorkflow(workflow) && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                asChild
                              >
                                <Link href={`/dashboard/merit-review/configuration/edit/${workflow.id}`}>
                                  <PencilIcon className="h-4 w-4" />
                                  <span className="sr-only">Edit</span>
                                </Link>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteWorkflow(workflow.id)}
                              >
                                <Trash2Icon className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
