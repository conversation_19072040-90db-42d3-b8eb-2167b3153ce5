"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import Link from "next/link";

export default function MeritReviewConfigurationPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [units, setUnits] = useState<any[]>([]);
  const [selectedUnit, setSelectedUnit] = useState<string | undefined>(undefined);
  const [description, setDescription] = useState<string>("");
  const [workflowStatus, setWorkflowStatus] = useState<string>("draft");
  const [isLoading, setIsLoading] = useState(false);

  // Redirect if not authenticated or not faculty_admin
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
      return;
    }

    if (authStatus === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
      }
    }
  }, [authStatus, session, router]);

  // Fetch units and current faculty data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch units
        const unitsResponse = await fetch("/api/units");
        let unitsData;
        if (unitsResponse.ok) {
          unitsData = await unitsResponse.json();
          setUnits(unitsData);
        } else {
          console.error("Failed to fetch units");
          return;
        }

        // Fetch current faculty data
        const facultyResponse = await fetch("/api/faculty/current");
        if (facultyResponse.ok) {
          const facultyData = await facultyResponse.json();
          if (facultyData && facultyData.primary_unit_id) {
            // Get the primary unit
            const primaryUnit = unitsData.find((unit: any) => unit.unit_id === facultyData.primary_unit_id);
            if (primaryUnit) {
              let currentUnit = primaryUnit;
              
              // If user is faculty_admin, find level 3 unit
              if (session?.user?.roles?.includes("faculty_admin")) {
                while (currentUnit && currentUnit.level_number !== 3) {
                  if (currentUnit.parent_unit_id) {
                    const parentUnit = unitsData.find((unit: any) => unit.unit_id === currentUnit.parent_unit_id);
                    if (parentUnit) {
                      currentUnit = parentUnit;
                    } else {
                      break;
                    }
                  } else {
                    break;
                  }
                }
              }
              // If user is department_admin, find level 4 unit
              else if (session?.user?.roles?.includes("department_admin")) {
                while (currentUnit && currentUnit.level_number !== 4) {
                  if (currentUnit.parent_unit_id) {
                    const parentUnit = unitsData.find((unit: any) => unit.unit_id === currentUnit.parent_unit_id);
                    if (parentUnit) {
                      currentUnit = parentUnit;
                    } else {
                      break;
                    }
                  } else {
                    break;
                  }
                }
              }

              // Set the selected unit if we found the appropriate level
              if (currentUnit && (
                (session?.user?.roles?.includes("faculty_admin") && currentUnit.level_number === 3) ||
                (session?.user?.roles?.includes("department_admin") && currentUnit.level_number === 4)
              )) {
                setSelectedUnit(currentUnit.unit_id.toString());

                // Auto-populate description based on role and unit
                const currentYear = new Date().getFullYear();
                if (session?.user?.roles?.includes("faculty_admin")) {
                  setDescription(`Merit Review for ${currentUnit.short_name || currentUnit.full_name} ${currentYear}`);
                } else if (session?.user?.roles?.includes("department_admin")) {
                  setDescription(`Merit Review for ${currentUnit.short_name || currentUnit.full_name} ${currentYear}`);
                }

              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    if (authStatus === "authenticated") {
      fetchData();
    }
  }, [authStatus, session]);

  const handleCreateWorkflow = async () => {
    if (!startDate || !endDate || !selectedUnit) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (startDate >= endDate) {
      toast.error("End date must be after start date");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/merit-review/workflow", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          unit_id: selectedUnit,
          start_dt: startDate.toISOString(),
          end_dt: endDate.toISOString(),
          status: workflowStatus,
          description: description.trim() || null
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create workflow");
      }

      toast.success("Workflow created successfully");
      router.push("/dashboard/merit-review/configuration/list");
    } catch (error) {
      console.error("Error creating workflow:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create workflow");
    } finally {
      setIsLoading(false);
    }
  };

  if (authStatus === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Create Workflow Configuration</h1>
        <Button variant="outline" asChild>
          <Link href="/dashboard/merit-review/configuration/list">Back to List</Link>
        </Button>
      </div>

      <style jsx global>{`
        .required:after {
          content: " *";
          color: #ef4444;
        }
      `}</style>

      <Card>
        <CardHeader>
          <CardTitle>Create New Workflow</CardTitle>
          <CardDescription>
            Set up the timeline and parameters for the merit review process
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="unit" className="required">Academic Unit</Label>
            <Select 
              value={selectedUnit || "none"} 

              onValueChange={(value) => {
                setSelectedUnit(value === "none" ? undefined : value);
                // Update description when unit changes
                const selectedUnitData = units.find((unit: any) => unit.unit_id.toString() === value);
                if (selectedUnitData) {
                  const currentYear = new Date().getFullYear();
                  if (session?.user?.roles?.includes("faculty_admin") && selectedUnitData.level_number === 3) {
                    setDescription(`Merit Review for ${selectedUnitData.short_name || selectedUnitData.full_name} ${currentYear}`);
                  } else if (session?.user?.roles?.includes("department_admin") && selectedUnitData.level_number === 4) {
                    setDescription(`Merit Review for ${selectedUnitData.short_name || selectedUnitData.full_name} ${currentYear}`);
                  }
                }
              }}

              required
            >
              <SelectTrigger id="unit">
                <SelectValue placeholder="Select unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none" disabled>
                  Select a unit
                </SelectItem>

                {units
                  .filter((unit: any) => 
                    session?.user?.roles?.includes("faculty_admin") 
                      ? unit.level_number === 3 
                      : session?.user?.roles?.includes("department_admin") 
                        ? unit.level_number === 4 
                        : false
                  )
                  .map((unit) => (
                    <SelectItem 
                      key={unit.unit_id} 
                      value={unit.unit_id.toString()}
                    >
                      {unit.full_name}
                    </SelectItem>
                  ))}

              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              placeholder="Enter workflow description (optional)"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="workflowStatus">Status</Label>
            <Select value={workflowStatus} onValueChange={setWorkflowStatus}>
              <SelectTrigger id="workflowStatus">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">Draft workflows can be edited later before activation.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="required">Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label className="required">End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <Button
            onClick={handleCreateWorkflow}
            disabled={isLoading}
            className="mt-4"
          >
            {isLoading ? "Creating..." : "Create Workflow"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
