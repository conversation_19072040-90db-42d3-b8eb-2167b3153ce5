"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";

interface Unit {
  unit_id: number;
  full_name: string;
  level_number: number;
}

export default function CreateWorkflowPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [units, setUnits] = useState<Unit[]>([]);
  const [formData, setFormData] = useState({
    unit_id: "",
    start_dt: format(new Date(), "yyyy-MM-dd"),
    end_dt: format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), "yyyy-MM-dd"),
    description: "",
  });

  // Redirect if not authenticated or not authorized
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
    } else if (authStatus === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
      }
    }
  }, [authStatus, session, router]);

  // Fetch units
  useEffect(() => {
    const fetchUnits = async () => {
      try {
        const response = await fetch("/api/units");
        if (response.ok) {
          const data = await response.json();
          // Filter for department level units (level 4)
          const departmentUnits = data.filter((unit: Unit) => unit.level_number === 4);
          setUnits(departmentUnits);
          
          // If user is department admin, pre-select their unit
          if (session?.user?.roles?.includes("department_admin")) {
            const facultyResponse = await fetch("/api/faculty/current");
            if (facultyResponse.ok) {
              const facultyData = await facultyResponse.json();
              const userUnit = departmentUnits.find(
                (unit: Unit) => unit.unit_id === facultyData.primary_unit_id
              );
              if (userUnit) {
                setFormData(prev => ({ ...prev, unit_id: userUnit.unit_id.toString() }));
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching units:", error);
        toast.error("Failed to fetch units");
      }
    };

    if (authStatus === "authenticated") {
      fetchUnits();
    }
  }, [authStatus]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch("/api/merit-review/workflow", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          unit_id: parseInt(formData.unit_id),
          status: "draft",
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create workflow");
      }

      toast.success("Workflow created successfully");
      router.push("/dashboard/merit-review/configuration/list");
    } catch (error) {
      console.error("Error creating workflow:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create workflow");
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  if (authStatus === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Create New Workflow</CardTitle>
            <CardDescription>
              Create a new merit review workflow for your department
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="unit_id">Department</Label>
                <select
                  id="unit_id"
                  name="unit_id"
                  value={formData.unit_id}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={session?.user?.roles?.includes("department_admin")}
                >
                  <option value="">Select a department</option>
                  {units.map((unit) => (
                    <option key={unit.unit_id} value={unit.unit_id}>
                      {unit.full_name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="start_dt">Start Date</Label>
                <Input
                  id="start_dt"
                  name="start_dt"
                  type="date"
                  value={formData.start_dt}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_dt">End Date</Label>
                <Input
                  id="end_dt"
                  name="end_dt"
                  type="date"
                  value={formData.end_dt}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  name="description"
                  type="text"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Merit Review for {department} {year}"
                />
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/dashboard/merit-review/configuration/list")}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Workflow"
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 