"use client";

import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export default function EditWorkflowConfigurationPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const params = useParams();
  const workflowId = params.id as string;
  
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [units, setUnits] = useState<any[]>([]);
  const [selectedUnit, setSelectedUnit] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [workflowStatus, setWorkflowStatus] = useState<string>("draft");
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated or not faculty_admin
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
      return;
    }

    if (authStatus === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
      }
    }
  }, [authStatus, session, router]);

  // Fetch units
  useEffect(() => {
    const fetchUnits = async () => {
      try {
        const response = await fetch("/api/units");
        if (response.ok) {
          const data = await response.json();
          setUnits(data);
        } else {
          console.error("Failed to fetch units");
          // Fallback mock data
          setUnits([
            { unit_id: 1, full_name: "Faculty of Engineering" },
            { unit_id: 2, full_name: "Faculty of Science" },
            { unit_id: 3, full_name: "Faculty of Arts" },
          ]);
        }
      } catch (error) {
        console.error("Error fetching units:", error);
        // Fallback mock data
        setUnits([
          { unit_id: 1, full_name: "Faculty of Engineering" },
          { unit_id: 2, full_name: "Faculty of Science" },
          { unit_id: 3, full_name: "Faculty of Arts" },
        ]);
      }
    };

    if (authStatus === "authenticated") {
      fetchUnits();
    }
  }, [authStatus]);

  // Fetch workflow data
  useEffect(() => {
    const fetchWorkflow = async () => {
      setIsFetching(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/merit-review/workflow/${workflowId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch workflow");
        }
        
        const workflow = await response.json();
        
        // Check if workflow is in draft status
        if (workflow.status !== "draft") {
          setError("This workflow cannot be edited because it is not in draft status.");
          setIsFetching(false);
          return;
        }
        
        // Set form values
        setSelectedUnit(workflow.unit_id.toString());
        setStartDate(new Date(workflow.start_dt));
        setEndDate(new Date(workflow.end_dt));
        setDescription(workflow.description || "");
        setWorkflowStatus(workflow.status);
      } catch (error) {
        console.error("Error fetching workflow:", error);
        setError(error instanceof Error ? error.message : "Failed to fetch workflow");
      } finally {
        setIsFetching(false);
      }
    };

    if (authStatus === "authenticated" && workflowId) {
      fetchWorkflow();
    }
  }, [authStatus, workflowId]);

  const handleUpdateWorkflow = async () => {
    if (!startDate || !endDate || !selectedUnit) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (startDate >= endDate) {
      toast.error("End date must be after start date");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/merit-review/workflow/${workflowId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          unit_id: selectedUnit,
          start_dt: startDate.toISOString(),
          end_dt: endDate.toISOString(),
          status: workflowStatus,
          description: description.trim() || null
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update workflow");
      }

      toast.success("Workflow updated successfully");
      router.push("/dashboard/merit-review/configuration/list");
    } catch (error) {
      console.error("Error updating workflow:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update workflow");
    } finally {
      setIsLoading(false);
    }
  };

  if (authStatus === "loading" || isFetching) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Edit Workflow Configuration</h1>
        <Button variant="outline" asChild>
          <Link href="/dashboard/merit-review/configuration/list">Back to List</Link>
        </Button>
      </div>
      
      <style jsx global>{`
        .required:after {
          content: " *";
          color: #ef4444;
        }
      `}</style>

      {error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Edit Workflow</CardTitle>
            <CardDescription>
              Update the timeline and parameters for the merit review process
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="unit" className="required">Academic Unit</Label>
              <Select value={selectedUnit} onValueChange={setSelectedUnit} required>
                <SelectTrigger id="unit">
                  <SelectValue placeholder="Select unit" />
                </SelectTrigger>
                <SelectContent>
                  {units.map((unit) => (
                    <SelectItem key={unit.unit_id} value={unit.unit_id.toString()}>
                      {unit.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                placeholder="Enter workflow description (optional)"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="workflowStatus">Status</Label>
              <Select value={workflowStatus} onValueChange={setWorkflowStatus}>
                <SelectTrigger id="workflowStatus">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Setting to Active will make this workflow available for faculty submissions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="required">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label className="required">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <Button 
              onClick={handleUpdateWorkflow} 
              disabled={isLoading}
              className="mt-4"
            >
              {isLoading ? "Updating..." : "Update Workflow"}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
