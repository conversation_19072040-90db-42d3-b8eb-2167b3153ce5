"use client";

import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, PencilIcon, CalendarIcon, ClockIcon, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { RolesFlowChart } from "@/components/merit-review/RolesFlowChart";

interface WorkflowConfig {
  id: number;
  unit_id: number;
  unit_name: string;
  start_dt: string;
  end_dt: string;
  status: string;
  description?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  level_number: number;
  can_edit: boolean;
}

export default function ViewWorkflowConfigurationPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const params = useParams();
  const workflowId = params.id as string;

  const [workflow, setWorkflow] = useState<WorkflowConfig | null>(null);
  const [isFetching, setIsFetching] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated or not faculty_admin
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
      return;
    }

    if (authStatus === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("faculty_admin") && !roles.includes("department_admin") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
      }
    }
  }, [authStatus, session, router]);

  // Fetch workflow data
  useEffect(() => {
    const fetchWorkflow = async () => {
      setIsFetching(true);
      setError(null);

      try {
        const response = await fetch(`/api/merit-review/workflow/${workflowId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch workflow");
        }

        const workflowData = await response.json();
        setWorkflow(workflowData);
      } catch (error) {
        console.error("Error fetching workflow:", error);
        setError(error instanceof Error ? error.message : "Failed to fetch workflow");
      } finally {
        setIsFetching(false);
      }
    };

    if (authStatus === "authenticated" && workflowId) {
      fetchWorkflow();
    }
  }, [authStatus, workflowId]);

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "draft":
        return "secondary";
      case "active":
        return "default";
      case "completed":
        return "outline";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "PPP");
    } catch (error) {
      return dateString;
    }
  };

  const canEditWorkflow = (workflow: WorkflowConfig) => {
    if (!session?.user?.roles) return false;

    // System admin can edit any workflow
    if (session.user.roles.includes("system_admin")) return true;

    // Faculty admin can edit faculty level workflows (level 3)
    if (session.user.roles.includes("faculty_admin") && workflow.level_number === 3) return true;

    // Department admin can only edit their own department's workflows (level 4)
    if (session.user.roles.includes("department_admin") && workflow.level_number === 4) {
      return workflow.can_edit;
    }

    return false;
  };

  if (authStatus === "loading" || isFetching) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Workflow Configuration Details</h1>
        <div className="flex gap-2">
          {workflow?.status === "draft" && workflow && canEditWorkflow(workflow) && (
            <Button variant="outline" asChild>
              <Link href={`/dashboard/merit-review/configuration/edit/${workflowId}`}>
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
          )}
          <Button variant="outline" asChild>
            <Link href="/dashboard/merit-review?tab=configuration">
              Back to List
            </Link>
          </Button>
        </div>
      </div>

      {error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : workflow ? (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Workflow configuration details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">ID</p>
                    <p>{workflow.id}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <Badge variant={getStatusBadgeVariant(workflow.status)}>
                      {workflow.status}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Academic Unit</p>
                    <p>{workflow.unit_name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Created</p>
                    <p>{formatDate(workflow.created_at)}</p>
                  </div>
                </div>

                {workflow.description && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Description</p>
                    <p>{workflow.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Timeline</CardTitle>
                <CardDescription>
                  Merit review process timeline
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Start Date</p>
                    <p>{formatDate(workflow.start_dt)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">End Date</p>
                    <p>{formatDate(workflow.end_dt)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Duration</p>
                    <p>
                      {Math.ceil(
                        (new Date(workflow.end_dt).getTime() - new Date(workflow.start_dt).getTime()) /
                        (1000 * 60 * 60 * 24)
                      )} days
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Roles and Committee Members Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Roles and Committee Members
              </CardTitle>
              <CardDescription>
                Department roles and committee members involved in the merit review process
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RolesFlowChart unitId={workflow.unit_id} />
            </CardContent>
          </Card>
        </div>
      ) : (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Data</AlertTitle>
          <AlertDescription>No workflow configuration found.</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
