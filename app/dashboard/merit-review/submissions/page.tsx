'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import {
  FileText,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileCheck,
  FileQuestion,
  ChevronRight,
  PieChart,
  Loader2,
  Star,
  FileDown,
  Download
} from 'lucide-react';
import { toast } from 'sonner';
import { downloadMeritReviewPDF } from '@/app/lib/utils/pdf-generator';

// Status colors for badges and charts
const statusColors = {
  'not_started': { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-200', color: '#94a3b8' },
  'draft': { bg: 'bg-yellow-50', text: 'text-yellow-700', border: 'border-yellow-200', color: '#eab308' },
  'in_progress': { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200', color: '#3b82f6' },
  'submitted': { bg: 'bg-green-50', text: 'text-green-700', border: 'border-green-200', color: '#22c55e' },
  'under_review': { bg: 'bg-purple-50', text: 'text-purple-700', border: 'border-purple-200', color: '#a855f7' },
  'reviewed': { bg: 'bg-indigo-50', text: 'text-indigo-700', border: 'border-indigo-200', color: '#6366f1' },
  'approved': { bg: 'bg-emerald-50', text: 'text-emerald-700', border: 'border-emerald-200', color: '#10b981' }
};

// Status display names
const statusNames = {
  'not_started': 'Not Started',
  'draft': 'Draft',
  'in_progress': 'In Progress',
  'submitted': 'Submitted',
  'under_review': 'Under Review',
  'reviewed': 'Reviewed',
  'approved': 'Approved'
};

// Interface for submission data
interface Submission {
  id: number;
  faculty_id: number;
  faculty_name: string;
  unit_id: number;
  unit_name: string;
  report_type: string;
  report_year: number;
  status: string;
  create_dt: string;
  update_dt: string;
  submit_dt?: string;
  rating_count?: number;
  submitted_rating_count?: number;
  in_progress_rating_count?: number;
}

// Interface for rating data
interface Rating {
  id: number;
  report_id: number;
  reviewer_id: number;
  reviewer_name: string;
  teaching_rating: number;
  research_rating: number;
  service_rating: number;
  comments: string;
  is_submitted: boolean;
  created_at: string;
  updated_at: string;
}

// Interface for status counts
interface StatusCounts {
  not_started: number;
  draft: number;
  in_progress: number;
  submitted: number;
  under_review: number;
  reviewed: number;
  approved: number;
  total: number;
}

// Mini flow chart component for submission status
const StatusFlowChart = ({
  status,
  ratingCount,
  submittedRatingCount,
  inProgressRatingCount
}: {
  status: string;
  ratingCount?: number;
  submittedRatingCount?: number;
  inProgressRatingCount?: number;
}) => {
  const steps = ['draft', 'in_progress', 'submitted', 'under_review', 'reviewed', 'approved'];
  const currentIndex = steps.indexOf(status);

  return (
    <div className="flex items-center space-x-1 text-xs">
      {steps.map((step, index) => (
        <div key={step} className="flex items-center relative group">
          <div
            className={`h-2 w-2 rounded-full ${
              index <= currentIndex
                ? statusColors[step as keyof typeof statusColors].bg.replace('bg-', 'bg-')
                : 'bg-gray-100'
            } ${
              index === currentIndex
                ? 'ring-2 ring-offset-1 ' + statusColors[step as keyof typeof statusColors].border.replace('border-', 'ring-')
                : ''
            }`}
          />
          {/* Tooltip for status details */}
          {index === currentIndex && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block z-10">
              <div className="bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                <div className="font-medium">{statusNames[step as keyof typeof statusNames]}</div>
                {step === 'under_review' && ratingCount !== undefined && (
                  <div className="text-gray-300 text-xs space-y-1">
                    <div>
                      <span className="font-medium">Total:</span> {ratingCount} rating{ratingCount !== 1 ? 's' : ''}
                    </div>
                    {submittedRatingCount !== undefined && submittedRatingCount > 0 && (
                      <div>
                        <span className="font-medium">Submitted:</span> {submittedRatingCount} rating{submittedRatingCount !== 1 ? 's' : ''}
                      </div>
                    )}
                    {inProgressRatingCount !== undefined && inProgressRatingCount > 0 && (
                      <div>
                        <span className="font-medium">In Progress:</span> {inProgressRatingCount} rating{inProgressRatingCount !== 1 ? 's' : ''}
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="border-t-4 border-l-4 border-r-4 border-transparent border-t-gray-800 w-0 h-0 absolute left-1/2 transform -translate-x-1/2"></div>
            </div>
          )}
          {index < steps.length - 1 && (
            <div className={`h-0.5 w-3 ${index < currentIndex ? 'bg-gray-300' : 'bg-gray-100'}`} />
          )}
        </div>
      ))}
    </div>
  );
};

// Ratings Dialog Component
const RatingsDialog = ({
  open,
  onOpenChange,
  submission,
  ratings,
  loading,
  formatDate
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  submission: Submission | null;
  ratings: Rating[];
  loading: boolean;
  formatDate: (date?: string) => string;
}) => {
  if (!submission) return null;

  const effectiveStatus = submission.rating_count && submission.rating_count > 0 && submission.status === 'submitted'
    ? 'under_review'
    : submission.status;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Review Ratings for {submission.faculty_name}</DialogTitle>
          <DialogDescription>
            All ratings submitted by committee members for this merit review submission
          </DialogDescription>
        </DialogHeader>

        {/* Rating Summary Section */}
        {effectiveStatus === 'under_review' && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium mb-3">Rating Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="bg-white p-3 rounded border space-y-1">
                <div className="text-xs text-gray-500">Total Ratings</div>
                <div className="text-lg font-medium">{submission.rating_count || 0}</div>
              </div>

              {submission.submitted_rating_count !== undefined && (
                <div className="bg-white p-3 rounded border space-y-1">
                  <div className="text-xs text-gray-500">Submitted Ratings</div>
                  <div className="text-lg font-medium text-green-600">{submission.submitted_rating_count}</div>
                </div>
              )}

              {submission.in_progress_rating_count !== undefined && (
                <div className="bg-white p-3 rounded border space-y-1">
                  <div className="text-xs text-gray-500">In Progress Ratings</div>
                  <div className="text-lg font-medium text-blue-600">{submission.in_progress_rating_count}</div>
                </div>
              )}
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <p className="mt-2 text-sm text-muted-foreground">Loading ratings...</p>
            </div>
          </div>
        ) : ratings.length === 0 && (!submission.rating_count || submission.rating_count === 0) ? (
          <div className="text-center py-8">
            <FileQuestion className="h-12 w-12 mx-auto text-muted-foreground" />
            <h3 className="mt-2 text-lg font-medium">No ratings found</h3>
            <p className="text-sm text-muted-foreground">
              No committee members have submitted ratings for this submission yet
            </p>
          </div>
        ) : ratings.length === 0 && submission.rating_count && submission.rating_count > 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 mx-auto text-blue-500" />
            <h3 className="mt-2 text-lg font-medium">Ratings in progress</h3>
            <p className="text-sm text-muted-foreground">
              {submission.submitted_rating_count && submission.submitted_rating_count > 0 ?
                `${submission.submitted_rating_count} rating${submission.submitted_rating_count !== 1 ? 's' : ''} submitted` : ''}
              {submission.submitted_rating_count && submission.submitted_rating_count > 0 &&
               submission.in_progress_rating_count && submission.in_progress_rating_count > 0 ? ', ' : ''}
              {submission.in_progress_rating_count && submission.in_progress_rating_count > 0 ?
                `${submission.in_progress_rating_count} rating${submission.in_progress_rating_count !== 1 ? 's' : ''} in progress` : ''}
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Detailed rating information will be available once the committee completes their review
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {ratings.map((rating) => (
              <Card key={rating.id} className="overflow-hidden">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">Reviewer: {rating.reviewer_name}</h3>
                      <div className="flex items-center">
                        <span className="text-xs text-muted-foreground mr-2">
                          Submitted: {formatDate(rating.created_at)}
                        </span>
                        <Badge variant={rating.is_submitted ? "default" : "secondary"} className={rating.is_submitted ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}>
                          {rating.is_submitted ? "Submitted" : "In Progress"}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="border rounded-md p-3 space-y-1">
                        <div className="text-sm font-medium flex items-center">
                          <span>Teaching</span>
                        </div>
                        <div className="text-2xl font-semibold">{rating.teaching_rating.toFixed(1)}</div>
                      </div>

                      <div className="border rounded-md p-3 space-y-1">
                        <div className="text-sm font-medium flex items-center">
                          <span>Research</span>
                        </div>
                        <div className="text-2xl font-semibold">{rating.research_rating.toFixed(1)}</div>
                      </div>

                      <div className="border rounded-md p-3 space-y-1">
                        <div className="text-sm font-medium flex items-center">
                          <span>Service</span>
                        </div>
                        <div className="text-2xl font-semibold">{rating.service_rating.toFixed(1)}</div>
                      </div>
                    </div>

                    {rating.comments && (
                      <div className="pt-2">
                        <h4 className="text-sm font-medium mb-1">Comments:</h4>
                        <p className="text-sm text-muted-foreground whitespace-pre-wrap">{rating.comments}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}



// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const statusStyle = statusColors[status as keyof typeof statusColors] || statusColors.draft;

  return (
    <Badge variant="outline" className={`${statusStyle.bg} ${statusStyle.text} ${statusStyle.border}`}>
      {statusNames[status as keyof typeof statusNames] || status}
    </Badge>
  );
};

// Status counts visualization component
const StatusCountsChart = ({ counts }: { counts: StatusCounts }) => {
  const statuses = ['not_started', 'draft', 'in_progress', 'submitted', 'under_review', 'reviewed', 'approved'];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Submission Status Overview</h3>
        <div className="flex items-center text-sm text-muted-foreground">
          <PieChart className="h-4 w-4 mr-1" />
          <span>Total Faculty: {counts.total}</span>
        </div>
      </div>

      <div className="h-2 w-full rounded-full bg-gray-100 overflow-hidden flex">
        {statuses.map(status => {
          const count = counts[status as keyof typeof counts] as number;
          const percentage = counts.total > 0 ? (count / counts.total) * 100 : 0;

          return percentage > 0 ? (
            <div
              key={status}
              className="h-full"
              style={{
                width: `${percentage}%`,
                backgroundColor: statusColors[status as keyof typeof statusColors].color
              }}
              title={`${statusNames[status as keyof typeof statusNames]}: ${count} (${percentage.toFixed(1)}%)`}
            />
          ) : null;
        })}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
        {statuses.map(status => {
          const count = counts[status as keyof typeof counts] as number;
          const percentage = counts.total > 0 ? (count / counts.total) * 100 : 0;

          if (count === 0) return null;

          return (
            <div key={status} className="flex items-center justify-between p-2 rounded-md border">
              <div className="flex items-center">
                <div
                  className="h-3 w-3 rounded-full mr-2"
                  style={{ backgroundColor: statusColors[status as keyof typeof statusColors].color }}
                />
                <span className="text-xs font-medium">{statusNames[status as keyof typeof statusNames]}</span>
              </div>
              <div className="text-xs text-muted-foreground">
                {count} ({percentage.toFixed(1)}%)
              </div>
            </div>
          );
        })}
      </div>

      <div className="text-xs text-muted-foreground mt-2">
        <p>* Percentages are calculated based on the total number of faculty members eligible for merit review.</p>
        {counts.not_started > 0 && (
          <p>* "Not Started" represents faculty members who haven't created a merit report yet.</p>
        )}
      </div>
    </div>
  );
}

export default function SubmissionsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<Submission[]>([]);
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    not_started: 0,
    draft: 0,
    in_progress: 0,
    submitted: 0,
    under_review: 0,
    reviewed: 0,
    approved: 0,
    total: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [unitFilter, setUnitFilter] = useState('all');
  const [units, setUnits] = useState<{id: number, name: string}[]>([]);
  const [showRatingsDialog, setShowRatingsDialog] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  const [ratings, setRatings] = useState<Rating[]>([]);
  const [loadingRatings, setLoadingRatings] = useState(false);
  const [generatingPDF, setGeneratingPDF] = useState(false);

  // Check if user has admin role
  const isAdmin = session?.user?.roles?.includes('system_admin') ||
                  session?.user?.roles?.includes('faculty_admin') ||
                  session?.user?.roles?.includes('department_admin');

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated' && !isAdmin) {
      router.push('/dashboard/merit-review');
      return;
    }
  }, [status, isAdmin, router]);

  // Fetch submissions data
  useEffect(() => {
    const fetchSubmissions = async () => {
      if (status === 'authenticated' && isAdmin) {
        try {
          setLoading(true);
          const response = await fetch('/api/merit-review/submissions/admin');

          if (response.ok) {
            const data = await response.json();
            const submissionsList = data.submissions || [];
            const metadata = data.metadata || { not_started_count: 0, total_faculty_count: 0 };

            setSubmissions(submissionsList);
            setFilteredSubmissions(submissionsList);

            // Calculate status counts
            const counts: StatusCounts = {
              not_started: metadata.not_started_count || 0,
              draft: 0,
              in_progress: 0,
              submitted: 0,
              under_review: 0,
              reviewed: 0,
              approved: 0,
              total: metadata.total_faculty_count || submissionsList.length
            };

            // Count submissions by status
            submissionsList.forEach((submission: Submission) => {
              // If submission has ratings and status is 'submitted', count it as 'under_review'
              const effectiveStatus = submission.rating_count && submission.rating_count > 0 && submission.status === 'submitted'
                ? 'under_review'
                : submission.status;

              if (effectiveStatus in counts) {
                counts[effectiveStatus as keyof typeof counts] =
                  (counts[effectiveStatus as keyof typeof counts] as number) + 1;
              }
            });

            setStatusCounts(counts);

            // Extract unique units
            const uniqueUnits = Array.from(new Set(submissionsList.map((s: Submission) => s.unit_id)))
              .map(unitId => {
                const submission = submissionsList.find((s: Submission) => s.unit_id === unitId);
                return {
                  id: Number(unitId),
                  name: submission ? submission.unit_name : 'Unknown'
                };
              });

            setUnits(uniqueUnits);
          } else {
            toast.error('Failed to fetch submissions');
          }
        } catch (error) {
          console.error('Error fetching submissions:', error);
          toast.error('An error occurred while fetching submissions');
        } finally {
          setLoading(false);
        }
      }
    };

    fetchSubmissions();
  }, [status, isAdmin]);

  // Apply filters when search term, status filter, or unit filter changes
  useEffect(() => {
    if (submissions.length === 0) return;

    let filtered = [...submissions];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(submission =>
        submission.faculty_name.toLowerCase().includes(term) ||
        submission.unit_name.toLowerCase().includes(term) ||
        submission.report_type.toLowerCase().includes(term) ||
        submission.report_year.toString().includes(term)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(submission => {
        const effectiveStatus = submission.rating_count && submission.rating_count > 0 && submission.status === 'submitted'
          ? 'under_review'
          : submission.status;
        return effectiveStatus === statusFilter;
      });
    }

    // Apply unit filter
    if (unitFilter !== 'all') {
      filtered = filtered.filter(submission => submission.unit_id.toString() === unitFilter);
    }

    setFilteredSubmissions(filtered);
  }, [searchTerm, statusFilter, unitFilter, submissions]);

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Generate PDF for a submission
  const handleGeneratePDF = async (submission: Submission) => {
    try {
      setGeneratingPDF(true);

      // Fetch the full report data
      const response = await fetch(`/api/merit-review/report/admin?id=${submission.id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch report data');
      }

      const reportData = await response.json();

      // Prepare data for PDF generation
      const pdfData = {
        // Basic information
        faculty_name: submission.faculty_name,
        department: submission.unit_name,
        unit_name: submission.unit_name,
        rank: reportData.faculty_rank || '',
        administrative_role: reportData.administrative_role || '',
        research_chair: reportData.research_chair || '',
        report_type: submission.report_type,
        report_year: submission.report_year,
        status: submission.status,

        // Include all sections from the report
        teaching: reportData.teaching || {},
        teaching_courses: reportData.teaching?.courses || [],
        teaching_development_activities: reportData.teaching?.development_activities || [],
        student_supervision: reportData.student_supervision || {},
        undergrad_supervision: reportData.teaching?.undergrad_supervision || [],
        grad_supervision_completed: reportData.teaching?.grad_supervision_completed || [],
        grad_supervision_in_progress: reportData.teaching?.grad_supervision_in_progress || [],
        course_masters_supervision: reportData.teaching?.course_masters_supervision || [],
        postdoc_supervision: reportData.teaching?.postdoc_supervision || [],

        // Research section
        research: reportData.research || {},
        research_publications: reportData.research?.publications || [],
        research_grants: reportData.research?.grants || [],

        // Service section
        service: reportData.service || {},
        service_activities: reportData.service?.activities || [],
        professional_registrations: reportData.service?.registrations || [],

        // Awards and comments
        awards: reportData.awards || [],
        additionalComments: reportData.additionalComments || {},
      };

      // Generate and download the PDF
      downloadMeritReviewPDF(pdfData, `merit-review-${submission.faculty_name.replace(/\s+/g, '-')}-${submission.report_year}.pdf`);

      toast.success('PDF generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF');
    } finally {
      setGeneratingPDF(false);
    }
  };

  // Fetch ratings for a submission
  const fetchRatings = async (submission: Submission) => {
    setSelectedSubmission(submission);
    setLoadingRatings(true);
    try {
      const response = await fetch(`/api/merit-review/submissions/ratings?report_id=${submission.id}`);
      if (response.ok) {
        const data = await response.json();
        setRatings(data.ratings || []);
      } else {
        toast.error('Failed to fetch ratings');
        setRatings([]);
      }
    } catch (error) {
      console.error('Error fetching ratings:', error);
      toast.error('An error occurred while fetching ratings');
      setRatings([]);
    } finally {
      setLoadingRatings(false);
      setShowRatingsDialog(true);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          <p className="mt-4 text-lg">Loading submissions...</p>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return null;
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You do not have permission to access this page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Merit Review Submissions</h1>
        <p className="text-muted-foreground">
          View and manage all merit review submissions
        </p>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Submission Statistics</CardTitle>
          <CardDescription>
            Overview of all faculty members eligible for merit review
          </CardDescription>
        </CardHeader>
        <CardContent>
          <StatusCountsChart counts={statusCounts} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Submissions</CardTitle>
          <CardDescription>
            All merit review submissions in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Filters */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search" className="sr-only">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by name, unit, or report type..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex flex-row gap-2">
                <div className="w-40">
                  <Label htmlFor="status-filter" className="sr-only">Status</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger id="status-filter" className="w-full">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="not_started">Not Started</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="submitted">Submitted</SelectItem>
                      <SelectItem value="under_review">Under Review</SelectItem>
                      <SelectItem value="reviewed">Reviewed</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="w-40">
                  <Label htmlFor="unit-filter" className="sr-only">Unit</Label>
                  <Select value={unitFilter} onValueChange={setUnitFilter}>
                    <SelectTrigger id="unit-filter" className="w-full">
                      <SelectValue placeholder="Filter by unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Units</SelectItem>
                      {units.map(unit => (
                        <SelectItem key={unit.id} value={unit.id.toString()}>
                          {unit.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <Separator />

            {/* Results count */}
            <div className="text-sm text-muted-foreground">
              Showing {filteredSubmissions.length} of {submissions.length} submissions
              {statusCounts.not_started > 0 && (
                <span> (plus {statusCounts.not_started} faculty members who haven't started)</span>
              )}
            </div>

            {/* Submissions list */}
            {filteredSubmissions.length === 0 ? (
              <div className="text-center py-8">
                <FileQuestion className="h-12 w-12 mx-auto text-muted-foreground" />
                <h3 className="mt-2 text-lg font-medium">No submissions found</h3>
                <p className="text-sm text-muted-foreground">
                  Try adjusting your filters or search term
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredSubmissions.map((submission) => (
                  <Card key={submission.id} className="overflow-hidden">
                    <div className="flex flex-col md:flex-row">
                      {/* Status indicator */}
                      <div className={`w-2 ${statusColors[(submission.rating_count && submission.rating_count > 0 && submission.status === 'submitted' ? 'under_review' : submission.status) as keyof typeof statusColors].bg}`} />

                      <CardContent className="p-4 flex-1">
                        <div className="flex flex-col md:flex-row justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center">
                              <h3 className="font-medium">{submission.faculty_name}</h3>
                              <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">{submission.unit_name}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <StatusBadge status={submission.rating_count && submission.rating_count > 0 && submission.status === 'submitted' ? 'under_review' : submission.status} />
                              <span className="text-xs text-muted-foreground">
                                {submission.report_type} {submission.report_year}
                              </span>
                            </div>
                            <div className="pt-1">
                              <StatusFlowChart
                                status={submission.rating_count && submission.rating_count > 0 && submission.status === 'submitted' ? 'under_review' : submission.status}
                                ratingCount={submission.rating_count}
                                submittedRatingCount={submission.submitted_rating_count}
                                inProgressRatingCount={submission.in_progress_rating_count}
                              />
                            </div>
                          </div>

                          <div className="mt-2 md:mt-0 flex flex-col items-end justify-between">
                            <div className="text-xs text-right space-y-1">
                              <div>Created: {formatDate(submission.create_dt)}</div>
                              <div>Updated: {formatDate(submission.update_dt)}</div>
                              {submission.submit_dt && (
                                <div>Submitted: {formatDate(submission.submit_dt)}</div>
                              )}
                            </div>

                            <div className="flex space-x-2 mt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => router.push(`/dashboard/merit-review/submission/v2/admin?id=${submission.id}&faculty=${encodeURIComponent(submission.faculty_name)}`)}
                              >
                                <FileText className="h-4 w-4 mr-2" />
                                View Report
                              </Button>

                              {submission.rating_count && submission.rating_count > 0 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => fetchRatings(submission)}
                                >
                                  <Star className="h-4 w-4 mr-2" />
                                  Review Details
                                </Button>
                              )}

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleGeneratePDF(submission)}
                                disabled={generatingPDF}
                              >
                                <Download className="h-4 w-4 mr-2" />
                                Generate PDF
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Render Ratings Dialog */}
      <RatingsDialog
        open={showRatingsDialog}
        onOpenChange={setShowRatingsDialog}
        submission={selectedSubmission}
        ratings={ratings}
        loading={loadingRatings}
        formatDate={formatDate}
      />
    </div>
  );
}