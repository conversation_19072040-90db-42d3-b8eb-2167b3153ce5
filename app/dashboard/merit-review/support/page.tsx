"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FileText, CheckCircle2, AlertCircle, Clock, Download } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Submission {
  id: number;
  faculty_id: number;
  faculty_name: string;
  unit_id: number;
  unit_name: string;
  report_doc: string;
  create_dt: string;
  update_dt: string;
  status: string;
  rating_count: number;
}

interface Report {
  id: number;
  unit_id: number;
  unit_name: string;
  report_type: string;
  created_at: string;
  status: string;
}

export default function SupportPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [reports, setReports] = useState<Report[]>([]);
  const [selectedReportType, setSelectedReportType] = useState<string>("summary");

  // Redirect if not authenticated
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
    }
  }, [authStatus, router]);

  // Fetch submissions and reports
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [submissionsResponse, reportsResponse] = await Promise.all([
          fetch("/api/merit-review/support/submissions"),
          fetch("/api/merit-review/support/reports"),
        ]);

        if (!submissionsResponse.ok || !reportsResponse.ok) {
          throw new Error("Failed to fetch data");
        }

        const [submissionsData, reportsData] = await Promise.all([
          submissionsResponse.json(),
          reportsResponse.json(),
        ]);

        setSubmissions(submissionsData);
        setReports(reportsData);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to fetch data");
      } finally {
        setIsLoading(false);
      }
    };

    if (authStatus === "authenticated") {
      fetchData();
    }
  }, [authStatus]);

  const handleGenerateReport = async () => {
    try {
      const response = await fetch("/api/merit-review/support/reports", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          report_type: selectedReportType,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate report");
      }

      const newReport = await response.json();
      setReports([...reports, newReport]);

      toast.success("Report generated successfully");
    } catch (error) {
      console.error("Error generating report:", error);
      toast.error("Failed to generate report");
    }
  };

  const handleDownloadReport = async (reportId: number) => {
    try {
      const response = await fetch(`/api/merit-review/support/reports/${reportId}/download`);
      
      if (!response.ok) {
        throw new Error("Failed to download report");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `merit-review-report-${reportId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Error downloading report:", error);
      toast.error("Failed to download report");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "submitted":
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case "in_progress":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case "needs_revision":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "submitted":
        return "Submitted";
      case "in_progress":
        return "In Progress";
      case "needs_revision":
        return "Needs Revision";
      default:
        return "Not Started";
    }
  };

  if (authStatus === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Department Support Dashboard</h1>
        <Button onClick={() => router.push("/dashboard/merit-review")}>
          Back to Merit Review
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Submissions Overview</CardTitle>
            <CardDescription>
              Monitor and track faculty submissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Faculty</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Ratings Received</TableHead>
                    <TableHead>Submission Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {submissions.map((submission) => (
                    <TableRow key={submission.id}>
                      <TableCell>{submission.faculty_name}</TableCell>
                      <TableCell>{submission.unit_name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(submission.status)}
                          {getStatusText(submission.status)}
                        </div>
                      </TableCell>
                      <TableCell>{submission.rating_count} / 3</TableCell>
                      <TableCell>
                        {new Date(submission.create_dt).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))}
                  {submissions.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        No submissions found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Report Generation</CardTitle>
            <CardDescription>
              Generate and download merit review reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-6">
              <Select
                value={selectedReportType}
                onValueChange={setSelectedReportType}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">Summary Report</SelectItem>
                  <SelectItem value="status">Status Report</SelectItem>
                  <SelectItem value="ratings">Ratings Report</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleGenerateReport}>
                Generate Report
              </Button>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Report Type</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Generated On</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell className="capitalize">
                      {report.report_type.replace("_", " ")}
                    </TableCell>
                    <TableCell>{report.unit_name}</TableCell>
                    <TableCell>
                      {new Date(report.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(report.status)}
                        {getStatusText(report.status)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadReport(report.id)}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {reports.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      No reports generated yet
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 