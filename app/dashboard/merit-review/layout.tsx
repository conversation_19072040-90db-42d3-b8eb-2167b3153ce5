"use client";

import { Suspense, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import Breadcrumbs from "@/app/ui/common/breadcrumbs";

export default function MeritReviewLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  // State to track if user is eligible for merit review
  const [isEligible, setIsEligible] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  // Check if user is eligible for merit review (system_admin, admin roles, Regular Faculty, or committee member)
  useEffect(() => {
    const checkEligibility = async () => {
      if (status === "unauthenticated") {
        router.push("/login");
        return;
      }

      if (status === "authenticated") {
        // System admins, faculty_admin, and department_admin are always eligible
        if (session?.user?.roles?.includes('system_admin') ||
            session?.user?.roles?.includes('faculty_admin') ||
            session?.user?.roles?.includes('department_admin') ||
            session?.user?.roles?.includes('department_approver') ||
            session?.user?.roles?.includes('faculty_approver')) {
          setIsEligible(true);
          setIsChecking(false);
          return;
        }

        // Check if user is a committee member
        try {
          const committeeResponse = await fetch('/api/merit-review/committee/check-membership');
          if (committeeResponse.ok) {
            const data = await committeeResponse.json();
            if (data.isMember) {
              setIsEligible(true);
              setIsChecking(false);
              return;
            }
          }
        } catch (error) {
          console.error('Error checking committee membership:', error);
          // Continue with other checks
        }

        // For others, check if they are Regular Faculty
        try {
          const response = await fetch('/api/profile/faculty');
          if (response.ok) {
            const data = await response.json();
            if (data?.job_family === 'Regular Faculty') {
              setIsEligible(true);
            } else {
              // Not eligible, redirect to dashboard
              router.push('/dashboard');
            }
          } else {
            // Error fetching faculty data, redirect to dashboard
            router.push('/dashboard');
          }
        } catch (error) {
          console.error('Error checking faculty status:', error);
          router.push('/dashboard');
        } finally {
          setIsChecking(false);
        }
      }
    };

    checkEligibility();
  }, [status, session, router]);

  // Generate breadcrumbs based on the current path
  const getBreadcrumbs = () => {
    // Check if pathname is defined
    if (!pathname) {
      return [{ label: "Merit Review", href: "/dashboard/merit-review", active: true }];
    }

    const paths = pathname.split("/").filter(Boolean);
    const searchParams = new URLSearchParams(window.location.search);

    // For Merit Review and Activity, don't include "Dashboard" in breadcrumb
    const breadcrumbs = [
      { label: "Merit Review", href: "/dashboard/merit-review/overview", active: false },
    ];

    // Add additional breadcrumbs for subpaths
    if (paths.length > 2) {
      const subpath = paths[2];

      if (subpath === "configuration") {
        breadcrumbs.push({
          label: "Configuration",
          href: "/dashboard/merit-review/configuration",
          active: true,
        });
      } else if (subpath === "committee") {
        breadcrumbs.push({
          label: "Committee",
          href: "/dashboard/merit-review/committee",
          active: true,
        });
      } else if (subpath === "submission") {
        // Check if this is the admin view of a faculty submission
        if (paths.length > 3 && paths[3] === "v2" && paths[4] === "admin") {
          const facultyName = searchParams.get('faculty') || 'Faculty Member';

          breadcrumbs.push({
            label: "Submissions",
            href: "/dashboard/merit-review/submissions",
            active: false,
          });

          breadcrumbs.push({
            label: facultyName,
            href: `${pathname}?${searchParams.toString()}`,
            active: true,
          });
        } else {
          breadcrumbs.push({
            label: "My Submission",
            href: "/dashboard/merit-review/submission",
            active: true,
          });
        }
      } else if (subpath === "submissions") {
        breadcrumbs.push({
          label: "Submissions",
          href: "/dashboard/merit-review/submissions",
          active: true,
        });
      } else if (subpath === "review") {
        breadcrumbs.push({
          label: "Review",
          href: "/dashboard/merit-review/review",
          active: true,
        });
      } else if (subpath === "course-evaluations") {
        breadcrumbs.push({
          label: "Course Evaluations",
          href: "/dashboard/merit-review/course-evaluations",
          active: true,
        });
      } else if (subpath === "committee") {
        if (paths.length > 3 && paths[3] === "ratings") {
          breadcrumbs.push({
            label: "Committee Ratings",
            href: "/dashboard/merit-review/committee/ratings",
            active: true,
          });
        } else {
          breadcrumbs.push({
            label: "Committee",
            href: "/dashboard/merit-review?tab=committee",
            active: true,
          });
        }
      } else if (subpath === "preliminary-ratings") {
        breadcrumbs.push({
          label: "Preliminary Ratings",
          href: "/dashboard/merit-review/preliminary-ratings",
          active: true,
        });
      }
    } else {
      // This is the main merit review page (overview)
      breadcrumbs.push({
        label: "Overview",
        href: "/dashboard/merit-review/overview",
        active: true,
      });
    }

    // Ensure we always return a valid array
    return Array.isArray(breadcrumbs) ? breadcrumbs : [];
  };

  if (status === "loading" || isChecking) {
    return <div>Loading...</div>;
  }

  // If not eligible, show an access denied message (this should rarely be seen due to the redirect)
  if (!isEligible) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-bold mb-4">Access Denied</h2>
            <p>You do not have permission to access the Merit Review section.</p>
            <p>Only Regular Faculty members, Faculty Administrators, Department Administrators, and System Administrators can participate in the Merit Review process.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <Breadcrumbs breadcrumbs={getBreadcrumbs()} />
      <Suspense fallback={<div>Loading...</div>}>
        <Card>
          <CardContent className="p-6">{children}</CardContent>
        </Card>
      </Suspense>
    </div>
  );
}
