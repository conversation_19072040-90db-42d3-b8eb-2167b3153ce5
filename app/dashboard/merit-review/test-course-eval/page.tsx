'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatTerm } from '@/app/lib/utils/term';

export default function TestCourseEvalPage() {
  const [courseEvaluations, setCourseEvaluations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCourseEvaluations = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/merit-review/faculty-course-evaluations');
      if (response.ok) {
        const data = await response.json();
        setCourseEvaluations(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch course evaluations');
      }
    } catch (error) {
      console.error('Error fetching course evaluations:', error);
      setError('An error occurred while fetching course evaluations');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Test Course Evaluations</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Course Evaluations</CardTitle>
          <CardDescription>
            Test fetching course evaluations data for the current faculty user
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={fetchCourseEvaluations}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Fetch Course Evaluations'}
          </Button>

          {error && (
            <div className="p-4 bg-red-50 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {courseEvaluations.length > 0 && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">Results ({courseEvaluations.length} courses)</h3>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Term ID</TableHead>
                      <TableHead>Term Year</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>SCP Q1-3</TableHead>
                      <TableHead>SCP Q4-6</TableHead>
                      <TableHead>Students</TableHead>
                      <TableHead>Response %</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {courseEvaluations.map((course, index) => (
                      <TableRow key={index}>
                        <TableCell>{course.term_id}</TableCell>
                        <TableCell>{course.term_year}</TableCell>
                        <TableCell>{course.course_id}</TableCell>
                        <TableCell>{course.course_title}</TableCell>
                        <TableCell>{course.scp_q1_q3}</TableCell>
                        <TableCell>{course.scp_q4_q6}</TableCell>
                        <TableCell>{course.class_size}</TableCell>
                        <TableCell>{course.response_percentage}%</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          {courseEvaluations.length > 0 && (
            <div className="mt-8">
              <h3 className="text-lg font-medium mb-2">Raw Data (First Course)</h3>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-xs">
                {JSON.stringify(courseEvaluations[0], null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Term Formatting Test</CardTitle>
          <CardDescription>
            Test the term formatting utility function
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Term ID</TableHead>
                <TableHead>Formatted Term</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[1231, 1235, 1239, 939, 1011, 1271].map((termId) => (
                <TableRow key={termId}>
                  <TableCell>{termId}</TableCell>
                  <TableCell>{formatTerm(termId)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
