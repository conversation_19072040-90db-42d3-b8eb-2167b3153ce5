'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '../../../../components/ui/separator';
import {
  PencilIcon,
  CheckCircleIcon,
  EyeIcon,
  FileText,
  Download
} from 'lucide-react';
import { toast } from 'sonner';
import { downloadMeritReviewPDF } from '@/app/lib/utils/pdf-generator';

export default function SubmissionTab() {
  const { status } = useSession();
  const [submissions, setSubmissions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [generatingPDF, setGeneratingPDF] = useState(false);

  // Add loading indicator when fetching submissions
  const LoadingIndicator = () => (
    <div className="flex items-center justify-center py-4">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
    </div>
  );

  // Generate PDF for a submission
  const handleGeneratePDF = async (submission: any) => {
    try {
      setGeneratingPDF(true);

      // Fetch the full report data
      const response = await fetch(`/api/merit-review/report/admin?id=${submission.id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch report data');
      }

      const reportData = await response.json();

      // Prepare data for PDF generation
      const pdfData = {
        // Basic information
        faculty_name: reportData.faculty_name || 'Faculty Member',
        department: reportData.unit_name || '',
        unit_name: reportData.unit_name || '',
        rank: reportData.faculty_rank || '',
        administrative_role: reportData.administrative_role || '',
        research_chair: reportData.research_chair || '',
        report_type: submission.report_type,
        report_year: submission.report_year,
        status: submission.status,

        // Include all sections from the report
        teaching: reportData.teaching || {},
        teaching_courses: reportData.teaching?.courses || [],
        teaching_development_activities: reportData.teaching?.development_activities || [],
        student_supervision: reportData.student_supervision || {},
        undergrad_supervision: reportData.teaching?.undergrad_supervision || [],
        grad_supervision_completed: reportData.teaching?.grad_supervision_completed || [],
        grad_supervision_in_progress: reportData.teaching?.grad_supervision_in_progress || [],
        course_masters_supervision: reportData.teaching?.course_masters_supervision || [],
        postdoc_supervision: reportData.teaching?.postdoc_supervision || [],

        // Research section
        research: reportData.research || {},
        research_publications: reportData.research?.publications || [],
        research_grants: reportData.research?.grants || [],

        // Service section
        service: reportData.service || {},
        service_activities: reportData.service?.activities || [],
        professional_registrations: reportData.service?.registrations || [],

        // Awards and comments
        awards: reportData.awards || [],
        additionalComments: reportData.additionalComments || {},
      };

      // Generate and download the PDF
      downloadMeritReviewPDF(pdfData, `merit-review-${submission.report_type}-${submission.report_year}.pdf`);

      toast.success('PDF generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF');
    } finally {
      setGeneratingPDF(false);
    }
  };

  useEffect(() => {
    const fetchSubmissions = async () => {
      if (status === "authenticated") {
        try {
          setIsLoading(true);
          const response = await fetch("/api/merit-review/report/v2");
          if (response.ok) {
            const data = await response.json();
            setSubmissions(Array.isArray(data) ? data : []);
          } else {
            console.error("Failed to fetch submissions");
          }
        } catch (error) {
          console.error("Error fetching submissions:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchSubmissions();
  }, [status]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>My Merit Report</CardTitle>
        <CardDescription>
          Submit and track your merit review report
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <LoadingIndicator />
        ) : (
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-medium">Current Submission</h3>
              <p className="text-sm text-muted-foreground">
                {submissions.length > 0
                  ? "Access your current merit report submission"
                  : "Create or access your merit report submission"}
              </p>
              <div className="flex space-x-4">
                <Button asChild>
                  <Link href="/dashboard/merit-review/submission/v2">
                    {submissions.length > 0 ? "Go to My Submission" : "Create or View Submission"}
                  </Link>
                </Button>
              </div>
            </div>

            <Separator />

            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-medium">Submission Timeline</h3>
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                <div className="relative flex items-center mb-6">
                  <div className="z-10 flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 border border-white">
                    <PencilIcon className="w-4 h-4" />
                  </div>
                  <div className="flex-1 ml-4">
                    <h3 className="text-sm font-medium">Create & Edit</h3>
                    <p className="text-xs text-gray-500">Start by creating your merit report. You can save it as a draft and return to edit it later.</p>
                  </div>
                </div>

                <div className="relative flex items-center mb-6">
                  <div className="z-10 flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 border border-white">
                    <CheckCircleIcon className="w-4 h-4" />
                  </div>
                  <div className="flex-1 ml-4">
                    <h3 className="text-sm font-medium">Submit</h3>
                    <p className="text-xs text-gray-500">Once your report is complete, submit it for review. After submission, you cannot make further edits.</p>
                  </div>
                </div>

                <div className="relative flex items-center mb-6">
                  <div className="z-10 flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 text-purple-600 border border-white">
                    <EyeIcon className="w-4 w-4" />
                  </div>
                  <div className="flex-1 ml-4">
                    <h3 className="text-sm font-medium">Review Process</h3>
                    <p className="text-xs text-gray-500">Your report will be reviewed by the merit review committee. This process may take several weeks.</p>
                  </div>
                </div>

                <div className="relative flex items-center">
                  <div className="z-10 flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 text-indigo-600 border border-white">
                    <FileText className="w-4 h-4" />
                  </div>
                  <div className="flex-1 ml-4">
                    <h3 className="text-sm font-medium">Feedback</h3>
                    <p className="text-xs text-gray-500">Once reviewed, you'll be able to see feedback and ratings from the committee.</p>
                  </div>
                </div>
              </div>
            </div>

            {submissions.length > 0 && (
              <>
                <Separator />

                <div className="flex flex-col space-y-2">
                  <h3 className="text-lg font-medium">My Reports</h3>
                  <div className="space-y-2">
                    {submissions.map((submission) => (
                      <Card key={submission.id}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-center">
                            <div>
                              <h4 className="font-medium">{submission.report_type} {submission.report_year}</h4>
                              <p className="text-sm text-muted-foreground">
                                Status: {submission.status} | Last Updated: {new Date(submission.update_dt).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="flex space-x-2">
                              <Button asChild size="sm">
                                <Link href={`/dashboard/merit-review/submission/v2?id=${submission.id}`}>
                                  View Report
                                </Link>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleGeneratePDF(submission)}
                                disabled={generatingPDF}
                              >
                                <Download className="h-4 w-4 mr-2" />
                                Generate PDF
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
