"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { Loader2, FileText, CheckCircle2, Clock, AlertCircle } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface Submission {
  id: number;
  faculty_id: number;
  faculty_name: string;
  unit_id: number;
  unit_name: string;
  create_dt: string;
  update_dt: string;
  submit_dt?: string;
  status: string;
  rating_status: string;
  rating_id?: number;
  teaching_rating?: number;
  research_rating?: number;
  service_rating?: number;
  comments?: string;
  rating_created_at?: string;
  rating_updated_at?: string;
  is_submitted?: boolean;
  deadline?: string;
}

interface RatingForm {
  teaching: number | string;
  research: number | string;
  service: number | string;
  comments: string;
  is_submitted: boolean;
}

export default function CommitteeRatingsPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  // Initialize with default values to avoid null/undefined issues
  const [ratings, setRatings] = useState<RatingForm>({
    teaching: 0,
    research: 0,
    service: 0,
    comments: "",
    is_submitted: false
  });
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");

  // Redirect if not authenticated
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
    }
  }, [authStatus, router]);

  // Fetch submissions for rating
  useEffect(() => {
    if (authStatus === "authenticated") {
      fetchSubmissions();
    }
  }, [authStatus]);

  const fetchSubmissions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/merit-review/committee/ratings/submissions");

      if (!response.ok) {
        throw new Error("Failed to fetch submissions");
      }

      const data = await response.json();
      setSubmissions(data);
    } catch (error) {
      console.error("Error fetching submissions:", error);
      toast.error("Failed to fetch submissions for rating");
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return format(new Date(dateString), "MMM d, yyyy");
  };

  const getRatingStatusBadge = (status: string) => {
    switch (status) {
      case "not_started":
        return <Badge variant="outline" className="bg-gray-100">Not Started</Badge>;
      case "in_progress":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
      case "submitted":
        return <Badge variant="outline" className="bg-green-100 text-green-800">Submitted</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const handleSelectSubmission = (submission: Submission) => {
    setSelectedSubmission(submission);

    // Initialize rating form with existing values or defaults
    // Ensure all values are numbers (not null or undefined)
    setRatings({
      teaching: submission.teaching_rating !== undefined && submission.teaching_rating !== null
        ? submission.teaching_rating
        : 0,
      research: submission.research_rating !== undefined && submission.research_rating !== null
        ? submission.research_rating
        : 0,
      service: submission.service_rating !== undefined && submission.service_rating !== null
        ? submission.service_rating
        : 0,
      comments: submission.comments || "",
      is_submitted: false
    });
  };

  const handleRatingChange = (category: keyof RatingForm, value: string | boolean) => {
    if (category === 'teaching' || category === 'research' || category === 'service') {
      // For ratings, we're now using text input with validation in the onChange handler
      // Just set the value directly - it's already validated
      setRatings(prev => ({
        ...prev,
        [category]: value
      }));
    } else {
      // For non-numeric values (comments, is_submitted)
      setRatings(prev => ({
        ...prev,
        [category]: value
      }));
    }
  };

  const handleSaveRating = async () => {
    if (!selectedSubmission) return;

    try {
      setIsSaving(true);

      // Convert any empty string ratings to 0 for the API call
      const teachingRating = ratings.teaching === '' ? 0 : Number(ratings.teaching);
      const researchRating = ratings.research === '' ? 0 : Number(ratings.research);
      const serviceRating = ratings.service === '' ? 0 : Number(ratings.service);

      console.log("Saving ratings:", {
        teaching: teachingRating,
        research: researchRating,
        service: serviceRating,
        comments: ratings.comments
      });

      const response = await fetch("/api/merit-review/committee/ratings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          report_id: selectedSubmission.id,
          teaching_rating: teachingRating,
          research_rating: researchRating,
          service_rating: serviceRating,
          comments: ratings.comments,
          is_submitted: false
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        console.error("Error response:", error);
        throw new Error(error.error || "Failed to save rating");
      }

      toast.success("Rating saved successfully");
      fetchSubmissions(); // Refresh the list
      setSelectedSubmission(null); // Clear selection
    } catch (error) {
      console.error("Error saving rating:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save rating");
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmitRating = async () => {
    if (!selectedSubmission) return;

    // Confirm before submitting
    if (!confirm("Are you sure you want to submit this rating? You won't be able to change it after submission.")) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Convert any empty string ratings to 0 for the API call
      const teachingRating = ratings.teaching === '' ? 0 : Number(ratings.teaching);
      const researchRating = ratings.research === '' ? 0 : Number(ratings.research);
      const serviceRating = ratings.service === '' ? 0 : Number(ratings.service);

      console.log("Submitting ratings:", {
        teaching: teachingRating,
        research: researchRating,
        service: serviceRating,
        comments: ratings.comments
      });

      const response = await fetch("/api/merit-review/committee/ratings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          report_id: selectedSubmission.id,
          teaching_rating: teachingRating,
          research_rating: researchRating,
          service_rating: serviceRating,
          comments: ratings.comments,
          is_submitted: true
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        console.error("Error response:", error);
        throw new Error(error.error || "Failed to submit rating");
      }

      toast.success("Rating submitted successfully");
      fetchSubmissions(); // Refresh the list
      setSelectedSubmission(null); // Clear selection
    } catch (error) {
      console.error("Error submitting rating:", error);
      toast.error(error instanceof Error ? error.message : "Failed to submit rating");
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredSubmissions = submissions.filter(submission => {
    if (activeTab === "all") return true;
    if (activeTab === "not_started") return submission.rating_status === "not_started";
    if (activeTab === "in_progress") return submission.rating_status === "in_progress";
    if (activeTab === "submitted") return submission.rating_status === "submitted";
    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Merit Review Committee Ratings</h1>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Submissions for Rating</CardTitle>
                <CardDescription>
                  Review and rate faculty submissions from your department
                </CardDescription>
                <Tabs defaultValue="all" className="mt-2" onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-4">
                    <TabsTrigger value="all">All</TabsTrigger>
                    <TabsTrigger value="not_started">Not Started</TabsTrigger>
                    <TabsTrigger value="in_progress">In Progress</TabsTrigger>
                    <TabsTrigger value="submitted">Submitted</TabsTrigger>
                  </TabsList>
                </Tabs>
              </CardHeader>
              <CardContent>
                {filteredSubmissions.length === 0 ? (
                  <div className="text-center py-6 text-gray-500">
                    No submissions found for this filter
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Faculty</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Submitted</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSubmissions.map((submission) => (
                        <TableRow key={submission.id}>
                          <TableCell>{submission.faculty_name}</TableCell>
                          <TableCell>{getRatingStatusBadge(submission.rating_status)}</TableCell>
                          <TableCell>{formatDate(submission.submit_dt)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // First, navigate back to the committee ratings page to avoid redirect issues
                                  router.push(`/dashboard/merit-review/committee/ratings`);
                                  // Then, after a short delay, navigate to the admin view page
                                  setTimeout(() => {
                                    router.push(`/dashboard/merit-review/submission/v2/admin?id=${submission.id}&faculty=${encodeURIComponent(submission.faculty_name)}`);
                                  }, 100);
                                }}
                              >
                                <FileText className="h-4 w-4 mr-1" />
                                View
                              </Button>
                              {submission.rating_status !== 'submitted' && (
                                <Button
                                  size="sm"
                                  onClick={() => handleSelectSubmission(submission)}
                                >
                                  {submission.rating_status === 'not_started' ? 'Rate' : 'Edit'}
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>

          {selectedSubmission && (
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>
                    {selectedSubmission.rating_status === 'not_started'
                      ? 'Create Rating'
                      : 'Edit Rating'}
                  </CardTitle>
                  <CardDescription>
                    Rate {selectedSubmission.faculty_name}'s submission
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="teaching-rating" className="text-sm font-medium">Teaching Rating (0-2, decimal allowed)</label>
                      <div className="flex items-center">
                        <Input
                          id="teaching-rating"
                          type="text"
                          inputMode="decimal"
                          value={ratings.teaching || ""}
                          onChange={(e) => {
                            // Allow numbers 0-2 with optional single decimal place
                            const value = e.target.value;
                            // Allow empty string, single digits 0-2, or numbers with one decimal place
                            if (
                              value === "" ||
                              /^[0-2]$/.test(value) ||
                              /^[0-2]\.\d?$/.test(value)
                            ) {
                              handleRatingChange("teaching", value);
                            }
                          }}
                          placeholder="Enter rating (0-2, one decimal allowed)"
                        />
                        <div className="ml-2 text-xs text-gray-500">
                          Examples: 0 (Unsatisfactory), 0.5, 1 (Satisfactory), 1.5, 2 (Excellent)
                        </div>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="research-rating" className="text-sm font-medium">Research Rating (0-2, decimal allowed)</label>
                      <div className="flex items-center">
                        <Input
                          id="research-rating"
                          type="text"
                          inputMode="decimal"
                          value={ratings.research || ""}
                          onChange={(e) => {
                            // Allow numbers 0-2 with optional single decimal place
                            const value = e.target.value;
                            // Allow empty string, single digits 0-2, or numbers with one decimal place
                            if (
                              value === "" ||
                              /^[0-2]$/.test(value) ||
                              /^[0-2]\.\d?$/.test(value)
                            ) {
                              handleRatingChange("research", value);
                            }
                          }}
                          placeholder="Enter rating (0-2, one decimal allowed)"
                        />
                        <div className="ml-2 text-xs text-gray-500">
                          Examples: 0 (Unsatisfactory), 0.5, 1 (Satisfactory), 1.5, 2 (Excellent)
                        </div>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="service-rating" className="text-sm font-medium">Service Rating (0-2, decimal allowed)</label>
                      <div className="flex items-center">
                        <Input
                          id="service-rating"
                          type="text"
                          inputMode="decimal"
                          value={ratings.service || ""}
                          onChange={(e) => {
                            // Allow numbers 0-2 with optional single decimal place
                            const value = e.target.value;
                            // Allow empty string, single digits 0-2, or numbers with one decimal place
                            if (
                              value === "" ||
                              /^[0-2]$/.test(value) ||
                              /^[0-2]\.\d?$/.test(value)
                            ) {
                              handleRatingChange("service", value);
                            }
                          }}
                          placeholder="Enter rating (0-2, one decimal allowed)"
                        />
                        <div className="ml-2 text-xs text-gray-500">
                          Examples: 0 (Unsatisfactory), 0.5, 1 (Satisfactory), 1.5, 2 (Excellent)
                        </div>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="comments" className="text-sm font-medium">Comments</label>
                      <Textarea
                        id="comments"
                        value={ratings.comments}
                        onChange={(e) => handleRatingChange("comments", e.target.value)}
                        placeholder="Enter your comments here..."
                        rows={4}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => setSelectedSubmission(null)}
                  >
                    Cancel
                  </Button>
                  <div className="space-x-2">
                    <Button
                      variant="outline"
                      onClick={handleSaveRating}
                      disabled={isSaving}
                    >
                      {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save
                    </Button>
                    <Button
                      onClick={handleSubmitRating}
                      disabled={isSubmitting}
                    >
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Submit
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
