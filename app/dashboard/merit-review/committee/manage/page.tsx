"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Plus, Trash2, AlertTriangle } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

interface CommitteeMember {
  id: number;
  faculty_id: number;
  faculty_name: string;
  role: string;
  created_at: string;
}

interface FacultyMember {
  faculty_id: number;
  full_name: string;
  email: string;
}

interface ConflictOfInterest {
  id: number;
  faculty_id: number;
  faculty_name: string;
  reason: string;
  created_at: string;
}

export default function CommitteeManagementPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const params = useParams();
  const workflowId = params.id as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [committeeMembers, setCommitteeMembers] = useState<CommitteeMember[]>([]);
  const [facultyMembers, setFacultyMembers] = useState<FacultyMember[]>([]);
  const [conflicts, setConflicts] = useState<ConflictOfInterest[]>([]);
  const [selectedFaculty, setSelectedFaculty] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [conflictReason, setConflictReason] = useState("");
  const [selectedConflict, setSelectedConflict] = useState<number | null>(null);
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [isAddingConflict, setIsAddingConflict] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated or not authorized
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
    } else if (authStatus === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
      }
    }
  }, [authStatus, session, router]);

  // Fetch committee members
  const fetchCommitteeMembers = async () => {
    try {
      const response = await fetch(`/api/merit-review/committee?workflow_id=${workflowId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch committee members");
      }
      const data = await response.json();
      setCommitteeMembers(data);
    } catch (error) {
      console.error("Error fetching committee members:", error);
      toast.error("Failed to fetch committee members");
    }
  };

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      if (authStatus !== "authenticated") return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch committee members
        await fetchCommitteeMembers();

        // Fetch faculty members
        const facultyResponse = await fetch("/api/faculty");
        if (!facultyResponse.ok) {
          throw new Error("Failed to fetch faculty members");
        }
        const facultyData = await facultyResponse.json();
        setFacultyMembers(facultyData);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to fetch data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [authStatus, workflowId]);

  const handleAddMember = async () => {
    if (!selectedFaculty || !selectedRole) {
      toast.error("Please select both faculty member and role");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/merit-review/committee", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          workflow_id: workflowId,
          faculty_id: selectedFaculty,
          role: selectedRole,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to add committee member");
      }

      // Refresh the list
      await fetchCommitteeMembers();
      
      // Reset form
      setSelectedFaculty("");
      setSelectedRole("");
      
      toast.success("Committee member added successfully");
    } catch (error) {
      console.error("Error adding member:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add committee member");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveMember = async (memberId: number) => {
    try {
      const response = await fetch(`/api/merit-review/committee/${memberId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to remove committee member");
      }

      // Refresh the list
      await fetchCommitteeMembers();
      toast.success("Committee member removed successfully");
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error(error instanceof Error ? error.message : "Failed to remove committee member");
    }
  };

  const handleAddConflict = async () => {
    if (!selectedConflict || !conflictReason.trim()) {
      toast.error("Please select a faculty member and provide a reason");
      return;
    }

    setIsAddingConflict(true);

    try {
      const response = await fetch("/api/merit-review/conflicts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          faculty_id: selectedConflict,
          reason: conflictReason.trim(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to add conflict of interest");
      }

      // Reset form
      setSelectedConflict(null);
      setConflictReason("");

      // Refresh conflicts
      const conflictsResponse = await fetch("/api/merit-review/conflicts");
      if (conflictsResponse.ok) {
        const conflictsData = await conflictsResponse.json();
        setConflicts(conflictsData);
      }

      toast.success("Conflict of interest added successfully");
    } catch (error) {
      console.error("Error adding conflict:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add conflict of interest");
    } finally {
      setIsAddingConflict(false);
    }
  };

  const handleRemoveConflict = async (id: number) => {
    try {
      const response = await fetch(`/api/merit-review/conflicts/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to remove conflict of interest");
      }

      // Refresh conflicts
      const conflictsResponse = await fetch("/api/merit-review/conflicts");
      if (conflictsResponse.ok) {
        const conflictsData = await conflictsResponse.json();
        setConflicts(conflictsData);
      }

      toast.success("Conflict of interest removed successfully");
    } catch (error) {
      console.error("Error removing conflict:", error);
      toast.error(error instanceof Error ? error.message : "Failed to remove conflict of interest");
    }
  };

  if (authStatus === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Committee Management</h1>
        <Button onClick={() => router.push("/dashboard/merit-review")}>
          Back to Merit Review
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Add Committee Member</CardTitle>
          <CardDescription>
            Select a faculty member to add to the committee
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Select
              value={selectedFaculty}
              onValueChange={setSelectedFaculty}
            >
              <SelectTrigger className="w-[300px]">
                <SelectValue placeholder="Select a faculty member" />
              </SelectTrigger>
              <SelectContent>
                {facultyMembers.map((faculty) => (
                  <SelectItem
                    key={faculty.faculty_id}
                    value={faculty.faculty_id.toString()}
                  >
                    {faculty.full_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              onClick={handleAddMember}
              disabled={!selectedFaculty || isAddingMember}
            >
              {isAddingMember ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Member
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Committee Members</CardTitle>
          <CardDescription>
            List of current committee members and their conflicts of interest
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Faculty Member</TableHead>
                  <TableHead>Conflicts of Interest</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {committeeMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>{member.faculty_name}</TableCell>
                    <TableCell>
                      <div className="space-y-2">
                        {conflicts
                          .filter((conflict) => conflict.faculty_id === member.faculty_id)
                          .map((conflict) => (
                            <div
                              key={conflict.id}
                              className="flex items-center gap-2 text-sm"
                            >
                              <AlertTriangle className="h-4 w-4 text-yellow-500" />
                              <span>
                                {facultyMembers.find(
                                  (f) => f.faculty_id === conflict.faculty_id
                                )?.full_name || "Unknown"}
                                : {conflict.reason}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveConflict(conflict.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedConflict(member.id)}
                            >
                              Add Conflict
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Add Conflict of Interest</DialogTitle>
                              <DialogDescription>
                                Select a faculty member and provide a reason for the
                                conflict of interest
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <Label>Faculty Member</Label>
                                <Select
                                  value={selectedFaculty}
                                  onValueChange={setSelectedFaculty}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a faculty member" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {facultyMembers.map((faculty) => (
                                      <SelectItem
                                        key={faculty.faculty_id}
                                        value={faculty.faculty_id.toString()}
                                      >
                                        {faculty.full_name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="space-y-2">
                                <Label>Reason</Label>
                                <Textarea
                                  value={conflictReason}
                                  onChange={(e) => setConflictReason(e.target.value)}
                                  placeholder="Enter the reason for the conflict of interest"
                                />
                              </div>
                            </div>
                            <DialogFooter>
                              <Button
                                onClick={handleAddConflict}
                                disabled={!selectedFaculty || !conflictReason || isAddingConflict}
                              >
                                {isAddingConflict ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Adding...
                                  </>
                                ) : (
                                  "Add Conflict"
                                )}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveMember(member.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 