"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { PlusIcon, Trash2Icon, AlertCircleIcon } from "lucide-react";
import { toast } from "sonner";

export default function MeritReviewCommitteePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [committeeMembers, setCommitteeMembers] = useState<any[]>([]);
  const [facultyMembers, setFacultyMembers] = useState<any[]>([]);
  const [selectedFaculty, setSelectedFaculty] = useState<string>("");
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [conflictDialogOpen, setConflictDialogOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<any>(null);
  const [conflictReason, setConflictReason] = useState("");
  const [conflictFaculty, setConflictFaculty] = useState<string>("");
  const [conflicts, setConflicts] = useState<any[]>([]);

  // Redirect if not authenticated or not department_admin
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
      return;
    }

    if (status === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("department_admin") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
        return;
      }

      // Redirect to the dashboard with committee tab
      router.push("/dashboard/merit-review?tab=committee");
      return;
    }
  }, [status, session, router]);

  // Fetch committee members and faculty members
  useEffect(() => {
    const fetchData = async () => {
      try {
        // This would be replaced with actual API calls
        // const committeeResponse = await fetch("/api/merit-review/committee");
        // const committeeData = await committeeResponse.json();
        // setCommitteeMembers(committeeData);

        // const facultyResponse = await fetch("/api/faculty");
        // const facultyData = await facultyResponse.json();
        // setFacultyMembers(facultyData);

        // Mock data for now
        setCommitteeMembers([
          { id: 1, faculty_id: 101, name: "Dr. Jane Smith", department: "Electrical Engineering" },
          { id: 2, faculty_id: 102, name: "Dr. John Doe", department: "Mechanical Engineering" },
        ]);

        setFacultyMembers([
          { faculty_id: 101, name: "Dr. Jane Smith", department: "Electrical Engineering" },
          { faculty_id: 102, name: "Dr. John Doe", department: "Mechanical Engineering" },
          { faculty_id: 103, name: "Dr. Alice Johnson", department: "Civil Engineering" },
          { faculty_id: 104, name: "Dr. Bob Brown", department: "Chemical Engineering" },
        ]);

        setConflicts([
          { id: 1, committee_member_id: 101, faculty_id: 103, reason: "Research collaboration" },
        ]);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  const handleAddMember = async () => {
    if (!selectedFaculty) {
      toast.error("Please select a faculty member");
      return;
    }

    setIsAddingMember(true);

    try {
      // This would be replaced with an actual API call
      // const response = await fetch("/api/merit-review/committee", {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify({
      //     faculty_id: selectedFaculty,
      //   }),
      // });

      // if (!response.ok) {
      //   throw new Error("Failed to add committee member");
      // }

      // const newMember = await response.json();

      // Mock successful response
      const facultyMember = facultyMembers.find(f => f.faculty_id.toString() === selectedFaculty);
      const newMember = {
        id: Math.floor(Math.random() * 1000),
        faculty_id: facultyMember.faculty_id,
        name: facultyMember.name,
        department: facultyMember.department,
      };

      setCommitteeMembers([...committeeMembers, newMember]);
      setSelectedFaculty("");
      setIsDialogOpen(false);
      toast.success("Committee member added successfully");
    } catch (error) {
      console.error("Error adding committee member:", error);
      toast.error("Failed to add committee member");
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleRemoveMember = async (id: number) => {
    try {
      // This would be replaced with an actual API call
      // const response = await fetch(`/api/merit-review/committee/${id}`, {
      //   method: "DELETE",
      // });

      // if (!response.ok) {
      //   throw new Error("Failed to remove committee member");
      // }

      // Mock successful response
      setCommitteeMembers(committeeMembers.filter(member => member.id !== id));
      toast.success("Committee member removed successfully");
    } catch (error) {
      console.error("Error removing committee member:", error);
      toast.error("Failed to remove committee member");
    }
  };

  const handleAddConflict = async () => {
    if (!selectedMember || !conflictFaculty || !conflictReason) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      // This would be replaced with an actual API call
      // const response = await fetch("/api/merit-review/conflict", {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify({
      //     committee_member_id: selectedMember.faculty_id,
      //     faculty_id: conflictFaculty,
      //     reason: conflictReason,
      //   }),
      // });

      // if (!response.ok) {
      //   throw new Error("Failed to add conflict of interest");
      // }

      // const newConflict = await response.json();

      // Mock successful response
      const facultyMember = facultyMembers.find(f => f.faculty_id.toString() === conflictFaculty);
      const newConflict = {
        id: Math.floor(Math.random() * 1000),
        committee_member_id: selectedMember.faculty_id,
        faculty_id: parseInt(conflictFaculty),
        faculty_name: facultyMember.name,
        reason: conflictReason,
      };

      setConflicts([...conflicts, newConflict]);
      setConflictFaculty("");
      setConflictReason("");
      setConflictDialogOpen(false);
      toast.success("Conflict of interest added successfully");
    } catch (error) {
      console.error("Error adding conflict of interest:", error);
      toast.error("Failed to add conflict of interest");
    }
  };

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Merit Review Committee</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Committee Member
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Committee Member</DialogTitle>
              <DialogDescription>
                Select a faculty member to add to the merit review committee.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="faculty">Faculty Member</Label>
                <Select value={selectedFaculty} onValueChange={setSelectedFaculty}>
                  <SelectTrigger id="faculty">
                    <SelectValue placeholder="Select faculty member" />
                  </SelectTrigger>
                  <SelectContent>
                    {facultyMembers
                      .filter(faculty => !committeeMembers.some(member => member.faculty_id === faculty.faculty_id))
                      .map((faculty) => (
                        <SelectItem key={faculty.faculty_id} value={faculty.faculty_id.toString()}>
                          {faculty.name} - {faculty.department}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddMember} disabled={isAddingMember}>
                {isAddingMember ? "Adding..." : "Add Member"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Committee Members</CardTitle>
          <CardDescription>
            Current members of the merit review committee
          </CardDescription>
        </CardHeader>
        <CardContent>
          {committeeMembers.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No committee members added yet
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {committeeMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>{member.name}</TableCell>
                    <TableCell>{member.department}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedMember(member);
                            setConflictDialogOpen(true);
                          }}
                        >
                          <AlertCircleIcon className="h-4 w-4 mr-2" />
                          Add Conflict
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleRemoveMember(member.id)}
                        >
                          <Trash2Icon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Conflicts of Interest</CardTitle>
          <CardDescription>
            Declared conflicts of interest for committee members
          </CardDescription>
        </CardHeader>
        <CardContent>
          {conflicts.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No conflicts of interest declared
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Committee Member</TableHead>
                  <TableHead>Faculty Member</TableHead>
                  <TableHead>Reason</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {conflicts.map((conflict) => {
                  const committeeMember = facultyMembers.find(f => f.faculty_id === conflict.committee_member_id);
                  const facultyMember = facultyMembers.find(f => f.faculty_id === conflict.faculty_id);

                  return (
                    <TableRow key={conflict.id}>
                      <TableCell>{committeeMember?.name || "Unknown"}</TableCell>
                      <TableCell>{facultyMember?.name || "Unknown"}</TableCell>
                      <TableCell>{conflict.reason}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialog for adding conflict of interest */}
      <Dialog open={conflictDialogOpen} onOpenChange={setConflictDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Conflict of Interest</DialogTitle>
            <DialogDescription>
              Declare a conflict of interest for {selectedMember?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="conflictFaculty">Faculty Member</Label>
              <Select value={conflictFaculty} onValueChange={setConflictFaculty}>
                <SelectTrigger id="conflictFaculty">
                  <SelectValue placeholder="Select faculty member" />
                </SelectTrigger>
                <SelectContent>
                  {facultyMembers
                    .filter(faculty => faculty.faculty_id !== selectedMember?.faculty_id)
                    .map((faculty) => (
                      <SelectItem key={faculty.faculty_id} value={faculty.faculty_id.toString()}>
                        {faculty.name} - {faculty.department}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="conflictReason">Reason for Conflict</Label>
              <Input
                id="conflictReason"
                value={conflictReason}
                onChange={(e) => setConflictReason(e.target.value)}
                placeholder="e.g., Research collaboration, Personal relationship"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConflictDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddConflict}>
              Add Conflict
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
