"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2, Clock, FileText, Save } from "lucide-react";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function MeritReviewSubmissionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [reportContent, setReportContent] = useState("");
  const [reportStatus, setReportStatus] = useState("not_started"); // not_started, draft, submitted, under_review, reviewed
  const [isLoading, setIsLoading] = useState(false);
  const [workflowActive, setWorkflowActive] = useState(false);
  const [workflowData, setWorkflowData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("report");

  // Redirect if not authenticated or not regular_user
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
      return;
    }

    if (status === "authenticated") {
      const roles = session?.user?.roles || [];
      if (!roles.includes("regular_user") && !roles.includes("system_admin")) {
        router.push("/dashboard/merit-review");
      }
    }
  }, [status, session, router]);

  // Fetch workflow status and existing report
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch active workflow
        const workflowResponse = await fetch("/api/merit-review/workflow/active");
        if (!workflowResponse.ok) {
          throw new Error("Failed to fetch active workflow");
        }

        const workflowData = await workflowResponse.json();
        setWorkflowActive(!!workflowData.active);
        setWorkflowData(workflowData.active);

        if (workflowData.active) {
          // Fetch existing report
          const reportResponse = await fetch("/api/merit-review/report");
          if (reportResponse.ok) {
            const reportData = await reportResponse.json();
            if (reportData.report) {
              setReportContent(reportData.report.report_doc || "");
              setReportStatus(reportData.report.status || "not_started");
            } else {
              // No existing report, set default content
              setReportContent("# Merit Report\n\n## Teaching\n\nDescribe your teaching activities here...\n\n## Research\n\nDescribe your research activities here...\n\n## Service\n\nDescribe your service activities here...");
              setReportStatus("not_started");
            }
          } else {
            // Error fetching report, set default content
            setReportContent("# Merit Report\n\n## Teaching\n\nDescribe your teaching activities here...\n\n## Research\n\nDescribe your research activities here...\n\n## Service\n\nDescribe your service activities here...");
            setReportStatus("not_started");
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to fetch merit review data");
      }
    };

    if (status === "authenticated") {
      fetchData();
    }
  }, [status]);

  const handleSaveDraft = async () => {
    if (!reportContent.trim()) {
      toast.error("Report content cannot be empty");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/merit-review/report", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          report_doc: reportContent,
          status: "draft",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save draft");
      }

      const data = await response.json();
      setReportStatus(data.status);
      toast.success("Draft saved successfully");
    } catch (error) {
      console.error("Error saving draft:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save draft");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitReport = async () => {
    if (!reportContent.trim()) {
      toast.error("Report content cannot be empty");
      return;
    }

    // Confirm submission
    if (!confirm("Are you sure you want to submit your report? Once submitted, it cannot be edited.")) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/merit-review/report/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          report_doc: reportContent,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to submit report");
      }

      const data = await response.json();
      setReportStatus(data.status);
      toast.success("Report submitted successfully");
    } catch (error) {
      console.error("Error submitting report:", error);
      toast.error(error instanceof Error ? error.message : "Failed to submit report");
    } finally {
      setIsLoading(false);
    }
  };

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">

      {!workflowActive ? (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No active merit review process</AlertTitle>
          <AlertDescription>
            There is currently no active merit review process. Please check back later.
          </AlertDescription>
        </Alert>
      ) : (
        <>
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="report">Report</TabsTrigger>
              <TabsTrigger value="guidelines">Guidelines</TabsTrigger>
            </TabsList>

            <TabsContent value="report" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardDescription>
                    {reportStatus === "not_started" && "Create your merit report"}
                    {reportStatus === "draft" && "Continue editing your draft report"}
                    {reportStatus === "submitted" && "Your submitted report"}
                    {reportStatus === "under_review" && "Your report is under review"}
                    {reportStatus === "reviewed" && "Your report has been reviewed"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {reportStatus === "submitted" || reportStatus === "under_review" || reportStatus === "reviewed" ? (
                    <div className="prose max-w-none">
                      <div className="whitespace-pre-wrap border p-4 rounded-md bg-muted">
                        {reportContent}
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="report">Report Content</Label>
                        <Textarea
                          id="report"
                          value={reportContent}
                          onChange={(e) => setReportContent(e.target.value)}
                          placeholder="Enter your merit report content here..."
                          className="min-h-[300px] font-mono"
                        />
                        <p className="text-sm text-muted-foreground">
                          You can use Markdown formatting in your report.
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  {(reportStatus === "not_started" || reportStatus === "draft") && (
                    <>
                      <Button
                        variant="outline"
                        onClick={handleSaveDraft}
                        disabled={isLoading}
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Save Draft
                      </Button>
                      <Button
                        onClick={handleSubmitReport}
                        disabled={isLoading}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Submit Report
                      </Button>
                    </>
                  )}
                  {reportStatus === "submitted" && (
                    <div className="flex items-center text-green-600">
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Report submitted successfully
                    </div>
                  )}
                  {reportStatus === "under_review" && (
                    <div className="flex items-center text-amber-600">
                      <Clock className="h-4 w-4 mr-2" />
                      Report is currently under review
                    </div>
                  )}
                  {reportStatus === "reviewed" && (
                    <div className="flex items-center text-blue-600">
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Report has been reviewed
                    </div>
                  )}
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="guidelines" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Merit Review Guidelines</CardTitle>
                  <CardDescription>
                    Guidelines for preparing your merit review submission
                  </CardDescription>
                </CardHeader>
                <CardContent className="prose max-w-none">
                  <h2>Guidelines for Faculty of Engineering</h2>
                  <p>
                    The merit review process evaluates faculty performance in three key areas:
                  </p>
                  <h3>1. Teaching</h3>
                  <p>
                    Document your teaching activities, including courses taught, student supervision,
                    curriculum development, and teaching innovations. Include quantitative metrics
                    such as student evaluations if available.
                  </p>
                  <h3>2. Research</h3>
                  <p>
                    Document your research activities, including publications, grants, presentations,
                    and impact. Include both quantitative metrics (e.g., number of publications, citation
                    counts) and qualitative descriptions of research significance.
                  </p>
                  <h3>3. Service</h3>
                  <p>
                    Document your service activities, including committee work, administrative roles,
                    professional service, and community engagement.
                  </p>
                  <p>
                    For more detailed guidelines, please refer to the{" "}
                    <a href="https://uwaterloo.ca/engineering/faculty-and-staff/faculty-performance" target="_blank" rel="noopener noreferrer">
                      Faculty of Engineering performance guidelines
                    </a>.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
