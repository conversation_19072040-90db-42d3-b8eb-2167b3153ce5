'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

import {
  CheckCircle2,
  ClipboardList,
  FileText,
  GraduationCap,
  Award,
  BookOpen,
  Users,
  MessageSquare,
  AlertTriangle,
  Save,

} from 'lucide-react';
import { toast } from 'sonner';
import TeachingSection from './components/TeachingSection';
import ResearchSection from './components/ResearchSection';
import ServiceSection from './components/ServiceSection';
import AwardsSection from './components/AwardsSection';
import CommentsSection from './components/CommentsSection';
import SubmissionSummary from './components/SubmissionSummary';

export default function MeritReviewSubmissionPage() {
  const { status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const reportId = searchParams.get('id');
  const submitMode = searchParams.get('submit') === 'true';

  const [report, setReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('summary');

  // State for active workflow
  const [activeWorkflow, setActiveWorkflow] = useState<any>(null);
  const [creatingReport, setCreatingReport] = useState(false);

  // Fetch report data
  useEffect(() => {
    const fetchReport = async () => {
      if (status === 'authenticated') {
        try {
          setLoading(true);

          if (reportId) {
            // Fetch existing report
            const response = await fetch(`/api/merit-review/report/v2?id=${reportId}`);
            if (response.ok) {
              const data = await response.json();
              setReport(data);

              // If in submit mode, go to summary tab
              if (submitMode) {
                setActiveTab('summary');
              }
            } else {
              toast.error('Failed to fetch report');
            }
            // Only set loading to false when we have a reportId and have fetched the data
            setLoading(false);
          } else {
            // No report ID, check if there's an active workflow
            const workflowResponse = await fetch('/api/merit-review/workflow/active');
            if (workflowResponse.ok) {
              const workflowData = await workflowResponse.json();
              console.log('workflowData', workflowData);

              if (workflowData.active && workflowData.active.unit_id && workflowData.active.id) {
                console.log('Active workflow found:', workflowData.active);
                setActiveWorkflow(workflowData.active);

                // Check if user already has a report for this workflow
                const reportsResponse = await fetch('/api/merit-review/report/v2');
                if (reportsResponse.ok) {
                  const reports = await reportsResponse.json();
                  console.log('Existing reports:', reports);
                  if (reports && reports.length > 0) {
                    console.log('Redirecting to existing report:', reports[0].id);
                    // Keep loading true during redirect
                    router.push(`/dashboard/merit-review/submission/v2?id=${reports[0].id}`);
                    // Don't set loading to false here, as we're redirecting
                    return;
                  }
                  // If no reports, we'll show the create report UI
                  setLoading(false);
                } else {
                  console.error('Failed to fetch reports');
                  toast.error('Failed to fetch your reports');
                  setLoading(false);
                }
              } else {
                toast.error('No active merit review workflow found');
                setTimeout(() => router.push('/dashboard/merit-review'), 0);
                setLoading(false);
              }
            } else {
              toast.error('Failed to check for active workflows');
              setTimeout(() => router.push('/dashboard/merit-review'), 0);
              setLoading(false);
            }
          }
        } catch (error) {
          console.error('Error fetching report:', error);
          toast.error('An error occurred while fetching the report');
          setLoading(false);
        }
      }
    };

    fetchReport();
  }, [status, reportId, router, submitMode]);

  // Handle create report
  const handleCreateReport = async () => {
    if (!activeWorkflow) {
      toast.error('No active workflow found');
      return;
    }

    try {
      setCreatingReport(true);

      const createResponse = await fetch('/api/merit-review/report/v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          unit_id: activeWorkflow.unit_id,
          workflow_id: activeWorkflow.id,
          report_type: 'APR', // Default to APR, can be changed later
          report_year: new Date().getFullYear()
        }),
      });

      if (createResponse.ok) {
        const newReport = await createResponse.json();

        // Fetch course evaluations data - requirement #1: use login user facultySsoId to match userid in perceptions.course_evaluations
        try {
          const courseEvalResponse = await fetch('/api/merit-review/faculty-course-evaluations');
          if (courseEvalResponse.ok) {
            const courseEvalData = await courseEvalResponse.json();
            console.log('Course evaluation data fetched:', courseEvalData.length);

            // Add courses to the report in bulk
            if (courseEvalData && courseEvalData.length > 0) {
              // Prepare course data for bulk insert
              const coursesForBulkInsert = courseEvalData.map((course: any) => {
                // Make sure term_year is not null or undefined
                const termYear = course.term_year || `Unknown Term ${course.term_id || ''}`;

                return {
                  term_year: termYear, // Ensure we have a valid term_year
                  course_number: course.course_id || 'Unknown', // Ensure we have a valid course_number
                  course_title: course.course_title || 'Unknown Course', // Ensure we have a valid course_title
                  scp_q1_q3: course.scp_q1_q3 || '0.00', // Requirement #4: average of q1_avg, q2_avg, q3_avg
                  scp_q1_q3_std_dev: course.scp_q1_q3_std_dev || '0.00', // Requirement #8: average of q1_std, q2_std, q3_std
                  scp_q4_q6: course.scp_q4_q6 || '0.00', // Requirement #5: average of q4_avg, q5_avg, q6_avg
                  scp_q4_q6_std_dev: course.scp_q4_q6_std_dev || '0.00', // Requirement #9: average of q4_std, q5_std, q6_std
                  num_students: course.class_size || 0, // Requirement #6: use class_size as num_students
                  response_percentage: parseInt(course.response_percentage) || 0 // Requirement #7: calculated in the API as a whole number
                };
              });

              // Use the bulk endpoint to add all courses at once
              const bulkResponse = await fetch('/api/merit-review/report/v2/teaching/courses/bulk', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  report_id: newReport.id,
                  courses: coursesForBulkInsert
                }),
              });

              if (bulkResponse.ok) {
                const bulkResult = await bulkResponse.json();
                console.log(`Added ${bulkResult.courses.length} courses to the report in bulk`);
              } else {
                const errorData = await bulkResponse.json();
                console.error('Error response from bulk API:', errorData);
                throw new Error(`API error: ${errorData.error || 'Unknown error'}`);
              }
            } else {
              console.log('No course evaluation data found for this faculty member');
            }
          } else {
            console.error('Failed to fetch course evaluations');
          }
        } catch (error) {
          console.error('Error fetching course evaluations:', error);
        }

        // Fetch supervision data
        try {
          const supervisionResponse = await fetch('/api/merit-review/faculty-supervision');
          if (supervisionResponse.ok) {
            const supervisionData = await supervisionResponse.json();
            console.log('Supervision data fetched:', supervisionData.supervisions?.length || 0);

            // Update the student supervision section with the summary counts
            if (supervisionData && supervisionData.summary) {
              await fetch('/api/merit-review/report/v2/teaching/supervision', {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  report_id: newReport.id,
                  ...supervisionData.summary
                }),
              });

              // Add supervision details to the report in bulk
              if (supervisionData.supervisions && supervisionData.supervisions.length > 0) {
                // Filter supervisions to ensure they have required fields
                const validSupervisions = supervisionData.supervisions.filter(
                  (supervision: any) => supervision.student_name && supervision.academic_plan
                );

                if (validSupervisions.length > 0) {
                  // Prepare supervision data for bulk insert
                  const supervisionsForBulkInsert = validSupervisions.map((supervision: any) => ({
                    supervision_id: supervision.supervision_id || null,
                    term: supervision.term || null,
                    faculty: supervision.faculty || null,
                    department: supervision.department || null,
                    student_id: supervision.student_id || null,
                    student_name: supervision.student_name,
                    academic_plan: supervision.academic_plan,
                    supervisor_last_name: supervision.supervisor_last_name || null,
                    supervisor_first_name: supervision.supervisor_first_name || null,
                    citizenship: supervision.citizenship || null,
                    email_address: supervision.email_address || null
                  }));

                  // Use the bulk endpoint to add all supervision details at once
                  const bulkResponse = await fetch('/api/merit-review/report/v2/teaching/supervision-details/bulk', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      report_id: newReport.id,
                      supervisions: supervisionsForBulkInsert
                    }),
                  });

                  if (bulkResponse.ok) {
                    const bulkResult = await bulkResponse.json();
                    console.log(`Added ${bulkResult.supervisions.length} supervision details to the report in bulk`);
                  } else {
                    const errorData = await bulkResponse.json();
                    console.error('Error response from bulk API:', errorData);
                    throw new Error(`API error: ${errorData.error || 'Unknown error'}`);
                  }
                }
              }
            }
          } else {
            console.error('Failed to fetch supervision data');
          }
        } catch (error) {
          console.error('Error fetching supervision data:', error);
        }

        // Fetch research funding data
        try {
          const researchFundingResponse = await fetch('/api/merit-review/faculty-research-funding');
          if (researchFundingResponse.ok) {
            const researchFundingData = await researchFundingResponse.json();
            console.log('Research funding data fetched:', researchFundingData.length);

            // Add research grants to the report in bulk
            if (researchFundingData && researchFundingData.length > 0) {
              // Prepare research funding data for bulk insert
              const grantsForBulkInsert = researchFundingData.map((funding: any) => {
                return {
                  report_id: newReport.id,
                  grant_type: funding.sponsor_type || 'research', // Use sponsor_type as grant_type
                  pi_name: funding.researcher || 'Unknown', // Use researcher as pi_name
                  collaborators: null, // No collaborators data in the source
                  title: funding.project_title || 'Unknown Title', // Use project_title as title
                  agency: funding.sponsor_name || 'Unknown Agency', // Use sponsor_name as agency
                  amount: funding.total_award || 0, // Use total_award as amount
                  installment_year: funding.award_year ? parseInt(funding.award_year) : null, // Use award_year as installment_year
                  share_percentage: 100, // Default to 100% as per requirements
                  status: 'awarded', // Default to 'awarded' as per requirements
                  submission_date: null // No submission date in the source
                };
              });

              // Use the bulk endpoint to add all grants at once
              const bulkResponse = await fetch('/api/merit-review/report/v2/research/grants/bulk', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  report_id: newReport.id,
                  grants: grantsForBulkInsert
                }),
              });

              if (bulkResponse.ok) {
                const bulkResult = await bulkResponse.json();
                console.log(`Added ${bulkResult.grants.length} research grants to the report in bulk`);
              } else {
                const errorData = await bulkResponse.json();
                console.error('Error response from bulk API:', errorData);
                throw new Error(`API error: ${errorData.error || 'Unknown error'}`);
              }
            } else {
              console.log('No research funding data found for this faculty member');
            }
          } else {
            console.error('Failed to fetch research funding data');
          }
        } catch (error) {
          console.error('Error fetching research funding data:', error);
        }

        // Import Google Scholar publications
        try {
          console.log('Importing Google Scholar publications...');

          // Use the bulk endpoint to import publications from Google Scholar
          const publicationsResponse = await fetch('/api/merit-review/report/v2/research/publications/bulk', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              report_id: newReport.id
            }),
          });

          if (publicationsResponse.ok) {
            const publicationsResult = await publicationsResponse.json();
            console.log(`Imported ${publicationsResult.publications.length} publications from Google Scholar`);
          } else {
            const errorData = await publicationsResponse.json();
            console.error('Error response from publications API:', errorData);
            // Don't throw error here, just log it and continue
          }
        } catch (error) {
          console.error('Error importing Google Scholar publications:', error);
          // Don't throw error here, just log it and continue
        }

        toast.success('Merit report created successfully');
        router.push(`/dashboard/merit-review/submission/v2?id=${newReport.id}`);
      } else {
        const errorData = await createResponse.json();
        console.log('Error creating report:', errorData);
        toast.error(`Failed to create report: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error creating report:', error);
      toast.error('An error occurred while creating the report');
    } finally {
      setCreatingReport(false);
    }
  };

  // Handle save draft
  const handleSaveDraft = async () => {
    if (!report) return;

    try {
      setSaving(true);

      // Update report status to draft if it's not already
      if (report.status !== 'draft' && report.status !== 'in_progress') {
        const response = await fetch('/api/merit-review/report/v2', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: report.id,
            status: 'in_progress'
          }),
        });

        if (response.ok) {
          const updatedReport = await response.json();
          setReport({
            ...report,
            status: updatedReport.status,
            update_dt: updatedReport.update_dt
          });
          toast.success('Report saved as draft');
        } else {
          toast.error('Failed to save report');
        }
      } else {
        toast.success('Report saved');
      }
    } catch (error) {
      console.error('Error saving draft:', error);
      toast.error('An error occurred while saving the report');
    } finally {
      setSaving(false);
    }
  };

  // Handle submit report
  const handleSubmitReport = async () => {
    if (!report) return;

    try {
      setSubmitting(true);

      // Update report status to submitted
      const response = await fetch('/api/merit-review/report/v2', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: report.id,
          status: 'submitted'
        }),
      });

      if (response.ok) {
        const updatedReport = await response.json();
        setReport({
          ...report,
          status: updatedReport.status,
          update_dt: updatedReport.update_dt,
          submit_dt: updatedReport.submit_dt
        });
        toast.success('Report submitted successfully');
        router.push('/dashboard/merit-review');
      } else {
        toast.error('Failed to submit report');
      }
    } catch (error) {
      console.error('Error submitting report:', error);
      toast.error('An error occurred while submitting the report');
    } finally {
      setSubmitting(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Draft</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">In Progress</Badge>;
      case 'submitted':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Submitted</Badge>;
      case 'under_review':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Under Review</Badge>;
      case 'reviewed':
        return <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">Reviewed</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">Approved</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Unknown</Badge>;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle unauthenticated state with useEffect
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Check if report is editable
  const isEditable = report && (report.status === 'draft' || report.status === 'in_progress');

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          <p className="mt-4 text-lg">Loading your merit report...</p>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return null;
  }

  if (!report) {
    // If we have an active workflow but no report, show the create report UI
    if (activeWorkflow && !loading) {
      return (
        <div className="container mx-auto py-8">
          <Card className="max-w-3xl mx-auto">
            <CardHeader>
              <CardTitle>Create Merit Report</CardTitle>
              <CardDescription>
                You don't have an active merit report for the current review cycle.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
                <h3 className="text-lg font-medium text-blue-800 mb-2">Merit Review Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-blue-700">Department/Unit</p>
                    <p className="text-sm">{activeWorkflow.unit_name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-700">Review Period</p>
                    <p className="text-sm">{new Date(activeWorkflow.start_dt).toLocaleDateString()} - {new Date(activeWorkflow.end_dt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-md font-medium">What to expect:</h3>
                <ul className="list-disc pl-5 space-y-1 text-sm">
                  <li>You'll be able to document your teaching, research, service activities, and awards</li>
                  <li>You can save your report as a draft and return to it later</li>
                  <li>Once submitted, your report will be reviewed by the merit review committee</li>
                  <li>You'll receive feedback after the review process is complete</li>
                </ul>
              </div>

              <div className="flex justify-center pt-4">
                <Button
                  onClick={handleCreateReport}
                  disabled={creatingReport}
                  className="w-full md:w-auto"
                >
                  {creatingReport ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating Report...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      Create Merit Report
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    // If no active workflow or there was an error
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load the merit report. Please try again later.
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/dashboard/merit-review')}>
            Return to Merit Review Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Merit Report Submission</h1>
          <p className="text-muted-foreground">
            {report.report_type} {report.report_year} - {report.unit_name}
          </p>
        </div>
        <div className="flex items-center mt-2 md:mt-0">
          <p className="text-sm text-muted-foreground mr-2">Status:</p>
          {getStatusBadge(report.status)}
        </div>
      </div>

      {!isEditable && (
        <Alert className="mb-6">
          <FileText className="h-4 w-4" />
          <AlertTitle>View Only Mode</AlertTitle>
          <AlertDescription>
            This report has been submitted and cannot be edited.
          </AlertDescription>
        </Alert>
      )}

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Report Information</CardTitle>
          <CardDescription>
            Basic information about your merit report
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Report Type</p>
              <p className="text-sm">{report.report_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Report Year</p>
              <p className="text-sm">{report.report_year}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Department/Unit</p>
              <p className="text-sm">{report.unit_name}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Last Updated</p>
              <p className="text-sm">{formatDate(report.update_dt)}</p>
            </div>
            {report.submit_dt && (
              <div>
                <p className="text-sm font-medium">Submitted On</p>
                <p className="text-sm">{formatDate(report.submit_dt)}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-6 gap-2">
          <TabsTrigger value="summary" className="flex items-center">
            <ClipboardList className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Summary</span>
          </TabsTrigger>
          <TabsTrigger value="teaching" className="flex items-center">
            <GraduationCap className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Teaching</span>
          </TabsTrigger>
          <TabsTrigger value="research" className="flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Research</span>
          </TabsTrigger>
          <TabsTrigger value="service" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Service</span>
          </TabsTrigger>
          <TabsTrigger value="awards" className="flex items-center">
            <Award className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Awards</span>
          </TabsTrigger>
          <TabsTrigger value="comments" className="flex items-center">
            <MessageSquare className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Comments</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary">
          <SubmissionSummary
            report={report}
            isEditable={isEditable}
            onSubmit={handleSubmitReport}
            submitting={submitting}
          />
        </TabsContent>

        <TabsContent value="teaching">
          <TeachingSection
            report={report}
            isEditable={isEditable}
            onSave={handleSaveDraft}
            saving={saving}
          />
        </TabsContent>

        <TabsContent value="research">
          <ResearchSection
            report={report}
            isEditable={isEditable}
            onSave={handleSaveDraft}
            saving={saving}
          />
        </TabsContent>

        <TabsContent value="service">
          <ServiceSection
            report={report}
            isEditable={isEditable}
            onSave={handleSaveDraft}
            saving={saving}
          />
        </TabsContent>

        <TabsContent value="awards">
          <AwardsSection
            report={report}
            isEditable={isEditable}
            onSave={handleSaveDraft}
            saving={saving}
          />
        </TabsContent>

        <TabsContent value="comments">
          <CommentsSection
            report={report}
            isEditable={isEditable}
            onSave={handleSaveDraft}
            saving={saving}
          />
        </TabsContent>
      </Tabs>

      {isEditable && (
        <div className="flex justify-end mt-6 space-x-4">
          <Button
            variant="outline"
            onClick={handleSaveDraft}
            disabled={saving}
            className="flex items-center"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </>
            )}
          </Button>
          <Button
            onClick={() => {
              setActiveTab('summary');
              setTimeout(() => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }, 100);
            }}
            className="flex items-center"
          >
            <CheckCircle2 className="h-4 w-4 mr-2" />
            Review & Submit
          </Button>
        </div>
      )}
    </div>
  );
}
