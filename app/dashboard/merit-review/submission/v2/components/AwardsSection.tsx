'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PlusCircle, Pencil, Save, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface AwardsSectionProps {
  report: any;
  isEditable: boolean;
  onSave: () => Promise<void>;
  saving: boolean;
}

export default function AwardsSection({ report, isEditable, onSave, saving }: AwardsSectionProps) {
  const [isAddingAward, setIsAddingAward] = useState(false);
  const [editingAwardId, setEditingAwardId] = useState<number | null>(null);
  
  // Form state
  const [awardForm, setAwardForm] = useState({
    award_name: '',
    award_type: 'teaching',
    awarding_body: '',
    year: new Date().getFullYear(),
    description: ''
  });
  
  // Reset award form
  const resetAwardForm = () => {
    setAwardForm({
      award_name: '',
      award_type: 'teaching',
      awarding_body: '',
      year: new Date().getFullYear(),
      description: ''
    });
    setIsAddingAward(false);
    setEditingAwardId(null);
  };
  
  // Handle adding an award
  const handleAddAward = async () => {
    if (!awardForm.award_name || !awardForm.award_type) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    try {
      const response = await fetch('/api/merit-review/report/v2/awards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...awardForm
        }),
      });
      
      if (response.ok) {
        const newAward = await response.json();
        
        // Update the report state with the new award
        report.awards = [...(report.awards || []), newAward];
        
        toast.success('Award added successfully');
        resetAwardForm();
        onSave();
      } else {
        toast.error('Failed to add award');
      }
    } catch (error) {
      console.error('Error adding award:', error);
      toast.error('An error occurred while adding the award');
    }
  };
  
  // Handle editing an award
  const handleEditAward = (award: any) => {
    setAwardForm({
      award_name: award.award_name,
      award_type: award.award_type,
      awarding_body: award.awarding_body || '',
      year: award.year,
      description: award.description || ''
    });
    setEditingAwardId(award.id);
    setIsAddingAward(true);
  };
  
  // Handle updating an award
  const handleUpdateAward = async () => {
    if (!awardForm.award_name || !awardForm.award_type) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    try {
      const response = await fetch('/api/merit-review/report/v2/awards', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingAwardId,
          report_id: report.id,
          ...awardForm
        }),
      });
      
      if (response.ok) {
        const updatedAward = await response.json();
        
        // Update the report state with the updated award
        report.awards = report.awards.map((a: any) => 
          a.id === updatedAward.id ? updatedAward : a
        );
        
        toast.success('Award updated successfully');
        resetAwardForm();
        onSave();
      } else {
        toast.error('Failed to update award');
      }
    } catch (error) {
      console.error('Error updating award:', error);
      toast.error('An error occurred while updating the award');
    }
  };
  
  // Handle deleting an award
  const handleDeleteAward = async (id: number) => {
    if (!confirm('Are you sure you want to delete this award?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/merit-review/report/v2/awards?id=${id}&report_id=${report.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        // Update the report state by removing the deleted award
        report.awards = report.awards.filter((a: any) => a.id !== id);
        
        toast.success('Award deleted successfully');
        onSave();
      } else {
        toast.error('Failed to delete award');
      }
    } catch (error) {
      console.error('Error deleting award:', error);
      toast.error('An error occurred while deleting the award');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Awards & Honors</CardTitle>
          <CardDescription>
            Document awards and honors received during the review period
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Awards Table */}
          {report.awards && report.awards.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Award Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Awarding Body</TableHead>
                  <TableHead>Year</TableHead>
                  {isEditable && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {report.awards.map((award: any) => (
                  <TableRow key={award.id}>
                    <TableCell>{award.award_name}</TableCell>
                    <TableCell className="capitalize">{award.award_type}</TableCell>
                    <TableCell>{award.awarding_body}</TableCell>
                    <TableCell>{award.year}</TableCell>
                    {isEditable && (
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleEditAward(award)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleDeleteAward(award.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              No awards added yet.
            </div>
          )}
          
          {/* Add/Edit Award Form */}
          {isEditable && (
            <>
              {isAddingAward ? (
                <Card>
                  <CardHeader>
                    <CardTitle>{editingAwardId ? 'Edit Award' : 'Add Award'}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="award_name">Award Name*</Label>
                        <Input 
                          id="award_name"
                          value={awardForm.award_name}
                          onChange={(e) => setAwardForm({...awardForm, award_name: e.target.value})}
                          placeholder="e.g., Teaching Excellence Award"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="award_type">Award Type*</Label>
                        <Select 
                          value={awardForm.award_type}
                          onValueChange={(value) => setAwardForm({...awardForm, award_type: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="teaching">Teaching</SelectItem>
                            <SelectItem value="research">Research</SelectItem>
                            <SelectItem value="service">Service</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="year">Year*</Label>
                        <Input 
                          id="year"
                          type="number"
                          value={awardForm.year}
                          onChange={(e) => setAwardForm({...awardForm, year: parseInt(e.target.value) || new Date().getFullYear()})}
                        />
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="awarding_body">Awarding Body</Label>
                        <Input 
                          id="awarding_body"
                          value={awardForm.awarding_body}
                          onChange={(e) => setAwardForm({...awardForm, awarding_body: e.target.value})}
                          placeholder="e.g., Faculty of Engineering"
                        />
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea 
                          id="description"
                          value={awardForm.description}
                          onChange={(e) => setAwardForm({...awardForm, description: e.target.value})}
                          placeholder="Brief description of the award..."
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button 
                      variant="outline" 
                      onClick={resetAwardForm}
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={editingAwardId ? handleUpdateAward : handleAddAward}
                    >
                      {editingAwardId ? 'Update Award' : 'Add Award'}
                    </Button>
                  </CardFooter>
                </Card>
              ) : (
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => setIsAddingAward(true)}
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add Award
                </Button>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
