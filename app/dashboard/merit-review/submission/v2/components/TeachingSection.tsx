'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlusCircle, Pencil, Save, Trash2, UserPlus, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { formatTerm } from '@/app/lib/utils/term';

interface TeachingSectionProps {
  report: any;
  isEditable: boolean;
  onSave: () => Promise<void>;
  saving: boolean;
}

export default function TeachingSection({ report, isEditable, onSave, saving }: TeachingSectionProps) {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState('courses');
  const [isAddingCourse, setIsAddingCourse] = useState(false);
  const [editingCourseId, setEditingCourseId] = useState<number | null>(null);

  // Supervision details state
  const [supervisionDetails, setSupervisionDetails] = useState<any[]>([]);
  const [loadingSupervisionDetails, setLoadingSupervisionDetails] = useState(false);
  const [isAddingSupervision, setIsAddingSupervision] = useState(false);
  const [editingSupervisionId, setEditingSupervisionId] = useState<number | null>(null);

  // Form states
  const [courseForm, setCourseForm] = useState({
    term_year: '',
    course_number: '',
    course_title: '',
    scp_q1_q3: '',
    scp_q1_q3_std_dev: '',
    scp_q4_q6: '',
    scp_q4_q6_std_dev: '',
    num_students: '',
    response_percentage: ''
  });

  const [supervisionForm, setSupervisionForm] = useState({
    undergrad_supervised: report?.teaching?.supervision?.undergrad_supervised || 0,
    undergrad_cosupervised: report?.teaching?.supervision?.undergrad_cosupervised || 0,
    masters_supervised: report?.teaching?.supervision?.masters_supervised || 0,
    masters_cosupervised: report?.teaching?.supervision?.masters_cosupervised || 0,
    phd_supervised: report?.teaching?.supervision?.phd_supervised || 0,
    phd_cosupervised: report?.teaching?.supervision?.phd_cosupervised || 0,
    pdf_supervised: report?.teaching?.supervision?.pdf_supervised || 0,
    pdf_cosupervised: report?.teaching?.supervision?.pdf_cosupervised || 0,
    visitors_supervised: report?.teaching?.supervision?.visitors_supervised || 0,
    visitors_cosupervised: report?.teaching?.supervision?.visitors_cosupervised || 0
  });

  const [supervisionDetailForm, setSupervisionDetailForm] = useState({
    term: '',
    student_name: '',
    student_id: '',
    academic_plan: '',
    department: ''
  });

  const [summaryForm, setSummaryForm] = useState({
    accomplishments: report?.teaching?.summary?.accomplishments || '',
    goals: report?.teaching?.summary?.goals || ''
  });

  // Fetch supervision details when the tab is active
  useEffect(() => {
    if (activeTab === 'supervision' && report?.id) {
      fetchSupervisionDetails();
    }
  }, [activeTab, report?.id]);

  // Fetch supervision details
  const fetchSupervisionDetails = async () => {
    if (!report?.id) return;

    try {
      setLoadingSupervisionDetails(true);

      // Determine if the user is an admin
      const isAdmin = session?.user?.roles?.includes("system_admin") ||
                      session?.user?.roles?.includes("faculty_admin") ||
                      session?.user?.roles?.includes("department_admin");

      // Use the appropriate endpoint based on user role
      const endpoint = isAdmin
        ? `/api/merit-review/report/v2/teaching/supervision-details/admin?report_id=${report.id}`
        : `/api/merit-review/report/v2/teaching/supervision-details?report_id=${report.id}`;

      const response = await fetch(endpoint);

      if (response.ok) {
        const data = await response.json();
        setSupervisionDetails(data);
      } else {
        console.error('Failed to fetch supervision details');
        toast.error('Failed to load student supervision details');
      }
    } catch (error) {
      console.error('Error fetching supervision details:', error);
      toast.error('Error loading student supervision details');
    } finally {
      setLoadingSupervisionDetails(false);
    }
  };

  // Reset course form
  const resetCourseForm = () => {
    setCourseForm({
      term_year: '',
      course_number: '',
      course_title: '',
      scp_q1_q3: '',
      scp_q1_q3_std_dev: '',
      scp_q4_q6: '',
      scp_q4_q6_std_dev: '',
      num_students: '',
      response_percentage: ''
    });
    setIsAddingCourse(false);
    setEditingCourseId(null);
  };

  // Handle adding a course
  const handleAddCourse = async () => {
    if (!courseForm.term_year || !courseForm.course_number || !courseForm.course_title) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/teaching/courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...courseForm,
          num_students: parseInt(courseForm.num_students) || 0,
          response_percentage: parseInt(courseForm.response_percentage) || 0
        }),
      });

      if (response.ok) {
        const newCourse = await response.json();

        // Update the report state with the new course
        report.teaching.courses = [...(report.teaching.courses || []), newCourse];

        toast.success('Course added successfully');
        resetCourseForm();
        onSave();
      } else {
        toast.error('Failed to add course');
      }
    } catch (error) {
      console.error('Error adding course:', error);
      toast.error('An error occurred while adding the course');
    }
  };

  // Handle editing a course
  const handleEditCourse = (course: any) => {
    setCourseForm({
      term_year: course.term_year,
      course_number: course.course_number,
      course_title: course.course_title,
      scp_q1_q3: course.scp_q1_q3 || '',
      scp_q1_q3_std_dev: course.scp_q1_q3_std_dev || '',
      scp_q4_q6: course.scp_q4_q6 || '',
      scp_q4_q6_std_dev: course.scp_q4_q6_std_dev || '',
      num_students: course.num_students?.toString() || '',
      response_percentage: course.response_percentage?.toString() || ''
    });
    setEditingCourseId(course.id);
    setIsAddingCourse(true);
  };

  // Handle updating a course
  const handleUpdateCourse = async () => {
    if (!courseForm.term_year || !courseForm.course_number || !courseForm.course_title) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/teaching/courses', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingCourseId,
          report_id: report.id,
          ...courseForm,
          num_students: parseInt(courseForm.num_students) || 0,
          response_percentage: parseInt(courseForm.response_percentage) || 0
        }),
      });

      if (response.ok) {
        const updatedCourse = await response.json();

        // Update the report state with the updated course
        report.teaching.courses = report.teaching.courses.map((c: any) =>
          c.id === updatedCourse.id ? updatedCourse : c
        );

        toast.success('Course updated successfully');
        resetCourseForm();
        onSave();
      } else {
        toast.error('Failed to update course');
      }
    } catch (error) {
      console.error('Error updating course:', error);
      toast.error('An error occurred while updating the course');
    }
  };

  // Handle deleting a course
  const handleDeleteCourse = async (id: number) => {
    if (!confirm('Are you sure you want to delete this course?')) {
      return;
    }

    try {
      const response = await fetch(`/api/merit-review/report/v2/teaching/courses?id=${id}&report_id=${report.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update the report state by removing the deleted course
        report.teaching.courses = report.teaching.courses.filter((c: any) => c.id !== id);

        toast.success('Course deleted successfully');
        onSave();
      } else {
        toast.error('Failed to delete course');
      }
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error('An error occurred while deleting the course');
    }
  };

  // Handle updating supervision
  const handleUpdateSupervision = async () => {
    try {
      const response = await fetch('/api/merit-review/report/v2/teaching/supervision', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...supervisionForm
        }),
      });

      if (response.ok) {
        const updatedSupervision = await response.json();

        // Update the report state with the updated supervision
        report.teaching.supervision = updatedSupervision;

        toast.success('Student supervision updated successfully');
        onSave();
      } else {
        toast.error('Failed to update student supervision');
      }
    } catch (error) {
      console.error('Error updating supervision:', error);
      toast.error('An error occurred while updating student supervision');
    }
  };

  // Reset supervision detail form
  const resetSupervisionDetailForm = () => {
    setSupervisionDetailForm({
      term: '',
      student_name: '',
      student_id: '',
      academic_plan: '',
      department: ''
    });
    setIsAddingSupervision(false);
    setEditingSupervisionId(null);
  };

  // Handle adding a supervision detail
  const handleAddSupervisionDetail = async () => {
    if (!supervisionDetailForm.student_name || !supervisionDetailForm.academic_plan) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/teaching/supervision-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...supervisionDetailForm,
          term: parseInt(supervisionDetailForm.term) || null
        }),
      });

      if (response.ok) {
        const newSupervision = await response.json();

        // Update the supervision details state
        setSupervisionDetails([...supervisionDetails, newSupervision]);

        toast.success('Student supervision detail added successfully');
        resetSupervisionDetailForm();

        // Update the supervision counts
        await updateSupervisionCounts();

        onSave();
      } else {
        toast.error('Failed to add student supervision detail');
      }
    } catch (error) {
      console.error('Error adding supervision detail:', error);
      toast.error('An error occurred while adding the supervision detail');
    }
  };

  // Handle editing a supervision detail
  const handleEditSupervisionDetail = (supervision: any) => {
    setSupervisionDetailForm({
      term: supervision.term?.toString() || '',
      student_name: supervision.student_name || '',
      student_id: supervision.student_id || '',
      academic_plan: supervision.academic_plan || '',
      department: supervision.department || ''
    });
    setEditingSupervisionId(supervision.id);
    setIsAddingSupervision(true);
  };

  // Handle updating a supervision detail
  const handleUpdateSupervisionDetail = async () => {
    if (!supervisionDetailForm.student_name || !supervisionDetailForm.academic_plan) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/teaching/supervision-details', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingSupervisionId,
          report_id: report.id,
          ...supervisionDetailForm,
          term: parseInt(supervisionDetailForm.term) || null
        }),
      });

      if (response.ok) {
        const updatedSupervision = await response.json();

        // Update the supervision details state
        setSupervisionDetails(supervisionDetails.map(s =>
          s.id === updatedSupervision.id ? updatedSupervision : s
        ));

        toast.success('Student supervision detail updated successfully');
        resetSupervisionDetailForm();

        // Update the supervision counts
        await updateSupervisionCounts();

        onSave();
      } else {
        toast.error('Failed to update student supervision detail');
      }
    } catch (error) {
      console.error('Error updating supervision detail:', error);
      toast.error('An error occurred while updating the supervision detail');
    }
  };

  // Handle deleting a supervision detail
  const handleDeleteSupervisionDetail = async (id: number) => {
    if (!confirm('Are you sure you want to delete this supervision detail?')) {
      return;
    }

    try {
      const response = await fetch(`/api/merit-review/report/v2/teaching/supervision-details?id=${id}&report_id=${report.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update the supervision details state
        setSupervisionDetails(supervisionDetails.filter(s => s.id !== id));

        toast.success('Student supervision detail deleted successfully');

        // Update the supervision counts
        await updateSupervisionCounts();

        onSave();
      } else {
        toast.error('Failed to delete student supervision detail');
      }
    } catch (error) {
      console.error('Error deleting supervision detail:', error);
      toast.error('An error occurred while deleting the supervision detail');
    }
  };

  // Update supervision counts based on supervision details
  const updateSupervisionCounts = async () => {
    // Get current date and calculate the date 3 years ago
    const currentDate = new Date();
    const threeYearsAgo = new Date();
    threeYearsAgo.setFullYear(currentDate.getFullYear() - 3);

    // Convert to term format (YYYYS) - approximate by using the first term of the year from 3 years ago
    const threeYearsAgoTerm = threeYearsAgo.getFullYear() * 10 + 1; // Using 1 for Winter term

    // Filter for non-deleted supervision details from the last 3 years
    const recentSupervisions = supervisionDetails.filter(s =>
      !s.is_deleted &&
      (s.term ? s.term >= threeYearsAgoTerm : true) // Include if term is missing or >= 3 years ago
    );

    // Create sets of distinct student IDs/names for each category
    const distinctMastersStudents = new Set();
    const distinctPhdStudents = new Set();

    // Process each supervision record
    recentSupervisions.forEach(s => {
      if (!s.academic_plan) return;

      // Use student_id as the key if available, otherwise use student_name
      const studentKey = s.student_id || s.student_name;

      // Check if academic plan ends with 'M' for Master's
      if (s.academic_plan.endsWith('M')) {
        distinctMastersStudents.add(studentKey);
      }
      // Check if academic plan ends with 'D' for PhD
      else if (s.academic_plan.endsWith('D')) {
        distinctPhdStudents.add(studentKey);
      }
    });

    // Get the counts of distinct students
    const mastersCount = distinctMastersStudents.size;
    const phdCount = distinctPhdStudents.size;

    // Update the supervision form with the new counts
    const newSupervisionForm = {
      ...supervisionForm,
      // Set undergraduate, PDF, and visitors to 0 as we're only showing Master's and PhD
      undergrad_supervised: 0,
      masters_supervised: mastersCount,
      phd_supervised: phdCount,
      pdf_supervised: 0,
      visitors_supervised: 0
    };

    setSupervisionForm(newSupervisionForm);

    // Update the supervision counts in the database
    try {
      const response = await fetch('/api/merit-review/report/v2/teaching/supervision', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...newSupervisionForm
        }),
      });

      if (response.ok) {
        const updatedSupervision = await response.json();
        report.teaching.supervision = updatedSupervision;
      }
    } catch (error) {
      console.error('Error updating supervision counts:', error);
    }
  };

  // Handle updating summary
  const handleUpdateSummary = async () => {
    try {
      const response = await fetch('/api/merit-review/report/v2/teaching/summary', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...summaryForm
        }),
      });

      if (response.ok) {
        const updatedSummary = await response.json();

        // Update the report state with the updated summary
        report.teaching.summary = updatedSummary;

        toast.success('Teaching summary updated successfully');
        onSave();
      } else {
        toast.error('Failed to update teaching summary');
      }
    } catch (error) {
      console.error('Error updating summary:', error);
      toast.error('An error occurred while updating teaching summary');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Teaching</CardTitle>
          <CardDescription>
            Document your teaching activities for the review period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="courses">Courses</TabsTrigger>
              <TabsTrigger value="supervision">Student Supervision</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>

            <TabsContent value="courses" className="space-y-4">
              {/* Courses Table */}
              {report.teaching?.courses && report.teaching.courses.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Term</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>SCP Q1-3</TableHead>
                      <TableHead>SCP Q4-6</TableHead>
                      <TableHead>Students</TableHead>
                      <TableHead>Response %</TableHead>
                      {isEditable && <TableHead>Actions</TableHead>}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {report.teaching.courses.map((course: any) => (
                      <TableRow key={course.id}>
                        <TableCell>{course.term_year}</TableCell>
                        <TableCell>{course.course_number}</TableCell>
                        <TableCell>{course.course_title}</TableCell>
                        <TableCell>{course.scp_q1_q3}</TableCell>
                        <TableCell>{course.scp_q4_q6}</TableCell>
                        <TableCell>{course.num_students}</TableCell>
                        <TableCell>{course.response_percentage}%</TableCell>
                        {isEditable && (
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEditCourse(course)}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteCourse(course.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No courses added yet.
                </div>
              )}

              {/* Add/Edit Course Form */}
              {isEditable && (
                <>
                  {isAddingCourse ? (
                    <Card>
                      <CardHeader>
                        <CardTitle>{editingCourseId ? 'Edit Course' : 'Add Course'}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="term_year">Term/Year*</Label>
                            <Input
                              id="term_year"
                              value={courseForm.term_year}
                              onChange={(e) => setCourseForm({...courseForm, term_year: e.target.value})}
                              placeholder="e.g., Fall 2023"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="course_number">Course Number*</Label>
                            <Input
                              id="course_number"
                              value={courseForm.course_number}
                              onChange={(e) => setCourseForm({...courseForm, course_number: e.target.value})}
                              placeholder="e.g., CS 101"
                            />
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="course_title">Course Title*</Label>
                            <Input
                              id="course_title"
                              value={courseForm.course_title}
                              onChange={(e) => setCourseForm({...courseForm, course_title: e.target.value})}
                              placeholder="e.g., Introduction to Computer Science"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="scp_q1_q3">SCP Q1-3 Score</Label>
                            <Input
                              id="scp_q1_q3"
                              value={courseForm.scp_q1_q3}
                              onChange={(e) => setCourseForm({...courseForm, scp_q1_q3: e.target.value})}
                              placeholder="e.g., 4.5"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="scp_q1_q3_std_dev">SCP Q1-3 Std Dev</Label>
                            <Input
                              id="scp_q1_q3_std_dev"
                              value={courseForm.scp_q1_q3_std_dev}
                              onChange={(e) => setCourseForm({...courseForm, scp_q1_q3_std_dev: e.target.value})}
                              placeholder="e.g., 0.5"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="scp_q4_q6">SCP Q4-6 Score</Label>
                            <Input
                              id="scp_q4_q6"
                              value={courseForm.scp_q4_q6}
                              onChange={(e) => setCourseForm({...courseForm, scp_q4_q6: e.target.value})}
                              placeholder="e.g., 4.2"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="scp_q4_q6_std_dev">SCP Q4-6 Std Dev</Label>
                            <Input
                              id="scp_q4_q6_std_dev"
                              value={courseForm.scp_q4_q6_std_dev}
                              onChange={(e) => setCourseForm({...courseForm, scp_q4_q6_std_dev: e.target.value})}
                              placeholder="e.g., 0.7"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="num_students">Number of Students</Label>
                            <Input
                              id="num_students"
                              type="number"
                              value={courseForm.num_students}
                              onChange={(e) => setCourseForm({...courseForm, num_students: e.target.value})}
                              placeholder="e.g., 50"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="response_percentage">Response Percentage</Label>
                            <Input
                              id="response_percentage"
                              type="number"
                              value={courseForm.response_percentage}
                              onChange={(e) => setCourseForm({...courseForm, response_percentage: e.target.value})}
                              placeholder="e.g., 75"
                            />
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Button
                          variant="outline"
                          onClick={resetCourseForm}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={editingCourseId ? handleUpdateCourse : handleAddCourse}
                        >
                          {editingCourseId ? 'Update Course' : 'Add Course'}
                        </Button>
                      </CardFooter>
                    </Card>
                  ) : (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setIsAddingCourse(true)}
                    >
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Course
                    </Button>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="supervision" className="space-y-4">
              {/* Student Supervision Summary */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Student Supervision Summary</CardTitle>
                  <CardDescription>
                    Summary of students you supervised or co-supervised
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left font-medium py-2 pl-1">Student Type</th>
                          <th className="text-center font-medium py-2">Supervised</th>
                          <th className="text-center font-medium py-2">Co-supervised</th>
                          <th className="text-center font-medium py-2">Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b border-muted">
                          <td className="py-2 pl-1 font-medium text-green-700">Master's</td>
                          <td className="text-center py-2">
                            <span className="bg-green-50 text-green-700 px-2 py-1 rounded-md font-medium inline-block min-w-[2rem]">
                              {supervisionForm.masters_supervised}
                            </span>
                          </td>
                          <td className="text-center py-2">
                            <span className="bg-green-50 text-green-700 px-2 py-1 rounded-md font-medium inline-block min-w-[2rem]">
                              {supervisionForm.masters_cosupervised}
                            </span>
                          </td>
                          <td className="text-center py-2 font-medium">
                            {supervisionForm.masters_supervised + supervisionForm.masters_cosupervised}
                          </td>
                        </tr>
                        <tr className="border-b border-muted">
                          <td className="py-2 pl-1 font-medium text-purple-700">PhD</td>
                          <td className="text-center py-2">
                            <span className="bg-purple-50 text-purple-700 px-2 py-1 rounded-md font-medium inline-block min-w-[2rem]">
                              {supervisionForm.phd_supervised}
                            </span>
                          </td>
                          <td className="text-center py-2">
                            <span className="bg-purple-50 text-purple-700 px-2 py-1 rounded-md font-medium inline-block min-w-[2rem]">
                              {supervisionForm.phd_cosupervised}
                            </span>
                          </td>
                          <td className="text-center py-2 font-medium">
                            {supervisionForm.phd_supervised + supervisionForm.phd_cosupervised}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-2 pl-1 font-medium">Total</td>
                          <td className="text-center py-2 font-medium">
                            {supervisionForm.masters_supervised +
                             supervisionForm.phd_supervised}
                          </td>
                          <td className="text-center py-2 font-medium">
                            {supervisionForm.masters_cosupervised +
                             supervisionForm.phd_cosupervised}
                          </td>
                          <td className="text-center py-2 font-medium">
                            {supervisionForm.masters_supervised + supervisionForm.masters_cosupervised +
                             supervisionForm.phd_supervised + supervisionForm.phd_cosupervised}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  {isEditable && (
                    <div className="mt-4">
                      <p className="text-xs text-muted-foreground">
                        Note: Supervised counts show distinct students from the last 3 years with academic plans ending in 'M' (Master's) or 'D' (PhD). You can adjust co-supervised counts if needed.
                      </p>
                      <div className="grid grid-cols-2 gap-4 mt-3">
                        <div className="space-y-1">
                          <Label htmlFor="masters_cosupervised" className="text-xs">Master's (Co-supervised)</Label>
                          <Input
                            id="masters_cosupervised"
                            type="number"
                            value={supervisionForm.masters_cosupervised}
                            onChange={(e) => setSupervisionForm({...supervisionForm, masters_cosupervised: parseInt(e.target.value) || 0})}
                            className="h-8 text-sm"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor="phd_cosupervised" className="text-xs">PhD (Co-supervised)</Label>
                          <Input
                            id="phd_cosupervised"
                            type="number"
                            value={supervisionForm.phd_cosupervised}
                            onChange={(e) => setSupervisionForm({...supervisionForm, phd_cosupervised: parseInt(e.target.value) || 0})}
                            className="h-8 text-sm"
                          />
                        </div>
                      </div>
                      <div className="mt-3 flex justify-end">
                        <Button
                          onClick={handleUpdateSupervision}
                          size="sm"
                        >
                          <Save className="h-3.5 w-3.5 mr-1.5" />
                          Save Co-supervision Counts
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Student Supervision Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Student Supervision Details</CardTitle>
                  <CardDescription>
                    List of students you supervised during the review period
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loadingSupervisionDetails ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                      <span className="ml-2 text-muted-foreground">Loading supervision data...</span>
                    </div>
                  ) : supervisionDetails.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Student Name</TableHead>
                          <TableHead>Student ID</TableHead>
                          <TableHead>Academic Plan</TableHead>
                          <TableHead>Department</TableHead>
                          <TableHead>Terms</TableHead>
                          {isEditable && <TableHead>Actions</TableHead>}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {(() => {
                          // Group supervision details by student
                          const studentMap = new Map();

                          // Group by student ID if available, otherwise by name
                          supervisionDetails.forEach(supervision => {
                            const key = supervision.student_id || supervision.student_name;
                            if (!studentMap.has(key)) {
                              studentMap.set(key, {
                                student_name: supervision.student_name,
                                student_id: supervision.student_id,
                                academic_plan: supervision.academic_plan,
                                department: supervision.department,
                                terms: [],
                                details: []
                              });
                            }

                            // Add term to the student's terms list if it's not already there
                            if (supervision.term) {
                              studentMap.get(key).terms.push({
                                term: supervision.term,
                                id: supervision.id
                              });
                            }

                            // Add the full supervision detail for reference
                            studentMap.get(key).details.push(supervision);
                          });

                          // Convert the map to an array and sort by student name
                          return Array.from(studentMap.values())
                            .sort((a, b) => a.student_name.localeCompare(b.student_name))
                            .map((student, index) => (
                              <TableRow key={index}>
                                <TableCell>{student.student_name}</TableCell>
                                <TableCell>{student.student_id}</TableCell>
                                <TableCell>{student.academic_plan}</TableCell>
                                <TableCell>{student.department}</TableCell>
                                <TableCell>
                                  <div className="flex flex-wrap gap-1">
                                    {student.terms
                                      .sort((a: { term: number; id: number }, b: { term: number; id: number }) => b.term - a.term) // Sort terms in descending order
                                      .map((termInfo: { term: number; id: number }, i: number) => (
                                        <div
                                          key={i}
                                          className="inline-flex items-center bg-muted px-2 py-1 rounded-md text-xs"
                                        >
                                          {formatTerm(termInfo.term)}
                                          {isEditable && (
                                            <button
                                              className="ml-1 text-muted-foreground hover:text-foreground"
                                              onClick={() => {
                                                const detail = student.details.find((d: any) => d.id === termInfo.id);
                                                if (detail) handleEditSupervisionDetail(detail);
                                              }}
                                            >
                                              <Pencil className="h-3 w-3" />
                                            </button>
                                          )}
                                        </div>
                                      ))}
                                  </div>
                                </TableCell>
                                {isEditable && (
                                  <TableCell>
                                    <div className="flex space-x-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          // Pre-fill the form with student info but empty term
                                          setSupervisionDetailForm({
                                            term: '',
                                            student_name: student.student_name,
                                            student_id: student.student_id || '',
                                            academic_plan: student.academic_plan,
                                            department: student.department || ''
                                          });
                                          setIsAddingSupervision(true);
                                        }}
                                      >
                                        Add Term
                                      </Button>
                                      {student.details.length > 0 && (
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="text-destructive hover:text-destructive"
                                          onClick={() => {
                                            if (confirm(`Are you sure you want to delete all supervision records for ${student.student_name}?`)) {
                                              // Delete all supervision details for this student
                                              Promise.all(
                                                student.details.map((detail: any) =>
                                                  fetch(`/api/merit-review/report/v2/teaching/supervision-details?id=${detail.id}&report_id=${report.id}`, {
                                                    method: 'DELETE',
                                                  })
                                                )
                                              ).then(() => {
                                                // Update the supervision details state
                                                setSupervisionDetails(supervisionDetails.filter(s =>
                                                  s.student_id !== student.student_id && s.student_name !== student.student_name
                                                ));
                                                toast.success('Student supervision details deleted successfully');
                                                updateSupervisionCounts();
                                                onSave();
                                              }).catch(error => {
                                                console.error('Error deleting supervision details:', error);
                                                toast.error('An error occurred while deleting the supervision details');
                                              });
                                            }
                                          }}
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      )}
                                    </div>
                                  </TableCell>
                                )}
                              </TableRow>
                            ));
                        })()}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No student supervision details added yet.
                    </div>
                  )}

                  {/* Add/Edit Supervision Detail Form */}
                  {isEditable && (
                    <>
                      {isAddingSupervision ? (
                        <Card className="mt-4">
                          <CardHeader>
                            <CardTitle>{editingSupervisionId ? 'Edit Student' : 'Add Student'}</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="term">Term (YYYYS format)*</Label>
                                <Input
                                  id="term"
                                  value={supervisionDetailForm.term}
                                  onChange={(e) => setSupervisionDetailForm({...supervisionDetailForm, term: e.target.value})}
                                  placeholder="e.g., 1231 for Winter 2023"
                                />
                                <p className="text-xs text-muted-foreground">
                                  Format: YYYYS where S is 1 (Winter), 5 (Spring), or 9 (Fall)
                                </p>
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="student_name">Student Name*</Label>
                                <Input
                                  id="student_name"
                                  value={supervisionDetailForm.student_name}
                                  onChange={(e) => setSupervisionDetailForm({...supervisionDetailForm, student_name: e.target.value})}
                                  placeholder="e.g., John Smith"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="student_id">Student ID</Label>
                                <Input
                                  id="student_id"
                                  value={supervisionDetailForm.student_id}
                                  onChange={(e) => setSupervisionDetailForm({...supervisionDetailForm, student_id: e.target.value})}
                                  placeholder="e.g., 12345678"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="academic_plan">Academic Plan*</Label>
                                <Select
                                  value={supervisionDetailForm.academic_plan}
                                  onValueChange={(value) => setSupervisionDetailForm({...supervisionDetailForm, academic_plan: value})}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select academic plan" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Undergraduate">Undergraduate</SelectItem>
                                    <SelectItem value="Master's">Master's</SelectItem>
                                    <SelectItem value="PhD">PhD</SelectItem>
                                    <SelectItem value="Postdoctoral Fellow">Postdoctoral Fellow</SelectItem>
                                    <SelectItem value="Visiting Scholar">Visiting Scholar</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="space-y-2 md:col-span-2">
                                <Label htmlFor="department">Department</Label>
                                <Input
                                  id="department"
                                  value={supervisionDetailForm.department}
                                  onChange={(e) => setSupervisionDetailForm({...supervisionDetailForm, department: e.target.value})}
                                  placeholder="e.g., Electrical and Computer Engineering"
                                />
                              </div>
                            </div>
                          </CardContent>
                          <CardFooter className="flex justify-between">
                            <Button
                              variant="outline"
                              onClick={resetSupervisionDetailForm}
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={editingSupervisionId ? handleUpdateSupervisionDetail : handleAddSupervisionDetail}
                            >
                              {editingSupervisionId ? 'Update Student' : 'Add Student'}
                            </Button>
                          </CardFooter>
                        </Card>
                      ) : (
                        <Button
                          variant="outline"
                          className="w-full mt-4"
                          onClick={() => setIsAddingSupervision(true)}
                        >
                          <UserPlus className="h-4 w-4 mr-2" />
                          Add Student
                        </Button>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="summary" className="space-y-4">
              {/* Teaching Summary Form */}
              <Card>
                <CardHeader>
                  <CardTitle>Teaching Summary</CardTitle>
                  <CardDescription>
                    Summarize your teaching accomplishments and goals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="accomplishments">Teaching Accomplishments</Label>
                      <Textarea
                        id="accomplishments"
                        value={summaryForm.accomplishments}
                        onChange={(e) => setSummaryForm({...summaryForm, accomplishments: e.target.value})}
                        placeholder="Describe your teaching accomplishments during the review period..."
                        className="min-h-[150px]"
                        disabled={!isEditable}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="goals">Teaching Goals</Label>
                      <Textarea
                        id="goals"
                        value={summaryForm.goals}
                        onChange={(e) => setSummaryForm({...summaryForm, goals: e.target.value})}
                        placeholder="Describe your teaching goals for the next review period..."
                        className="min-h-[150px]"
                        disabled={!isEditable}
                      />
                    </div>
                  </div>
                </CardContent>
                {isEditable && (
                  <CardFooter>
                    <Button
                      onClick={handleUpdateSummary}
                      className="ml-auto"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
