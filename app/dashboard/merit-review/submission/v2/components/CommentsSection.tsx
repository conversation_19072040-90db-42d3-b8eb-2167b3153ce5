'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Save } from 'lucide-react';
import { toast } from 'sonner';

interface CommentsSectionProps {
  report: any;
  isEditable: boolean;
  onSave: () => Promise<void>;
  saving: boolean;
}

export default function CommentsSection({ report, isEditable, onSave, saving }: CommentsSectionProps) {
  const [comments, setComments] = useState(report?.additionalComments?.comments || '');
  
  // Handle updating comments
  const handleUpdateComments = async () => {
    try {
      const response = await fetch('/api/merit-review/report/v2/comments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          comments
        }),
      });
      
      if (response.ok) {
        const updatedComments = await response.json();
        
        // Update the report state with the updated comments
        report.additionalComments = updatedComments;
        
        toast.success('Comments updated successfully');
        onSave();
      } else {
        toast.error('Failed to update comments');
      }
    } catch (error) {
      console.error('Error updating comments:', error);
      toast.error('An error occurred while updating comments');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Additional Comments</CardTitle>
          <CardDescription>
            Provide any additional information or comments that may be relevant to your merit review
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="comments">Comments</Label>
              <Textarea 
                id="comments"
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                placeholder="Enter any additional comments or information here..."
                className="min-h-[200px]"
                disabled={!isEditable}
              />
            </div>
          </div>
        </CardContent>
        {isEditable && (
          <CardFooter>
            <Button 
              onClick={handleUpdateComments}
              className="ml-auto"
              disabled={saving}
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Comments
                </>
              )}
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
