'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '../../../../../../components/ui/separator';
import { CheckCircle2, AlertTriangle, Send, FileText } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface SubmissionSummaryProps {
  report: any;
  isEditable: boolean;
  onSubmit: () => Promise<void>;
  submitting: boolean;
}

export default function SubmissionSummary({ report, isEditable, onSubmit, submitting }: SubmissionSummaryProps) {
  const [confirmOpen, setConfirmOpen] = useState(false);

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Check if sections are complete
  const isTeachingComplete = () => {
    if (!report.teaching) return false;

    // Check if teaching courses are added
    const hasCourses = report.teaching.courses && report.teaching.courses.length > 0;

    // Check if summary is filled
    const hasSummary = report.teaching.summary &&
      report.teaching.summary.accomplishments &&
      report.teaching.summary.goals;

    return hasCourses && hasSummary;
  };

  const isResearchComplete = () => {
    if (!report.research) return false;

    // Check if publications or grants are added
    const hasPublications = report.research.publications && report.research.publications.length > 0;
    const hasGrants = report.research.grants && report.research.grants.length > 0;

    // Check if summary is filled
    const hasSummary = report.research.summary &&
      report.research.summary.accomplishments &&
      report.research.summary.goals;

    // Research is complete if there are publications OR grants, AND the summary is filled
    return (hasPublications || hasGrants) && hasSummary;
  };

  const isServiceComplete = () => {
    if (!report.service) return false;

    // Check if service activities are added
    const hasActivities = report.service.activities && report.service.activities.length > 0;

    // Check if summary is filled
    const hasSummary = report.service.summary &&
      report.service.summary.accomplishments &&
      report.service.summary.goals;

    return hasActivities && hasSummary;
  };

  const isAwardsComplete = () => {
    // Awards are optional, so this is always true
    return true;
  };

  const isCommentsComplete = () => {
    // Comments are optional, so this is always true
    return true;
  };

  const allSectionsComplete =
    isTeachingComplete() &&
    isResearchComplete() &&
    isServiceComplete() &&
    isAwardsComplete() &&
    isCommentsComplete();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Submission Summary</CardTitle>
          <CardDescription>
            Review your merit report before submission
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Report Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium">Current Status</p>
                <p className="text-sm">{report.status}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Last Updated</p>
                <p className="text-sm">{formatDate(report.update_dt)}</p>
              </div>
              {report.submit_dt && (
                <div>
                  <p className="text-sm font-medium">Submitted On</p>
                  <p className="text-sm">{formatDate(report.submit_dt)}</p>
                </div>
              )}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-medium mb-2">Section Completion</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Teaching</span>
                {isTeachingComplete() ? (
                  <span className="flex items-center text-green-600 text-sm">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Complete
                  </span>
                ) : (
                  <span className="flex items-center text-amber-600 text-sm">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Incomplete
                  </span>
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Research</span>
                {isResearchComplete() ? (
                  <span className="flex items-center text-green-600 text-sm">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Complete
                  </span>
                ) : (
                  <span className="flex items-center text-amber-600 text-sm">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Incomplete
                  </span>
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Service</span>
                {isServiceComplete() ? (
                  <span className="flex items-center text-green-600 text-sm">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Complete
                  </span>
                ) : (
                  <span className="flex items-center text-amber-600 text-sm">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Incomplete
                  </span>
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Awards (Optional)</span>
                <span className="flex items-center text-green-600 text-sm">
                  <CheckCircle2 className="h-4 w-4 mr-1" />
                  Complete
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Additional Comments (Optional)</span>
                <span className="flex items-center text-green-600 text-sm">
                  <CheckCircle2 className="h-4 w-4 mr-1" />
                  Complete
                </span>
              </div>
            </div>
          </div>

          {!allSectionsComplete && isEditable && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Incomplete Sections</AlertTitle>
              <AlertDescription>
                Please complete all required sections before submitting your report.
              </AlertDescription>
            </Alert>
          )}

          {report.status === 'submitted' && (
            <Alert>
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle>Report Submitted</AlertTitle>
              <AlertDescription>
                Your report has been submitted successfully on {formatDate(report.submit_dt)}.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter className="flex justify-end">
          {isEditable && (
            <Dialog open={confirmOpen} onOpenChange={setConfirmOpen}>
              <DialogTrigger asChild>
                <Button
                  disabled={!allSectionsComplete || submitting}
                  className="flex items-center"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Submit Report
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Confirm Submission</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to submit your merit report? Once submitted, it cannot be edited.
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <p className="text-sm text-muted-foreground">
                    Please ensure all information is accurate and complete before submitting.
                  </p>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setConfirmOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      onSubmit();
                      setConfirmOpen(false);
                    }}
                    disabled={submitting}
                    className="flex items-center"
                  >
                    {submitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Submitting...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4 mr-2" />
                        Confirm Submission
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
