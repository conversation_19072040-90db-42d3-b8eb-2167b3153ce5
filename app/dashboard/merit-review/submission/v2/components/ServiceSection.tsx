'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PlusCircle, Pencil, Save, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface ServiceSectionProps {
  report: any;
  isEditable: boolean;
  onSave: () => Promise<void>;
  saving: boolean;
}

export default function ServiceSection({ report, isEditable, onSave, saving }: ServiceSectionProps) {
  const [activeTab, setActiveTab] = useState('activities');
  const [isAddingActivity, setIsAddingActivity] = useState(false);
  const [editingActivityId, setEditingActivityId] = useState<number | null>(null);
  
  // Form states
  const [activityForm, setActivityForm] = useState({
    service_type: 'departmental',
    committee_name: '',
    role: '',
    start_date: '',
    end_date: '',
    time_spent: ''
  });
  
  const [summaryForm, setSummaryForm] = useState({
    accomplishments: report?.service?.summary?.accomplishments || '',
    goals: report?.service?.summary?.goals || ''
  });
  
  // Reset activity form
  const resetActivityForm = () => {
    setActivityForm({
      service_type: 'departmental',
      committee_name: '',
      role: '',
      start_date: '',
      end_date: '',
      time_spent: ''
    });
    setIsAddingActivity(false);
    setEditingActivityId(null);
  };
  
  // Handle adding a service activity
  const handleAddActivity = async () => {
    if (!activityForm.committee_name) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    try {
      const response = await fetch('/api/merit-review/report/v2/service/activities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...activityForm
        }),
      });
      
      if (response.ok) {
        const newActivity = await response.json();
        
        // Update the report state with the new activity
        report.service.activities = [...(report.service.activities || []), newActivity];
        
        toast.success('Service activity added successfully');
        resetActivityForm();
        onSave();
      } else {
        toast.error('Failed to add service activity');
      }
    } catch (error) {
      console.error('Error adding service activity:', error);
      toast.error('An error occurred while adding the service activity');
    }
  };
  
  // Handle editing a service activity
  const handleEditActivity = (activity: any) => {
    setActivityForm({
      service_type: activity.service_type,
      committee_name: activity.committee_name,
      role: activity.role || '',
      start_date: activity.start_date ? new Date(activity.start_date).toISOString().split('T')[0] : '',
      end_date: activity.end_date ? new Date(activity.end_date).toISOString().split('T')[0] : '',
      time_spent: activity.time_spent || ''
    });
    setEditingActivityId(activity.id);
    setIsAddingActivity(true);
  };
  
  // Handle updating a service activity
  const handleUpdateActivity = async () => {
    if (!activityForm.committee_name) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    try {
      const response = await fetch('/api/merit-review/report/v2/service/activities', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingActivityId,
          report_id: report.id,
          ...activityForm
        }),
      });
      
      if (response.ok) {
        const updatedActivity = await response.json();
        
        // Update the report state with the updated activity
        report.service.activities = report.service.activities.map((a: any) => 
          a.id === updatedActivity.id ? updatedActivity : a
        );
        
        toast.success('Service activity updated successfully');
        resetActivityForm();
        onSave();
      } else {
        toast.error('Failed to update service activity');
      }
    } catch (error) {
      console.error('Error updating service activity:', error);
      toast.error('An error occurred while updating the service activity');
    }
  };
  
  // Handle deleting a service activity
  const handleDeleteActivity = async (id: number) => {
    if (!confirm('Are you sure you want to delete this service activity?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/merit-review/report/v2/service/activities?id=${id}&report_id=${report.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        // Update the report state by removing the deleted activity
        report.service.activities = report.service.activities.filter((a: any) => a.id !== id);
        
        toast.success('Service activity deleted successfully');
        onSave();
      } else {
        toast.error('Failed to delete service activity');
      }
    } catch (error) {
      console.error('Error deleting service activity:', error);
      toast.error('An error occurred while deleting the service activity');
    }
  };
  
  // Handle updating service summary
  const handleUpdateSummary = async () => {
    try {
      const response = await fetch('/api/merit-review/report/v2/service/summary', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...summaryForm
        }),
      });
      
      if (response.ok) {
        const updatedSummary = await response.json();
        
        // Update the report state with the updated summary
        report.service.summary = updatedSummary;
        
        toast.success('Service summary updated successfully');
        onSave();
      } else {
        toast.error('Failed to update service summary');
      }
    } catch (error) {
      console.error('Error updating summary:', error);
      toast.error('An error occurred while updating service summary');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Service</CardTitle>
          <CardDescription>
            Document your service activities for the review period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="activities">Activities</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>
            
            <TabsContent value="activities" className="space-y-4">
              {/* Service Activities Table */}
              {report.service?.activities && report.service.activities.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Type</TableHead>
                      <TableHead>Committee/Service</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Period</TableHead>
                      <TableHead>Time Spent</TableHead>
                      {isEditable && <TableHead>Actions</TableHead>}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {report.service.activities.map((activity: any) => (
                      <TableRow key={activity.id}>
                        <TableCell className="capitalize">{activity.service_type}</TableCell>
                        <TableCell>{activity.committee_name}</TableCell>
                        <TableCell>{activity.role}</TableCell>
                        <TableCell>
                          {activity.start_date ? new Date(activity.start_date).toLocaleDateString() : ''}
                          {activity.end_date ? ` - ${new Date(activity.end_date).toLocaleDateString()}` : ''}
                        </TableCell>
                        <TableCell>{activity.time_spent}</TableCell>
                        {isEditable && (
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => handleEditActivity(activity)}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => handleDeleteActivity(activity.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No service activities added yet.
                </div>
              )}
              
              {/* Add/Edit Service Activity Form */}
              {isEditable && (
                <>
                  {isAddingActivity ? (
                    <Card>
                      <CardHeader>
                        <CardTitle>{editingActivityId ? 'Edit Service Activity' : 'Add Service Activity'}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="service_type">Service Type*</Label>
                            <Select 
                              value={activityForm.service_type}
                              onValueChange={(value) => setActivityForm({...activityForm, service_type: value})}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="departmental">Departmental</SelectItem>
                                <SelectItem value="faculty">Faculty</SelectItem>
                                <SelectItem value="university">University</SelectItem>
                                <SelectItem value="external">External/Professional</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="committee_name">Committee/Service Name*</Label>
                            <Input 
                              id="committee_name"
                              value={activityForm.committee_name}
                              onChange={(e) => setActivityForm({...activityForm, committee_name: e.target.value})}
                              placeholder="e.g., Curriculum Committee"
                            />
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="role">Role</Label>
                            <Input 
                              id="role"
                              value={activityForm.role}
                              onChange={(e) => setActivityForm({...activityForm, role: e.target.value})}
                              placeholder="e.g., Chair, Member"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="start_date">Start Date</Label>
                            <Input 
                              id="start_date"
                              type="date"
                              value={activityForm.start_date}
                              onChange={(e) => setActivityForm({...activityForm, start_date: e.target.value})}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="end_date">End Date</Label>
                            <Input 
                              id="end_date"
                              type="date"
                              value={activityForm.end_date}
                              onChange={(e) => setActivityForm({...activityForm, end_date: e.target.value})}
                            />
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="time_spent">Time Spent</Label>
                            <Input 
                              id="time_spent"
                              value={activityForm.time_spent}
                              onChange={(e) => setActivityForm({...activityForm, time_spent: e.target.value})}
                              placeholder="e.g., 2 hours per week"
                            />
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Button 
                          variant="outline" 
                          onClick={resetActivityForm}
                        >
                          Cancel
                        </Button>
                        <Button 
                          onClick={editingActivityId ? handleUpdateActivity : handleAddActivity}
                        >
                          {editingActivityId ? 'Update Activity' : 'Add Activity'}
                        </Button>
                      </CardFooter>
                    </Card>
                  ) : (
                    <Button 
                      variant="outline" 
                      className="w-full"
                      onClick={() => setIsAddingActivity(true)}
                    >
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Service Activity
                    </Button>
                  )}
                </>
              )}
            </TabsContent>
            
            <TabsContent value="summary" className="space-y-4">
              {/* Service Summary Form */}
              <Card>
                <CardHeader>
                  <CardTitle>Service Summary</CardTitle>
                  <CardDescription>
                    Summarize your service accomplishments and goals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="accomplishments">Service Accomplishments</Label>
                      <Textarea 
                        id="accomplishments"
                        value={summaryForm.accomplishments}
                        onChange={(e) => setSummaryForm({...summaryForm, accomplishments: e.target.value})}
                        placeholder="Describe your service accomplishments during the review period..."
                        className="min-h-[150px]"
                        disabled={!isEditable}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="goals">Service Goals</Label>
                      <Textarea 
                        id="goals"
                        value={summaryForm.goals}
                        onChange={(e) => setSummaryForm({...summaryForm, goals: e.target.value})}
                        placeholder="Describe your service goals for the next review period..."
                        className="min-h-[150px]"
                        disabled={!isEditable}
                      />
                    </div>
                  </div>
                </CardContent>
                {isEditable && (
                  <CardFooter>
                    <Button 
                      onClick={handleUpdateSummary}
                      className="ml-auto"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
