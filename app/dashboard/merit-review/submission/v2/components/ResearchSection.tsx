'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PlusCircle, Pencil, Save, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface ResearchSectionProps {
  report: any;
  isEditable: boolean;
  onSave: () => Promise<void>;
  saving: boolean;
}

export default function ResearchSection({ report, isEditable, onSave, saving }: ResearchSectionProps) {
  const [activeTab, setActiveTab] = useState('publications');
  const [isAddingPublication, setIsAddingPublication] = useState(false);
  const [editingPublicationId, setEditingPublicationId] = useState<number | null>(null);
  const [isAddingGrant, setIsAddingGrant] = useState(false);
  const [editingGrantId, setEditingGrantId] = useState<number | null>(null);

  // Form states
  const [publicationForm, setPublicationForm] = useState({
    publication_type: 'journal',
    title: '',
    authors: '',
    venue: '',
    year: new Date().getFullYear(),
    doi: '',
    citation_count: '',
    is_highlighted: false
  });

  const [grantForm, setGrantForm] = useState({
    grant_type: 'research',
    pi_name: '',
    collaborators: '',
    title: '',
    agency: '',
    amount: '',
    installment_year: new Date().getFullYear(),
    share_percentage: '',
    status: 'awarded',
    submission_date: ''
  });

  const [researchForm, setResearchForm] = useState({
    year1: report?.research?.year1 || new Date().getFullYear(),
    year2: report?.research?.year2 || new Date().getFullYear() - 1,
    year3: report?.research?.year3 || new Date().getFullYear() - 2,
    ref_journal_papers_year1: report?.research?.ref_journal_papers_year1 || 0,
    ref_journal_papers_year2: report?.research?.ref_journal_papers_year2 || 0,
    ref_journal_papers_year3: report?.research?.ref_journal_papers_year3 || 0,
    ref_conf_papers_year1: report?.research?.ref_conf_papers_year1 || 0,
    ref_conf_papers_year2: report?.research?.ref_conf_papers_year2 || 0,
    ref_conf_papers_year3: report?.research?.ref_conf_papers_year3 || 0
  });

  const [summaryForm, setSummaryForm] = useState({
    accomplishments: report?.research?.summary?.accomplishments || '',
    goals: report?.research?.summary?.goals || ''
  });

  // Reset publication form
  const resetPublicationForm = () => {
    setPublicationForm({
      publication_type: 'journal',
      title: '',
      authors: '',
      venue: '',
      year: new Date().getFullYear(),
      doi: '',
      citation_count: '',
      is_highlighted: false
    });
    setIsAddingPublication(false);
    setEditingPublicationId(null);
  };

  // Reset grant form
  const resetGrantForm = () => {
    setGrantForm({
      grant_type: 'research',
      pi_name: '',
      collaborators: '',
      title: '',
      agency: '',
      amount: '',
      installment_year: new Date().getFullYear(),
      share_percentage: '',
      status: 'awarded',
      submission_date: ''
    });
    setIsAddingGrant(false);
    setEditingGrantId(null);
  };

  // Handle adding a publication
  const handleAddPublication = async () => {
    if (!publicationForm.title || !publicationForm.authors || !publicationForm.venue) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/research/publications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...publicationForm,
          citation_count: parseInt(publicationForm.citation_count) || 0
        }),
      });

      if (response.ok) {
        const newPublication = await response.json();

        // Update the report state with the new publication
        report.research.publications = [...(report.research.publications || []), newPublication];

        toast.success('Publication added successfully');
        resetPublicationForm();
        onSave();
      } else {
        toast.error('Failed to add publication');
      }
    } catch (error) {
      console.error('Error adding publication:', error);
      toast.error('An error occurred while adding the publication');
    }
  };

  // Handle editing a publication
  const handleEditPublication = (publication: any) => {
    setPublicationForm({
      publication_type: publication.publication_type,
      title: publication.title,
      authors: publication.authors,
      venue: publication.venue,
      year: publication.year,
      doi: publication.doi || '',
      citation_count: publication.citation_count?.toString() || '',
      is_highlighted: publication.is_highlighted || false
    });
    setEditingPublicationId(publication.id);
    setIsAddingPublication(true);
  };

  // Handle updating a publication
  const handleUpdatePublication = async () => {
    if (!publicationForm.title || !publicationForm.authors || !publicationForm.venue) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/research/publications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingPublicationId,
          report_id: report.id,
          ...publicationForm,
          citation_count: parseInt(publicationForm.citation_count) || 0
        }),
      });

      if (response.ok) {
        const updatedPublication = await response.json();

        // Update the report state with the updated publication
        report.research.publications = report.research.publications.map((p: any) =>
          p.id === updatedPublication.id ? updatedPublication : p
        );

        toast.success('Publication updated successfully');
        resetPublicationForm();
        onSave();
      } else {
        toast.error('Failed to update publication');
      }
    } catch (error) {
      console.error('Error updating publication:', error);
      toast.error('An error occurred while updating the publication');
    }
  };

  // Handle deleting a publication
  const handleDeletePublication = async (id: number) => {
    if (!confirm('Are you sure you want to delete this publication?')) {
      return;
    }

    try {
      const response = await fetch(`/api/merit-review/report/v2/research/publications?id=${id}&report_id=${report.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update the report state by removing the deleted publication
        report.research.publications = report.research.publications.filter((p: any) => p.id !== id);

        toast.success('Publication deleted successfully');
        onSave();
      } else {
        toast.error('Failed to delete publication');
      }
    } catch (error) {
      console.error('Error deleting publication:', error);
      toast.error('An error occurred while deleting the publication');
    }
  };

  // Handle updating research summary
  const handleUpdateSummary = async () => {
    try {
      const response = await fetch('/api/merit-review/report/v2/research/summary', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...summaryForm
        }),
      });

      if (response.ok) {
        const updatedSummary = await response.json();

        // Update the report state with the updated summary
        report.research.summary = updatedSummary;

        toast.success('Research summary updated successfully');
        onSave();
      } else {
        toast.error('Failed to update research summary');
      }
    } catch (error) {
      console.error('Error updating summary:', error);
      toast.error('An error occurred while updating research summary');
    }
  };

  // Handle adding a grant
  const handleAddGrant = async () => {
    if (!grantForm.title || !grantForm.pi_name || !grantForm.agency || !grantForm.amount) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/research/grants', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...grantForm,
          amount: parseFloat(grantForm.amount) || 0,
          share_percentage: parseFloat(grantForm.share_percentage) || 100
        }),
      });

      if (response.ok) {
        const newGrant = await response.json();

        // Update the report state with the new grant
        report.research.grants = [...(report.research.grants || []), newGrant];

        toast.success('Grant added successfully');
        resetGrantForm();
        onSave();
      } else {
        toast.error('Failed to add grant');
      }
    } catch (error) {
      console.error('Error adding grant:', error);
      toast.error('An error occurred while adding the grant');
    }
  };

  // Handle updating a grant
  const handleUpdateGrant = async () => {
    if (!grantForm.title || !grantForm.pi_name || !grantForm.agency || !grantForm.amount) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/merit-review/report/v2/research/grants', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingGrantId,
          report_id: report.id,
          ...grantForm,
          amount: parseFloat(grantForm.amount) || 0,
          share_percentage: parseFloat(grantForm.share_percentage) || 100
        }),
      });

      if (response.ok) {
        const updatedGrant = await response.json();

        // Update the report state with the updated grant
        report.research.grants = report.research.grants.map((g: any) =>
          g.id === updatedGrant.id ? updatedGrant : g
        );

        toast.success('Grant updated successfully');
        resetGrantForm();
        onSave();
      } else {
        toast.error('Failed to update grant');
      }
    } catch (error) {
      console.error('Error updating grant:', error);
      toast.error('An error occurred while updating the grant');
    }
  };

  // Handle updating research data
  const handleUpdateResearch = async () => {
    try {
      const response = await fetch('/api/merit-review/report/v2/research', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: report.id,
          ...researchForm
        }),
      });

      if (response.ok) {
        const updatedResearch = await response.json();

        // Update the report state with the updated research data
        report.research = {
          ...report.research,
          ...updatedResearch
        };

        toast.success('Research data updated successfully');
        onSave();
      } else {
        toast.error('Failed to update research data');
      }
    } catch (error) {
      console.error('Error updating research data:', error);
      toast.error('An error occurred while updating research data');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Research</CardTitle>
          <CardDescription>
            Document your research activities for the review period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="publications">Publications</TabsTrigger>
              <TabsTrigger value="grants">Grants</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>

            <TabsContent value="publications" className="space-y-4">
              {/* Publications Table */}
              {report.research?.publications && report.research.publications.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Type</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>Authors</TableHead>
                      <TableHead>Venue</TableHead>
                      <TableHead>Year</TableHead>
                      <TableHead>Citations</TableHead>
                      {isEditable && <TableHead>Actions</TableHead>}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {report.research.publications.map((publication: any) => (
                      <TableRow key={publication.id}>
                        <TableCell className="capitalize">{publication.publication_type}</TableCell>
                        <TableCell>{publication.title}</TableCell>
                        <TableCell>{publication.authors}</TableCell>
                        <TableCell>{publication.venue}</TableCell>
                        <TableCell>{publication.year}</TableCell>
                        <TableCell>{publication.citation_count}</TableCell>
                        {isEditable && (
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEditPublication(publication)}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeletePublication(publication.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No publications added yet.
                </div>
              )}

              {/* Add/Edit Publication Form */}
              {isEditable && (
                <>
                  {isAddingPublication ? (
                    <Card>
                      <CardHeader>
                        <CardTitle>{editingPublicationId ? 'Edit Publication' : 'Add Publication'}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="publication_type">Publication Type*</Label>
                            <Select
                              value={publicationForm.publication_type}
                              onValueChange={(value) => setPublicationForm({...publicationForm, publication_type: value})}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="journal">Journal</SelectItem>
                                <SelectItem value="conference">Conference</SelectItem>
                                <SelectItem value="book">Book</SelectItem>
                                <SelectItem value="book_chapter">Book Chapter</SelectItem>
                                <SelectItem value="patent">Patent</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="year">Year*</Label>
                            <Input
                              id="year"
                              type="number"
                              value={publicationForm.year}
                              onChange={(e) => setPublicationForm({...publicationForm, year: parseInt(e.target.value) || new Date().getFullYear()})}
                            />
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="title">Title*</Label>
                            <Input
                              id="title"
                              value={publicationForm.title}
                              onChange={(e) => setPublicationForm({...publicationForm, title: e.target.value})}
                              placeholder="Publication title"
                            />
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="authors">Authors*</Label>
                            <Input
                              id="authors"
                              value={publicationForm.authors}
                              onChange={(e) => setPublicationForm({...publicationForm, authors: e.target.value})}
                              placeholder="e.g., Smith, J., Jones, A., ..."
                            />
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="venue">Venue/Journal*</Label>
                            <Input
                              id="venue"
                              value={publicationForm.venue}
                              onChange={(e) => setPublicationForm({...publicationForm, venue: e.target.value})}
                              placeholder="e.g., IEEE Transactions on..."
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="doi">DOI</Label>
                            <Input
                              id="doi"
                              value={publicationForm.doi}
                              onChange={(e) => setPublicationForm({...publicationForm, doi: e.target.value})}
                              placeholder="e.g., 10.1109/..."
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="citation_count">Citation Count</Label>
                            <Input
                              id="citation_count"
                              type="number"
                              value={publicationForm.citation_count}
                              onChange={(e) => setPublicationForm({...publicationForm, citation_count: e.target.value})}
                              placeholder="e.g., 5"
                            />
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Button
                          variant="outline"
                          onClick={resetPublicationForm}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={editingPublicationId ? handleUpdatePublication : handleAddPublication}
                        >
                          {editingPublicationId ? 'Update Publication' : 'Add Publication'}
                        </Button>
                      </CardFooter>
                    </Card>
                  ) : (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setIsAddingPublication(true)}
                    >
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Publication
                    </Button>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="grants" className="space-y-4">
              {/* Grants Table */}
              {report.research?.grants && report.research.grants.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Type</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>PI</TableHead>
                      <TableHead>Agency</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Year</TableHead>
                      <TableHead>Status</TableHead>
                      {isEditable && <TableHead>Actions</TableHead>}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {report.research.grants.map((grant: any) => (
                      <TableRow key={grant.id}>
                        <TableCell className="capitalize">{grant.grant_type}</TableCell>
                        <TableCell>{grant.title}</TableCell>
                        <TableCell>{grant.pi_name}</TableCell>
                        <TableCell>{grant.agency}</TableCell>
                        <TableCell>${new Intl.NumberFormat().format(grant.amount)}</TableCell>
                        <TableCell>{grant.installment_year}</TableCell>
                        <TableCell className="capitalize">{grant.status}</TableCell>
                        {isEditable && (
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  setGrantForm({
                                    grant_type: grant.grant_type,
                                    pi_name: grant.pi_name,
                                    collaborators: grant.collaborators || '',
                                    title: grant.title,
                                    agency: grant.agency,
                                    amount: grant.amount.toString(),
                                    installment_year: grant.installment_year,
                                    share_percentage: grant.share_percentage?.toString() || '',
                                    status: grant.status,
                                    submission_date: grant.submission_date || ''
                                  });
                                  setEditingGrantId(grant.id);
                                  setIsAddingGrant(true);
                                }}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={async () => {
                                  if (confirm('Are you sure you want to delete this grant?')) {
                                    try {
                                      const response = await fetch(`/api/merit-review/report/v2/research/grants?id=${grant.id}&report_id=${report.id}`, {
                                        method: 'DELETE',
                                      });

                                      if (response.ok) {
                                        // Update the report state by removing the deleted grant
                                        report.research.grants = report.research.grants.filter((g: any) => g.id !== grant.id);

                                        toast.success('Grant deleted successfully');
                                        onSave();
                                      } else {
                                        toast.error('Failed to delete grant');
                                      }
                                    } catch (error) {
                                      console.error('Error deleting grant:', error);
                                      toast.error('An error occurred while deleting the grant');
                                    }
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No grants added yet.
                </div>
              )}

              {/* Add/Edit Grant Form */}
              {isEditable && (
                <>
                  {isAddingGrant ? (
                    <Card>
                      <CardHeader>
                        <CardTitle>{editingGrantId ? 'Edit Grant' : 'Add Grant'}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="grant_type">Grant Type*</Label>
                            <Select
                              value={grantForm.grant_type}
                              onValueChange={(value) => setGrantForm({...grantForm, grant_type: value})}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="research">Research</SelectItem>
                                <SelectItem value="equipment">Equipment</SelectItem>
                                <SelectItem value="infrastructure">Infrastructure</SelectItem>
                                <SelectItem value="training">Training</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="status">Status*</Label>
                            <Select
                              value={grantForm.status}
                              onValueChange={(value) => setGrantForm({...grantForm, status: value})}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="awarded">Awarded</SelectItem>
                                <SelectItem value="submitted">Submitted</SelectItem>
                                <SelectItem value="in_progress">In Progress</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="title">Title*</Label>
                            <Input
                              id="title"
                              value={grantForm.title}
                              onChange={(e) => setGrantForm({...grantForm, title: e.target.value})}
                              placeholder="Grant title"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="pi_name">Principal Investigator*</Label>
                            <Input
                              id="pi_name"
                              value={grantForm.pi_name}
                              onChange={(e) => setGrantForm({...grantForm, pi_name: e.target.value})}
                              placeholder="PI name"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="collaborators">Collaborators</Label>
                            <Input
                              id="collaborators"
                              value={grantForm.collaborators}
                              onChange={(e) => setGrantForm({...grantForm, collaborators: e.target.value})}
                              placeholder="e.g., Smith, J., Jones, A., ..."
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="agency">Funding Agency*</Label>
                            <Input
                              id="agency"
                              value={grantForm.agency}
                              onChange={(e) => setGrantForm({...grantForm, agency: e.target.value})}
                              placeholder="e.g., NSERC, CIHR, ..."
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="amount">Amount (CAD)*</Label>
                            <Input
                              id="amount"
                              value={grantForm.amount}
                              onChange={(e) => setGrantForm({...grantForm, amount: e.target.value})}
                              placeholder="e.g., 100000"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="installment_year">Installment Year*</Label>
                            <Input
                              id="installment_year"
                              type="number"
                              value={grantForm.installment_year}
                              onChange={(e) => setGrantForm({...grantForm, installment_year: parseInt(e.target.value) || new Date().getFullYear()})}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="share_percentage">Your Share (%)</Label>
                            <Input
                              id="share_percentage"
                              value={grantForm.share_percentage}
                              onChange={(e) => setGrantForm({...grantForm, share_percentage: e.target.value})}
                              placeholder="e.g., 100"
                            />
                          </div>
                          {grantForm.status === 'submitted' && (
                            <div className="space-y-2">
                              <Label htmlFor="submission_date">Submission Date</Label>
                              <Input
                                id="submission_date"
                                type="date"
                                value={grantForm.submission_date}
                                onChange={(e) => setGrantForm({...grantForm, submission_date: e.target.value})}
                              />
                            </div>
                          )}
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Button
                          variant="outline"
                          onClick={resetGrantForm}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={editingGrantId ? handleUpdateGrant : handleAddGrant}
                        >
                          {editingGrantId ? 'Update Grant' : 'Add Grant'}
                        </Button>
                      </CardFooter>
                    </Card>
                  ) : (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setIsAddingGrant(true)}
                    >
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Grant
                    </Button>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="summary" className="space-y-4">
              {/* Research Summary Form */}
              <Card>
                <CardHeader>
                  <CardTitle>Research Summary</CardTitle>
                  <CardDescription>
                    Summarize your research accomplishments and goals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="accomplishments">Research Accomplishments</Label>
                      <Textarea
                        id="accomplishments"
                        value={summaryForm.accomplishments}
                        onChange={(e) => setSummaryForm({...summaryForm, accomplishments: e.target.value})}
                        placeholder="Describe your research accomplishments during the review period..."
                        className="min-h-[150px]"
                        disabled={!isEditable}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="goals">Research Goals</Label>
                      <Textarea
                        id="goals"
                        value={summaryForm.goals}
                        onChange={(e) => setSummaryForm({...summaryForm, goals: e.target.value})}
                        placeholder="Describe your research goals for the next review period..."
                        className="min-h-[150px]"
                        disabled={!isEditable}
                      />
                    </div>
                  </div>
                </CardContent>
                {isEditable && (
                  <CardFooter>
                    <Button
                      onClick={handleUpdateSummary}
                      className="ml-auto"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
