'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

import {
  CheckCircle2,
  ClipboardList,
  FileText,
  GraduationCap,
  Award,
  BookOpen,
  Users,
  MessageSquare,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import TeachingSection from '../components/TeachingSection';
import ResearchSection from '../components/ResearchSection';
import ServiceSection from '../components/ServiceSection';
import AwardsSection from '../components/AwardsSection';
import CommentsSection from '../components/CommentsSection';
import SubmissionSummary from '../components/SubmissionSummary';

export default function AdminMeritReviewSubmissionPage() {
  const { status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const reportId = searchParams.get('id');
  const facultyName = searchParams.get('faculty') || 'Faculty Member';

  const [report, setReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('summary');

  // Fetch report data
  useEffect(() => {
    const fetchReport = async () => {
      if (status === 'unauthenticated') {
        router.push('/login');
        return;
      }

      if (status === 'authenticated') {
        if (!reportId) {
          toast.error('No report ID provided');
          router.push('/dashboard/merit-review/submissions');
          return;
        }

        try {
          setLoading(true);
          const response = await fetch(`/api/merit-review/report/admin?id=${reportId}`);

          if (response.ok) {
            const data = await response.json();
            setReport(data);
          } else {
            const errorData = await response.json();
            toast.error(errorData.error || 'Failed to fetch report');
            setTimeout(() => router.push('/dashboard/merit-review/submissions'), 2000);
          }
        } catch (error) {
          console.error('Error fetching report:', error);
          toast.error('An error occurred while fetching the report');
        } finally {
          setLoading(false);
        }
      }
    };

    fetchReport();
  }, [status, reportId, router]);

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    const statusColors: Record<string, string> = {
      'draft': 'bg-yellow-50 text-yellow-700 border-yellow-200',
      'in_progress': 'bg-blue-50 text-blue-700 border-blue-200',
      'submitted': 'bg-green-50 text-green-700 border-green-200',
      'under_review': 'bg-purple-50 text-purple-700 border-purple-200',
      'reviewed': 'bg-indigo-50 text-indigo-700 border-indigo-200',
      'approved': 'bg-emerald-50 text-emerald-700 border-emerald-200'
    };

    const statusNames: Record<string, string> = {
      'draft': 'Draft',
      'in_progress': 'In Progress',
      'submitted': 'Submitted',
      'under_review': 'Under Review',
      'reviewed': 'Reviewed',
      'approved': 'Approved'
    };

    return (
      <Badge variant="outline" className={statusColors[status] || 'bg-gray-50 text-gray-700 border-gray-200'}>
        {statusNames[status] || status}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          <p className="ml-4 text-lg">Loading report...</p>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Report Not Found</AlertTitle>
          <AlertDescription>
            The requested report could not be found or you don't have permission to view it.
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/dashboard/merit-review/submissions')}>
            Return to Submissions
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Merit Report</h1>
          <p className="text-muted-foreground">
            {report.faculty_name} - {report.report_type} {report.report_year} - {report.unit_name}
          </p>
        </div>
        <div className="flex items-center mt-2 md:mt-0">
          <p className="text-sm text-muted-foreground mr-2">Status:</p>
          {getStatusBadge(report.status)}
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Report Information</CardTitle>
          <CardDescription>
            Basic information about this merit report
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Faculty</p>
              <p className="text-sm">{report.faculty_name}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Report Type</p>
              <p className="text-sm">{report.report_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Report Year</p>
              <p className="text-sm">{report.report_year}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Department/Unit</p>
              <p className="text-sm">{report.unit_name}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Last Updated</p>
              <p className="text-sm">{formatDate(report.update_dt)}</p>
            </div>
            {report.submit_dt && (
              <div>
                <p className="text-sm font-medium">Submitted</p>
                <p className="text-sm">{formatDate(report.submit_dt)}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-6 gap-2">
          <TabsTrigger value="summary" className="flex items-center">
            <ClipboardList className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Summary</span>
          </TabsTrigger>
          <TabsTrigger value="teaching" className="flex items-center">
            <GraduationCap className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Teaching</span>
          </TabsTrigger>
          <TabsTrigger value="research" className="flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Research</span>
          </TabsTrigger>
          <TabsTrigger value="service" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Service</span>
          </TabsTrigger>
          <TabsTrigger value="awards" className="flex items-center">
            <Award className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Awards</span>
          </TabsTrigger>
          <TabsTrigger value="comments" className="flex items-center">
            <MessageSquare className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">Comments</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary">
          <SubmissionSummary
            report={report}
            isEditable={false}
            onSubmit={async () => {/* No-op, admin view is read-only */}}
            submitting={false}
          />
        </TabsContent>

        <TabsContent value="teaching">
          <TeachingSection
            report={report}
            isEditable={false}
            onSave={async () => {/* No-op, admin view is read-only */}}
            saving={false}
          />
        </TabsContent>

        <TabsContent value="research">
          <ResearchSection
            report={report}
            isEditable={false}
            onSave={async () => {/* No-op, admin view is read-only */}}
            saving={false}
          />
        </TabsContent>

        <TabsContent value="service">
          <ServiceSection
            report={report}
            isEditable={false}
            onSave={async () => {/* No-op, admin view is read-only */}}
            saving={false}
          />
        </TabsContent>

        <TabsContent value="awards">
          <AwardsSection
            report={report}
            isEditable={false}
            onSave={async () => {/* No-op, admin view is read-only */}}
            saving={false}
          />
        </TabsContent>

        <TabsContent value="comments">
          <CommentsSection
            report={report}
            isEditable={false}
            onSave={async () => {/* No-op, admin view is read-only */}}
            saving={false}
          />
        </TabsContent>
      </Tabs>

      <div className="mt-6 flex justify-end">
        <Button
          variant="outline"
          onClick={() => router.push('/dashboard/merit-review/submissions')}
        >
          Back to Submissions
        </Button>
      </div>
    </div>
  );
}
