"use client";

import React from 'react';
import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// Button import removed as it's not used
import { lusitana } from "@/app/ui/fonts";
import { Loader2 } from "lucide-react";
import QuestionDescriptions from "./components/QuestionDescriptions";

interface CourseEvaluation {
  term_id: number;
  course_id: string;
  course_title: string;
  userid: string;
  gender: string;
  first_name: string;
  last_name: string;
  faculty_name: string;
  department: string;
  section: number;
  q1_responses: number;
  q1_avg: number;
  q1_std: number;
  q2_responses: number;
  q2_avg: number;
  q2_std: number;
  q3_responses: number;
  q3_avg: number;
  q3_std: number;
  q4_responses: number;
  q4_avg: number;
  q4_std: number;
  q5_responses: number;
  q5_avg: number;
  q5_std: number;
  q6_responses: number;
  q6_avg: number;
  q6_std: number;
  class_size: number;
  faculty_id: number;
  faculty_first_name: string;
  faculty_last_name: string;
  work_email: string;
}

export default function CourseEvaluationsPage() {
  const { status } = useSession(); // We only need status for authentication check
  const router = useRouter();
  const [evaluations, setEvaluations] = useState<CourseEvaluation[]>([]);
  const [filteredEvaluations, setFilteredEvaluations] = useState<CourseEvaluation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [termFilter, setTermFilter] = useState<string>("all");
  const [uniqueTerms, setUniqueTerms] = useState<number[]>([]);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Fetch course evaluations data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/merit-review/course-evaluations");
        if (!response.ok) {
          throw new Error("Failed to fetch course evaluations");
        }
        const data = await response.json();
        setEvaluations(data);
        setFilteredEvaluations(data);

        // Extract unique terms for filtering
        const terms = [...new Set(data.map((item: CourseEvaluation) => item.term_id))] as number[];
        setUniqueTerms(terms.sort((a, b) => b - a)); // Sort in descending order

        setLoading(false);
      } catch (error) {
        console.error("Error fetching course evaluations:", error);
        setLoading(false);
      }
    };

    if (status === "authenticated") {
      fetchData();
    }
  }, [status]);

  // Filter evaluations based on search term and term filter
  useEffect(() => {
    let filtered = evaluations;

    // Apply term filter
    if (termFilter !== "all") {
      const termFilterNumber = parseInt(termFilter);

      filtered = filtered.filter(item => {
        // Try multiple comparison approaches to handle potential type issues
        return (
          item.term_id === termFilterNumber || // Direct comparison
          String(item.term_id) === String(termFilterNumber) || // String comparison
          Number(item.term_id) === termFilterNumber // Number comparison
        );
      });
    }

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.course_id?.toLowerCase().includes(search) ||
          item.first_name?.toLowerCase().includes(search) ||
          item.last_name?.toLowerCase().includes(search) ||
          `${item.first_name} ${item.last_name}`.toLowerCase().includes(search) ||
          item.department?.toLowerCase().includes(search)
      );
    }

    setFilteredEvaluations(filtered);
  }, [searchTerm, termFilter, evaluations]);

  // Format term ID for display (e.g., 1271 -> "Winter 2027", 0939 -> "Fall 1993")
  const formatTerm = (termId: number): string => {
    if (!termId) return "Unknown";

    const termString = termId.toString();
    if (termString.length !== 4) return termString;

    // First digit indicates century (0 for 1900s, 1 for 2000s)
    // Second and third digits represent the year
    // Last digit indicates the term: 1 = January (Winter), 5 = May (Spring), 9 = September (Fall)
    const century = termString.charAt(0) === '0' ? '19' : '20';
    const year = century + termString.substring(1, 3);
    const termDigit = termString.charAt(3);

    let season = "Unknown";
    switch (termDigit) {
      case "1":
        season = "Winter";
        break;
      case "5":
        season = "Spring";
        break;
      case "9":
        season = "Fall";
        break;
      default:
        // Handle any other term digits that might appear
        season = "Unknown";
    }

    return `${season} ${year}`;
  };

  // Format average score with color coding
  const formatAvg = (avg: number): React.ReactElement => {
    if (!avg && avg !== 0) return <span>-</span>;

    let color = "text-gray-700";
    if (avg >= 4.5) color = "text-green-600 font-semibold";
    else if (avg >= 4.0) color = "text-green-500";
    else if (avg >= 3.5) color = "text-yellow-600";
    else if (avg < 3.0) color = "text-red-500 font-semibold";

    return <span className={color}>{avg.toFixed(2)}</span>;
  };

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      

      <QuestionDescriptions />

      <Card>
        <CardHeader>
          <CardTitle>Course Evaluation Results</CardTitle>
          <CardDescription>
            View and analyze course evaluation data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search by course ID, instructor name, or department..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="w-full md:w-48">
              <Select
                value={termFilter}
                onValueChange={setTermFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by term" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Terms</SelectItem>
                  {uniqueTerms.map((term) => (
                    <SelectItem key={term} value={term.toString()}>
                      {formatTerm(term)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading course evaluations...</span>
            </div>
          ) : filteredEvaluations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No course evaluations found.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Term</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead>Section</TableHead>
                    <TableHead>Instructor</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead className="text-center">Class Size</TableHead>
                    <TableHead className="text-center">Q1 Avg</TableHead>
                    <TableHead className="text-center">Q2 Avg</TableHead>
                    <TableHead className="text-center">Q3 Avg</TableHead>
                    <TableHead className="text-center">Q4 Avg</TableHead>
                    <TableHead className="text-center">Q5 Avg</TableHead>
                    <TableHead className="text-center">Q6 Avg</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEvaluations.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{formatTerm(item.term_id)}</TableCell>
                      <TableCell>{item.course_id}</TableCell>
                      <TableCell>{item.section}</TableCell>
                      <TableCell>
                        {item.first_name} {item.last_name}
                      </TableCell>
                      <TableCell>{item.department}</TableCell>
                      <TableCell className="text-center">{item.class_size || "-"}</TableCell>
                      <TableCell className="text-center">{formatAvg(item.q1_avg)}</TableCell>
                      <TableCell className="text-center">{formatAvg(item.q2_avg)}</TableCell>
                      <TableCell className="text-center">{formatAvg(item.q3_avg)}</TableCell>
                      <TableCell className="text-center">{formatAvg(item.q4_avg)}</TableCell>
                      <TableCell className="text-center">{formatAvg(item.q5_avg)}</TableCell>
                      <TableCell className="text-center">{formatAvg(item.q6_avg)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
