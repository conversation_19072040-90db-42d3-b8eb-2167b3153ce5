import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

export default function QuestionDescriptions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Course Evaluation Questions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 text-sm">
          <p><strong>Q1:</strong> The instructor explained course concepts clearly.</p>
          <p><strong>Q2:</strong> The instructor was well prepared for class.</p>
          <p><strong>Q3:</strong> The instructor provided constructive feedback throughout this course.</p>
          <p><strong>Q4:</strong> The instructor stimulated my interest in the subject matter.</p>
          <p><strong>Q5:</strong> The instructor created an atmosphere that was conducive to learning.</p>
          <p><strong>Q6:</strong> Overall, the instructor was an effective teacher.</p>
          <p className="mt-4 text-xs text-muted-foreground">
            Note: Scores range from 1 (strongly disagree) to 5 (strongly agree).
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
