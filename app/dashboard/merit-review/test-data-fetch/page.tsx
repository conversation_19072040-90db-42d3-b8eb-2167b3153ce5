'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export default function TestDataFetchPage() {
  const [courseEvaluations, setCourseEvaluations] = useState<any[]>([]);
  const [supervisionData, setSupervisionData] = useState<any>(null);
  const [loading, setLoading] = useState<{ courses: boolean; supervision: boolean }>({
    courses: false,
    supervision: false,
  });
  const [error, setError] = useState<{ courses: string | null; supervision: string | null }>({
    courses: null,
    supervision: null,
  });

  const fetchCourseEvaluations = async () => {
    setLoading(prev => ({ ...prev, courses: true }));
    setError(prev => ({ ...prev, courses: null }));
    
    try {
      const response = await fetch('/api/merit-review/faculty-course-evaluations');
      if (response.ok) {
        const data = await response.json();
        setCourseEvaluations(data);
      } else {
        const errorData = await response.json();
        setError(prev => ({ ...prev, courses: errorData.error || 'Failed to fetch course evaluations' }));
      }
    } catch (error) {
      console.error('Error fetching course evaluations:', error);
      setError(prev => ({ ...prev, courses: 'An error occurred while fetching course evaluations' }));
    } finally {
      setLoading(prev => ({ ...prev, courses: false }));
    }
  };

  const fetchSupervisionData = async () => {
    setLoading(prev => ({ ...prev, supervision: true }));
    setError(prev => ({ ...prev, supervision: null }));
    
    try {
      const response = await fetch('/api/merit-review/faculty-supervision');
      if (response.ok) {
        const data = await response.json();
        setSupervisionData(data);
      } else {
        const errorData = await response.json();
        setError(prev => ({ ...prev, supervision: errorData.error || 'Failed to fetch supervision data' }));
      }
    } catch (error) {
      console.error('Error fetching supervision data:', error);
      setError(prev => ({ ...prev, supervision: 'An error occurred while fetching supervision data' }));
    } finally {
      setLoading(prev => ({ ...prev, supervision: false }));
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Test Data Fetch</h1>
      
      <Tabs defaultValue="courses" className="space-y-6">
        <TabsList>
          <TabsTrigger value="courses">Course Evaluations</TabsTrigger>
          <TabsTrigger value="supervision">Supervision Data</TabsTrigger>
        </TabsList>
        
        <TabsContent value="courses">
          <Card>
            <CardHeader>
              <CardTitle>Course Evaluations</CardTitle>
              <CardDescription>
                Test fetching course evaluations data for the current faculty user
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={fetchCourseEvaluations} 
                disabled={loading.courses}
              >
                {loading.courses ? 'Loading...' : 'Fetch Course Evaluations'}
              </Button>
              
              {error.courses && (
                <div className="p-4 bg-red-50 text-red-700 rounded-md">
                  {error.courses}
                </div>
              )}
              
              {courseEvaluations.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-lg font-medium mb-2">Results ({courseEvaluations.length} courses)</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Term</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SCP Q1-3</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SCP Q4-6</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response %</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {courseEvaluations.map((course, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{course.term_year}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{course.course_id}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{course.course_title}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{course.scp_q1_q3}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{course.scp_q4_q6}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{course.class_size}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{course.response_percentage}%</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="supervision">
          <Card>
            <CardHeader>
              <CardTitle>Supervision Data</CardTitle>
              <CardDescription>
                Test fetching supervision data for the current faculty user
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={fetchSupervisionData} 
                disabled={loading.supervision}
              >
                {loading.supervision ? 'Loading...' : 'Fetch Supervision Data'}
              </Button>
              
              {error.supervision && (
                <div className="p-4 bg-red-50 text-red-700 rounded-md">
                  {error.supervision}
                </div>
              )}
              
              {supervisionData && (
                <div className="mt-4 space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Summary</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-gray-50 rounded-md">
                        <p className="text-sm font-medium">Undergraduate Students</p>
                        <p className="text-2xl font-bold">{supervisionData.summary.undergrad_supervised}</p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-md">
                        <p className="text-sm font-medium">Master's Students</p>
                        <p className="text-2xl font-bold">{supervisionData.summary.masters_supervised}</p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-md">
                        <p className="text-sm font-medium">PhD Students</p>
                        <p className="text-2xl font-bold">{supervisionData.summary.phd_supervised}</p>
                      </div>
                    </div>
                  </div>
                  
                  {supervisionData.supervisions.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium mb-2">Student Details ({supervisionData.supervisions.length} students)</h3>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Term</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student Name</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Academic Plan</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {supervisionData.supervisions.map((student: any) => (
                              <tr key={student.supervision_id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{student.term}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{student.student_id}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{student.student_name}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{student.academic_plan}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{student.department}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
