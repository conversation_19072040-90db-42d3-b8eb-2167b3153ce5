'use client';
import { FilePond } from 'react-filepond';
import 'filepond/dist/filepond.min.css';
import { useState } from 'react';

import { RawExtraction } from '@/app/lib/processLongText';

// Create tabs for Publications, Activities, and Raw Text
type TabType = 'publications' | 'activities' | 'rawText';

export default function FileUpload() {
  const [activeTab, setActiveTab] = useState<TabType>('publications');
  const [responseData, setResponseData] = useState<{
    publications: RawExtraction[];
    activities: RawExtraction[];
    rawText: string;
  }>({
    publications: [],
    activities: [],
    rawText: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  return (
    <div className="flex flex-col gap-4 p-4">
      <h1 className="text-2xl font-bold">Upload PDF for Analysis</h1>
      
      <div className="w-full">
        <FilePond
          server={{
            process: {
              url: '/api/upload',
              method: 'POST',
              onload: (res) => {
                // `res` is the raw response body as a string
                try {
                  console.log('Response received');
                  const data = JSON.parse(res);
                  setResponseData({
                    publications: data.publications || [],
                    activities: data.activities || [],
                    rawText: data.rawText || 'No text extracted',
                  });
                  setIsLoading(false);
                } catch (error) {
                  console.error('Error parsing response:', error);
                  setResponseData({
                    publications: [],
                    activities: [],
                    rawText: 'Error parsing response',
                  });
                  setIsLoading(false);
                }
                return 'success'; // Return value for FilePond
              },
              ondata: (formData) => {
                setIsLoading(true);
                return formData;
              },
              onerror: (error) => {
                console.error('Upload error:', error);
                setResponseData({
                  publications: [],
                  activities: [],
                  rawText: 'Error uploading file',
                });
                setIsLoading(false);
                return 'error';
              },
            },
            fetch: null,
            revert: null,
          }}
          credits={false}
          labelIdle='Drag & Drop your PDF or <span class="filepond--label-action">Browse</span>'
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          <p className="ml-4 text-lg">Processing PDF with AI...</p>
        </div>
      ) : (
        <div className="w-full">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex -mb-px" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('publications')}
                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                  activeTab === 'publications'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Publications ({responseData.publications.length})
              </button>
              <button
                onClick={() => setActiveTab('activities')}
                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                  activeTab === 'activities'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Activities ({responseData.activities.length})
              </button>
              <button
                onClick={() => setActiveTab('rawText')}
                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                  activeTab === 'rawText'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Raw Text
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="mt-4">
            {/* Publications Tab */}
            {activeTab === 'publications' && (
              <div className="flow-root">
                <div className="inline-block min-w-full align-middle">
                  <div className="rounded-lg bg-gray-50 p-2 md:pt-0">
                    {responseData.publications.length === 0 ? (
                      <p className="text-center py-4 text-gray-500">No publications found</p>
                    ) : (
                      <ul className="divide-y divide-gray-200">
                        {responseData.publications.map((publication, index) => (
                          <li key={index} className="px-4 py-4 bg-white sm:px-6">
                            <p className="text-sm text-gray-900 break-words">{publication.rawText}</p>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Activities Tab */}
            {activeTab === 'activities' && (
              <div className="flow-root">
                <div className="inline-block min-w-full align-middle">
                  <div className="rounded-lg bg-gray-50 p-2 md:pt-0">
                    {responseData.activities.length === 0 ? (
                      <p className="text-center py-4 text-gray-500">No activities found</p>
                    ) : (
                      <ul className="divide-y divide-gray-200">
                        {responseData.activities.map((activity, index) => (
                          <li key={index} className="px-4 py-4 bg-white sm:px-6">
                            <p className="text-sm text-gray-900 break-words">{activity.rawText}</p>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Raw Text Tab */}
            {activeTab === 'rawText' && (
              <div>
                <textarea
                  className="w-full h-64 p-4 border rounded-lg bg-gray-50 resize-none"
                  value={responseData.rawText}
                  readOnly
                  placeholder="Upload a PDF file to see extracted text here..."
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}