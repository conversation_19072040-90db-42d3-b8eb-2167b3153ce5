import { lusitana } from '@/app/ui/fonts';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import UserProfile from './components/UserProfile';
import SessionInfo from '@/app/components/test/SessionInfo';

export default async function Page() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return (
      <div className="w-full">
        <div className="flex w-full items-center justify-between">
          <h1 className={`${lusitana.className} text-2xl`}>Profile</h1>
        </div>
        <div className="mt-4">
          <p className="text-gray-600">Please sign in to view your profile.</p>
        </div>
      </div>
    );
  }

  // Check if user is a faculty member
  const facultyCheck = await sql`
    SELECT faculty_id FROM uw.faculty
    WHERE work_email = ${session.user.email} AND is_deleted = FALSE
  `;
  console.log('Faculty check result:', facultyCheck);
  const isFaculty = facultyCheck.length > 0;
  console.log('Is faculty:', isFaculty);

  return (
    <div className="w-full">
      <div className="flex w-full items-center justify-between">
        <h1 className={`${lusitana.className} text-2xl`}>Profile</h1>
      </div>
      <div className="mt-4 space-y-6">
        <UserProfile isFaculty={isFaculty} />
        {/* Test component to verify session data - only shown in non-production environments */}
        {process.env.NODE_ENV !== 'production' && <SessionInfo />}
      </div>
    </div>
  );
}