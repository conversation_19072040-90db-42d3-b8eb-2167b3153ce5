"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/app/components/ui/skeleton";

interface FacultyProfile {
  intelicampus_id: string;
  sso_id: string;
  first_name: string;
  last_name: string;
  work_email: string;
  primary_unit_id: number;
  unit_name: string;
  primary_unit_percentage: number;
  tenure_status: string;
  position_id: number;
  job_family: string;
  rank: string;
  start_date: string;
  end_date: string | null;
  is_active: boolean;
}

export default function FacultyProfile() {
  const [profile, setProfile] = useState<FacultyProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch('/api/profile/faculty');
        if (!response.ok) {
          throw new Error('Failed to fetch faculty profile');
        }
        const data = await response.json();
        setProfile(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-4 w-[300px]" />
        <Skeleton className="h-4 w-[250px]" />
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  if (!profile) {
    return <div className="text-gray-500">No faculty profile found</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Intelicampus ID</p>
            <p>{profile.intelicampus_id}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">SSO ID</p>
            <p>{profile.sso_id}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">First Name</p>
            <p>{profile.first_name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Last Name</p>
            <p>{profile.last_name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Work Email</p>
            <p>{profile.work_email}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Employment Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Primary Unit</p>
            <p>{profile.unit_name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">FTE</p>
            <p>{profile.primary_unit_percentage}%</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Tenure Status</p>
            <p>{profile.tenure_status}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Job Family</p>
            <p>{profile.job_family}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Rank</p>
            <p>{profile.rank}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Status</p>
            <p>{profile.is_active ? 'Active' : 'Inactive'}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Start Date</p>
            <p>{new Date(profile.start_date).toLocaleDateString()}</p>
          </div>
          {profile.end_date && (
            <div>
              <p className="text-sm font-medium text-gray-500">End Date</p>
              <p>{new Date(profile.end_date).toLocaleDateString()}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 