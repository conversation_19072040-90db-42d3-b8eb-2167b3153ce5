"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/app/components/ui/skeleton";
import { useSession } from "next-auth/react";
import ProfileFacultyInfo from "./ProfileFacultyInfo";

// We've removed the FacultyProfile interface since we're only using EngRecordsFaculty

interface EngRecordsFaculty {
  nexus: string;
  first_name: string;
  last_name: string;
  appt_title: string;
  descr: string;
  fac_org_unit: string;
  unit_full_name: string;
  appt_type: string;
  track_type: string;
  fac_group: string;
  fac_rank_desc: string;
  is_active: boolean;
}

interface ScholarProfile {
  scholar_id: string;
  name: string;
  affiliation: string;
  areas_of_interest: string;
  citations_all: number;
  h_index_all: number;
  profile_url: string;
  profile_image_url: string;
}

interface UserProfileProps {
  isFaculty: boolean;
}

export default function UserProfile({ isFaculty }: UserProfileProps) {
  const { data: session } = useSession();
  // We only use the engRecordsFaculty data for the Personal Information section
  const [engRecordsFaculty, setEngRecordsFaculty] = useState<EngRecordsFaculty | null>(null);
  const [scholarProfile, setScholarProfile] = useState<ScholarProfile | null>(null);
  const [displayName, setDisplayName] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch display name from common.user table
        if (session?.user?.id) {
          const displayNameResponse = await fetch(`/api/profile/display-name?userId=${session.user.id}`);
          if (displayNameResponse.ok) {
            const { displayName } = await displayNameResponse.json();
            setDisplayName(displayName);
          }
        }

        console.log('isFaculty1:', isFaculty);
        // Fetch faculty profile if user is faculty
        if (isFaculty) {
          // We don't need to fetch the regular faculty profile anymore
          // since we're using the EngRecords data

          // Fetch EngRecords faculty data
          console.log('Fetching EngRecords faculty data...');
          const engRecordsResponse = await fetch('/api/profile/engrecords-faculty');
          console.log('EngRecords response status:', engRecordsResponse.status);

          if (engRecordsResponse.ok) {
            const engRecordsData = await engRecordsResponse.json();
            console.log('EngRecords data:', engRecordsData);

            if (engRecordsData) {
              setEngRecordsFaculty(engRecordsData);
              console.log('Set engRecordsFaculty state with data');
            } else {
              console.log('No EngRecords data returned');
            }
          } else {
            console.error('Failed to fetch EngRecords faculty data:', engRecordsResponse.statusText);
          }

          // Fetch Google Scholar profile
          console.log('Fetching Google Scholar profile...');
          const scholarResponse = await fetch('/api/profile/scholar');
          console.log('Google Scholar response status:', scholarResponse.status);

          if (scholarResponse.ok) {
            const scholarData = await scholarResponse.json();
            console.log('Google Scholar data:', scholarData);

            if (scholarData) {
              setScholarProfile(scholarData);
              console.log('Set scholar profile state with data');
            } else {
              console.log('No Google Scholar data returned');
            }
          } else {
            console.error('Failed to fetch Google Scholar profile:', scholarResponse.statusText);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isFaculty, session?.user?.id]);

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-4 w-[300px]" />
        <Skeleton className="h-4 w-[250px]" />
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-6">
          {/* Profile Photo on the left */}
          <div className="flex-shrink-0">
            {scholarProfile && scholarProfile.profile_image_url ? (
              <div className="h-24 w-24 rounded-full overflow-hidden">
                <img
                  src={scholarProfile.profile_image_url}
                  alt={displayName || session?.user?.name || 'Profile'}
                  className="h-full w-full object-cover"
                  onError={(e) => {
                    console.error('Error loading image:', e);
                    // Hide the image if it fails to load
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            ) : (
              <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-500">
                  {(displayName || session?.user?.name || '?').charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>

          {/* User Information on the right */}
          <div className="flex flex-col space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p>{session?.user?.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Name</p>
              <p>{displayName || session?.user?.name || 'Not set'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {isFaculty && engRecordsFaculty ? (
        <ProfileFacultyInfo faculty={engRecordsFaculty} />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              You are not registered as a faculty member. If you believe this is an error, please contact your administrator.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}