'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";

interface Faculty {
  nexus: string;
  first_name: string;
  last_name: string;
  appt_title: string;
  descr: string;
  fac_org_unit: string;
  unit_full_name: string;
  appt_type: string;
  track_type: string;
  fac_group: string;
  fac_rank_desc: string;
  is_active: boolean;
}

export default function ProfileFacultyInfo({ faculty }: { faculty: Faculty }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="rounded-lg bg-gray-50 p-6">
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center">
              <span className="font-medium mr-2">Title:</span>
              <span>{faculty.appt_title}</span>
            </div>
            <div className="flex items-center">
              <span className="font-medium mr-2">Faculty Group:</span>
              <span>
                {['Part-time Faculty', 'NonFaculty', 'Faculty'].includes(faculty.fac_group) ? faculty.fac_group : ''}
              </span>
            </div>
            <div className="flex items-center">
              <span className="font-medium mr-2">Rank:</span>
              <span>{faculty.fac_rank_desc || ''}</span>
            </div>
            <div className="flex items-center">
              <span className="font-medium mr-2">Organization Unit:</span>
              <span>{faculty.unit_full_name || faculty.fac_org_unit}</span>
            </div>

          </div>
        </div>
      </CardContent>
    </Card>
  );
}
