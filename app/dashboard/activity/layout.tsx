'use client';

import { Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect } from 'react';
import Breadcrumbs from '@/app/ui/common/breadcrumbs';

export default function ActivityLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Generate breadcrumbs based on the current path
  const getBreadcrumbs = () => {
    // Check if pathname is defined
    if (!pathname) {
      return [{ label: "Activity", href: "/dashboard/activity", active: true }];
    }

    const paths = pathname.split('/').filter(Boolean);

    // For Merit Review and Activity, don't include "Dashboard" in breadcrumb
    const breadcrumbs = [
      { label: 'Activity', href: '/dashboard/activity', active: true },
    ];

    // Add additional breadcrumbs for subpaths
    if (paths.length > 2) {
      const subpath = paths[2];
      breadcrumbs[0].active = false;

      if (subpath === 'teaching-load') {
        breadcrumbs.push({
          label: 'Teaching Load',
          href: '/dashboard/activity/teaching-load',
          active: true,
        });
      } else if (subpath === 'supervision') {
        breadcrumbs.push({
          label: 'Supervision',
          href: '/dashboard/activity/supervision',
          active: true,
        });
      } else if (subpath === 'committee-service') {
        breadcrumbs.push({
          label: 'Committee Service',
          href: '/dashboard/activity/committee-service',
          active: true,
        });
      } else if (subpath === 'professional-service') {
        breadcrumbs.push({
          label: 'Professional Service',
          href: '/dashboard/activity/professional-service',
          active: true,
        });
      } else if (subpath === 'community-service') {
        breadcrumbs.push({
          label: 'Community Service',
          href: '/dashboard/activity/community-service',
          active: true,
        });
      } else if (subpath === 'honors-awards') {
        breadcrumbs.push({
          label: 'Honors & Awards',
          href: '/dashboard/activity/honors-awards',
          active: true,
        });
      } else if (subpath === 'publications') {
        breadcrumbs.push({
          label: 'Publications',
          href: '/dashboard/activity/publications',
          active: true,
        });

        // Check if there's a faculty ID (for individual faculty publications)
        if (paths.length > 3) {
          const facultyId = paths[3];
          breadcrumbs[1].active = false;
          breadcrumbs.push({
            label: 'Faculty Publications',
            href: `/dashboard/activity/publications/${facultyId}`,
            active: true,
          });
        }
      }
    }

    // Ensure we always return a valid array
    return Array.isArray(breadcrumbs) ? breadcrumbs : [];
  };

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="container mx-auto py-6">
        <Breadcrumbs breadcrumbs={getBreadcrumbs()} />
        {children}
      </div>
    </Suspense>
  );
}
