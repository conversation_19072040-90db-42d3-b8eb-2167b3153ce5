'use client';

import { Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function FacultyPublicationsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const isAdmin = session?.user?.roles?.includes('system_admin') ||
                  session?.user?.roles?.includes('institution_admin');

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'unauthenticated' || (status === 'authenticated' && !isAdmin)) {
      router.push('/login');
    }
  }, [status, router, isAdmin]);

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      {children}
    </Suspense>
  );
}
