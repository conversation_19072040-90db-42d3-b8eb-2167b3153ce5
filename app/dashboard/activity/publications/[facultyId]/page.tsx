'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { GoogleScholarPublication } from '@/lib/repositories/google-scholar-publication-repository';
import { lusitana } from '@/app/ui/fonts';
import { LatestPublicationsSkeleton } from '@/app/ui/skeletons';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

export default function FacultyPublicationsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const facultyId = params.facultyId as string;
  const [publications, setPublications] = useState<GoogleScholarPublication[]>([]);
  const [facultyDetails, setFacultyDetails] = useState<{ faculty_name: string; department: string | null; work_email: string } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'year' | 'citations'>('year');

  const isAdmin = session?.user?.roles?.includes('system_admin') ||
                  session?.user?.roles?.includes('institution_admin');

  useEffect(() => {
    // Redirect if not authenticated or not admin
    if (status === 'unauthenticated' || (status === 'authenticated' && !isAdmin)) {
      router.push('/login');
    }
  }, [status, router, isAdmin]);

  useEffect(() => {
    const fetchPublications = async () => {
      if (status !== 'authenticated' || !isAdmin) return;

      setLoading(true);
      try {
        const response = await fetch(`/api/activity/publications?facultyId=${facultyId}&sortBy=${sortBy}`);

        if (!response.ok) {
          throw new Error('Failed to fetch publications');
        }

        const data = await response.json();
        setPublications(data.publications);
        setFacultyDetails(data.facultyDetails);
      } catch (err) {
        console.error('Error fetching publications:', err);
        setError('Failed to load publications. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPublications();
  }, [facultyId, status, isAdmin, sortBy]);

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full">

      <div className="flex w-full items-center justify-between mt-4">
        <div className="flex items-center">
          <Link href="/dashboard/activity/publications" className="mr-4">
            <ArrowLeftIcon className="h-5 w-5 text-gray-500 hover:text-gray-700" />
          </Link>
          <h1 className={`${lusitana.className} text-2xl`}>
            {facultyDetails?.faculty_name || 'Faculty'} Publications
          </h1>
        </div>
        <div className="flex items-center">
          <label htmlFor="sortBy" className="mr-2 text-sm text-gray-600">Sort by:</label>
          <select
            id="sortBy"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'year' | 'citations')}
            className="rounded-md border border-gray-300 px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]"
          >
            <option value="year">Publication Year</option>
            <option value="citations">Citations</option>
          </select>
        </div>
      </div>

      {facultyDetails && (
        <div className="mt-4 text-sm text-gray-600">
          <p>Department: {facultyDetails.department || 'N/A'}</p>
          <p>Email: {facultyDetails.work_email}</p>
        </div>
      )}

      {loading ? (
        <LatestPublicationsSkeleton />
      ) : error ? (
        <div className="mt-6 rounded-lg bg-white p-4 shadow">
          <div className="text-center text-red-500">{error}</div>
        </div>
      ) : publications.length === 0 ? (
        <div className="mt-6 rounded-lg bg-white p-4 shadow">
          <div className="text-center py-8">
            <p className="text-gray-500">No publications found for this faculty member.</p>
          </div>
        </div>
      ) : (
        <div className="mt-6 rounded-lg bg-white p-4 shadow">
          <h2 className="text-lg font-medium mb-4">
            Publications {loading ? (
              <span className="inline-block ml-2 animate-pulse bg-gray-200 rounded h-5 w-8"></span>
            ) : (
              <span>({publications.length})</span>
            )}
          </h2>
          <div className="space-y-4">
            {publications.map((pub) => (
              <div key={pub.publication_id} className="border-b pb-3">
                <h3 className="font-medium text-base">{pub.title}</h3>
                <div className="flex flex-wrap gap-x-4 mt-1 text-xs text-gray-600">
                  <span>{pub.authors}</span>
                  {pub.venue && <span>• {pub.venue}</span>}
                  {pub.year && <span>• {pub.year}</span>}
                  {pub.citations && <span>• Citations: {pub.citations}</span>}
                </div>
                <div className="flex gap-2 mt-1">
                  {pub.publication_url && (
                    <a
                      href={pub.publication_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline text-xs"
                    >
                      View Publication
                    </a>
                  )}
                  {pub.scholar_url && (
                    <a
                      href={pub.scholar_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline text-xs"
                    >
                      View on Scholar
                    </a>
                  )}
                  {pub.pdf_url && (
                    <a
                      href={pub.pdf_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline text-xs"
                    >
                      PDF
                    </a>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
