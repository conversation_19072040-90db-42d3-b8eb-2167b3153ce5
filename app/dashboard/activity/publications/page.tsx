'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { GoogleScholarPublication, FacultySummary } from '@/lib/repositories/google-scholar-publication-repository';
import Pagination from '@/app/ui/common/pagination';
import { LatestPublicationsSkeleton } from '@/app/ui/skeletons';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

export default function PublicationsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [publications, setPublications] = useState<GoogleScholarPublication[]>([]);
  const [allFaculty, setAllFaculty] = useState<FacultySummary[]>([]);
  const [filteredFaculty, setFilteredFaculty] = useState<FacultySummary[]>([]);
  const [paginatedFaculty, setPaginatedFaculty] = useState<FacultySummary[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState<'year' | 'citations'>('year');

  // Get current page from URL or default to 1
  const currentPage = Number(searchParams.get('page') || '1');
  const pageSize = 20;

  const isAdmin = session?.user?.roles?.includes('system_admin') ||
                  session?.user?.roles?.includes('institution_admin');

  useEffect(() => {
    // Redirect if not authenticated
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Filter faculty based on search term and handle pagination
  useEffect(() => {
    if (allFaculty.length > 0) {
      let filtered;
      if (!searchTerm.trim()) {
        filtered = [...allFaculty];
      } else {
        const term = searchTerm.toLowerCase().trim();
        filtered = allFaculty.filter((f: FacultySummary) => {
          // Extract name and email information with null checks
          const fullName = (f.faculty_name || '').toLowerCase();
          const email = (f.work_email || '').toLowerCase();

          // Extract original name parts (with original casing)
          const originalNameParts = (f.faculty_name || '').split(/\s+/).filter(Boolean);

          // Extract lowercase name parts for case-insensitive matching
          const nameParts = fullName.split(/\s+/).filter(Boolean);
          const firstName = nameParts[0] || '';
          const lastName = nameParts.length > 1 ? nameParts[nameParts.length - 1] : '';

          // Get middle names if any
          const middleNames = nameParts.slice(1, -1).join(' ');

          // Direct matches (case insensitive)
          const firstNameMatch = firstName.includes(term);
          const lastNameMatch = lastName.includes(term);
          const middleNameMatch = middleNames.includes(term);
          const emailMatch = email.includes(term);

          // Full name match
          const fullNameMatch = fullName.includes(term);

          // Check if any part of the name contains the search term
          const namePartMatch = nameParts.some((part: string) => part && part.includes(term));

          // Check for exact matches on name parts (for short names like "Derek")
          const exactNamePartMatch = nameParts.some((part: string) => part && part === term);

          // Check for starts with matches (for partial names)
          const startsWithMatch = nameParts.some((part: string) => part && part.startsWith(term));

          // Check if search term matches multiple parts of the name
          // This handles cases like searching for "john smith"
          const multiPartMatch = term.includes(' ') &&
            term.split(/\s+/).every(termPart =>
              nameParts.some((namePart: string) => namePart && namePart.includes(termPart)));

          // Special case for very short search terms (2-3 chars)
          const isShortTerm = term.length >= 2 && term.length <= 3;
          const shortTermMatch = isShortTerm && originalNameParts.some((part: string) =>
            part && part.toLowerCase().startsWith(term));

          // Determine if there's a match
          const isMatch = firstNameMatch || lastNameMatch || middleNameMatch ||
                         emailMatch || fullNameMatch || namePartMatch ||
                         multiPartMatch || exactNamePartMatch || startsWithMatch ||
                         shortTermMatch;

          // Log matches for debugging
          if (isMatch) {
            console.log(`✓ MATCH FOUND: ${f.faculty_name || 'Unknown'}`);
            if (firstNameMatch) console.log(`  - First name match: ${firstName} includes ${term}`);
            if (lastNameMatch) console.log(`  - Last name match: ${lastName} includes ${term}`);
            if (exactNamePartMatch) console.log(`  - Exact name part match`);
            if (startsWithMatch) console.log(`  - Starts with match`);
            if (shortTermMatch) console.log(`  - Short term match`);
          }

          return isMatch;
        });
      }

      // Update filtered faculty
      setFilteredFaculty(filtered);

      // Calculate total pages based on filtered results
      const newTotalPages = Math.ceil(filtered.length / pageSize);
      setTotalPages(newTotalPages);

      // Apply pagination to filtered results
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setPaginatedFaculty(filtered.slice(startIndex, endIndex));
    }
  }, [allFaculty, searchTerm, currentPage, pageSize]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);

    // Reset to page 1 when searching
    if (currentPage !== 1) {
      router.push('/dashboard/activity/publications?page=1');
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (status !== 'authenticated') return;

      setLoading(true);
      try {
        if (isAdmin) {
          // Admins see faculty list with publication counts
          // Fetch all faculty data without pagination
          const response = await fetch(`/api/activity/publications?getAllFaculty=true`);

          if (!response.ok) {
            throw new Error('Failed to fetch faculty data');
          }

          const data = await response.json();
          setAllFaculty(data.faculty || []);
          setFilteredFaculty(data.faculty || []);

          // Initial pagination will be handled by the filter effect
        } else {
          // Regular users see their own publications
          const response = await fetch(`/api/activity/publications?sortBy=${sortBy}`);

          if (!response.ok) {
            throw new Error('Failed to fetch publications');
          }

          const data = await response.json();
          setPublications(data.publications || []);

          if (data.message) {
            setMessage(data.message);
          } else {
            setMessage(null);
          }
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentPage, pageSize, status, isAdmin, sortBy]);

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full">

      {loading ? (
        <LatestPublicationsSkeleton />
      ) : error ? (
        <div className="mt-6 rounded-lg bg-white p-4 shadow">
          <div className="text-center text-red-500">{error}</div>
        </div>
      ) : isAdmin ? (
        // Admin view - Faculty list with publication counts
        <div className="mt-6 rounded-lg bg-white p-4 shadow">
          <div className="mb-4 space-y-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search by name or email..."
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={handleSearchChange}
                autoComplete="off"
                aria-label="Search faculty"
              />
              {searchTerm ? (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 flex items-center pr-3"
                  aria-label="Clear search"
                >
                  <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              ) : (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              )}
            </div>
            {searchTerm && (
              <div className="text-sm text-gray-500">
                Found {filteredFaculty.length} {filteredFaculty.length === 1 ? 'faculty member' : 'faculty members'} matching "{searchTerm}"
                {filteredFaculty.length > 0 && (
                  <span> (showing page {currentPage} of {totalPages})</span>
                )}
              </div>
            )}
          </div>

          {filteredFaculty.length === 0 ? (
            <div className="text-center py-8">
              {searchTerm ? (
                <p className="text-gray-500">No faculty members match your search criteria.</p>
              ) : (
                <p className="text-gray-500">No faculty members found with publications.</p>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Faculty Name</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedFaculty.map((f) => (
                    <tr key={f.faculty_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{f.faculty_name || 'Unknown'}</div>
                        <div className="text-xs text-gray-500">{f.work_email || 'No email'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{f.department || 'N/A'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <Link
                          href={`/dashboard/activity/publications/${f.faculty_id}`}
                          className="text-blue-600 hover:text-blue-900 flex items-center"
                        >
                          View Publications
                          <ArrowRightIcon className="h-4 w-4 ml-1" />
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {totalPages > 1 && (
            <div className="mt-5 flex w-full justify-center">
              <Pagination totalPages={totalPages} />
            </div>
          )}
        </div>
      ) : (
        // Regular user view - Personal publications
        <div className="mt-6 rounded-lg bg-white p-4 shadow">
          <div className="flex justify-end mb-4">
            <div className="flex items-center">
              <label htmlFor="sortBy" className="mr-2 text-sm text-gray-600">Sort by:</label>
              <select
                id="sortBy"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'year' | 'citations')}
                className="rounded-md border border-gray-300 px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]"
              >
                <option value="year">Publication Year</option>
                <option value="citations">Citations</option>
              </select>
            </div>
          </div>

          {publications.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">{message || 'No publications found.'}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {publications.map((pub) => (
                <div key={pub.publication_id} className="border-b pb-3">
                  <h3 className="font-medium text-base">{pub.title}</h3>
                  <div className="flex flex-wrap gap-x-4 mt-1 text-xs text-gray-600">
                    <span>{pub.authors}</span>
                    {pub.venue && <span>• {pub.venue}</span>}
                    {pub.year && <span>• {pub.year}</span>}
                    {pub.citations && <span>• Citations: {pub.citations}</span>}
                  </div>
                  <div className="flex gap-2 mt-1">
                    {pub.publication_url && (
                      <a
                        href={pub.publication_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline text-xs"
                      >
                        View Publication
                      </a>
                    )}
                    {pub.scholar_url && (
                      <a
                        href={pub.scholar_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline text-xs"
                      >
                        View on Scholar
                      </a>
                    )}
                    {pub.pdf_url && (
                      <a
                        href={pub.pdf_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline text-xs"
                      >
                        PDF
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
