import { getServerSession } from "next-auth/next";
import { sql } from "@/app/lib/db";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";

export async function GET(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("professor") || !session.user.id) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  const files = await sql`
    SELECT file_name, uploaded_at 
    FROM uw.faculty_file 
    WHERE user_id = ${session.user.id} AND deleted_at IS NULL
  `;
  return NextResponse.json(files);
}

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("professor") || !session.user.id) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  const { fileName, filePath } = await req.json();
  await sql`
    INSERT INTO uw.faculty_file (user_id, file_name, file_path) 
    VALUES (${session.user.id}, ${fileName}, ${filePath})
  `;
  return NextResponse.json({ message: "File uploaded" }, { status: 201 });
}