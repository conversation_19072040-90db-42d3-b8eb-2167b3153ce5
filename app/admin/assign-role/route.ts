import { getServerSession } from "next-auth/next";
import { sql } from "@/app/lib/db";
import { NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.includes("institutional_admin")) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  const { userId, roleName } = await req.json();
  const [role] = await sql`
    SELECT role_id FROM common.role WHERE name = ${roleName} AND deleted_at IS NULL
  `;
  if (!role) {
    return NextResponse.json({ error: "Role not found" }, { status: 400 });
  }

  await sql`
    INSERT INTO common.user_role (user_id, role_id) 
    VALUES (${userId}, ${role.role_id})
    ON CONFLICT (user_id, role_id) DO NOTHING
  `;
  return NextResponse.json({ message: "Role assigned successfully" });
}