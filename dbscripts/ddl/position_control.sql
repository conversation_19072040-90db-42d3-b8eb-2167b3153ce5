-- Faculty Position Control Database Schema
-- This schema implements the Faculty Complement Planning and Position Control workflow
-- Using the existing uw schema

-- Set search path
SET search_path TO uw, public;

-- Enum types for position control
CREATE TYPE request_status AS ENUM (
    'draft',
    'submitted',
    'unit_head_review',
    'unit_head_approved',
    'donna_review',
    'donna_approved',
    'veronica_review',
    'veronica_approved',
    'dean_review',
    'dean_approved',
    'provost_review',
    'provost_approved',
    'completed',
    'rejected'
);

CREATE TYPE career_path_type AS ENUM (
    'tenure_stream',
    'teaching_stream',
    'research_faculty'
);

CREATE TYPE position_type AS ENUM (
    'new_position',
    'replacement'
);

CREATE TYPE new_position_type AS ENUM (
    'addition_to_operating_complement',
    'not_in_complement'
);

CREATE TYPE replacement_reason AS ENUM (
    'resignation',
    'retirement',
    'termination',
    'death',
    'other'
);

-- Faculty Position Requests table
CREATE TABLE faculty_position_requests (
    id SERIAL PRIMARY KEY,
    request_number VARCHAR(50) UNIQUE NOT NULL,
    status request_status DEFAULT 'draft',
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Hiring Unit(s) Information (Screen 1)
    is_joint_appointment BOOLEAN DEFAULT FALSE,
    home_department_id INTEGER NOT NULL,
    home_department_percentage NUMERIC(5,2) DEFAULT 100.00,
    home_unit_head_id INTEGER,

    -- Joint appointment details
    second_department_id INTEGER,
    second_department_percentage NUMERIC(5,2),
    second_unit_head_id INTEGER,

    -- Position Details (Screen 2)
    career_path career_path_type NOT NULL,
    position_type position_type NOT NULL,

    -- New position details
    new_position_type new_position_type,
    hiring_plan_reference VARCHAR(255),

    -- Replacement position details
    existing_position_number VARCHAR(100),
    replacement_reason replacement_reason,
    replacement_reason_other TEXT,
    incumbent_name VARCHAR(255),
    incumbent_employee_id VARCHAR(50),
    termination_date DATE,
    is_bridge_position BOOLEAN DEFAULT FALSE,
    bridge_position_number VARCHAR(100),
    bridge_end_date DATE,

    -- Common position details
    funding_sources TEXT,
    position_notes TEXT,

    -- Advertisement (Screen 3)
    position_title VARCHAR(500),
    advertisement_body TEXT,

    -- Search Status (Screen 4) - completed when search fails
    failed_search_date DATE,
    mission_critical_approval_file VARCHAR(500),
    recruitment_summary_file VARCHAR(500),

    -- Generated documents and approval tracking
    mission_critical_number VARCHAR(100),
    authorization_number VARCHAR(100),
    mission_critical_form_path VARCHAR(500),
    authorization_form_path VARCHAR(500),

    -- Soft delete
    is_deleted BOOLEAN DEFAULT FALSE,

    CONSTRAINT fk_created_by FOREIGN KEY (created_by) REFERENCES faculty(faculty_id),
    CONSTRAINT fk_home_department FOREIGN KEY (home_department_id) REFERENCES unit(unit_id),
    CONSTRAINT fk_second_department FOREIGN KEY (second_department_id) REFERENCES unit(unit_id),
    CONSTRAINT fk_home_unit_head FOREIGN KEY (home_unit_head_id) REFERENCES faculty(faculty_id),
    CONSTRAINT fk_second_unit_head FOREIGN KEY (second_unit_head_id) REFERENCES faculty(faculty_id),
    CONSTRAINT chk_joint_appointment CHECK (
        (is_joint_appointment = FALSE AND second_department_id IS NULL) OR
        (is_joint_appointment = TRUE AND second_department_id IS NOT NULL)
    ),
    CONSTRAINT chk_percentage_total CHECK (
        (is_joint_appointment = FALSE AND home_department_percentage = 100.00) OR
        (is_joint_appointment = TRUE AND home_department_percentage + second_department_percentage = 100.00)
    )
);

-- Request Approvals tracking table
CREATE TABLE request_approvals (
    id SERIAL PRIMARY KEY,
    request_id INTEGER NOT NULL,
    approval_step request_status NOT NULL,
    approver_id INTEGER,
    approved_at TIMESTAMP WITH TIME ZONE,
    rejected_at TIMESTAMP WITH TIME ZONE,
    comments TEXT,
    signature VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT fk_request_id FOREIGN KEY (request_id) REFERENCES faculty_position_requests(id),
    CONSTRAINT fk_approver_id FOREIGN KEY (approver_id) REFERENCES faculty(faculty_id)
);

-- Engineering Faculty Hiring Plans table
CREATE TABLE hiring_plans (
    id SERIAL PRIMARY KEY,
    plan_year INTEGER NOT NULL,
    reference_code VARCHAR(100) NOT NULL,
    department_id INTEGER NOT NULL,
    position_description TEXT,
    career_path career_path_type,
    priority_level INTEGER,
    budget_allocation NUMERIC(12,2),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,

    CONSTRAINT fk_department FOREIGN KEY (department_id) REFERENCES unit(unit_id),
    CONSTRAINT uk_plan_reference UNIQUE (plan_year, reference_code)
);

-- Position Request Documents table for file uploads
CREATE TABLE request_documents (
    id SERIAL PRIMARY KEY,
    request_id INTEGER NOT NULL,
    document_type VARCHAR(100) NOT NULL, -- 'mission_critical_approval', 'recruitment_summary', etc.
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_by INTEGER NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT fk_request_documents_request FOREIGN KEY (request_id) REFERENCES faculty_position_requests(id),
    CONSTRAINT fk_uploaded_by FOREIGN KEY (uploaded_by) REFERENCES faculty(faculty_id)
);

-- Create indexes for performance
CREATE INDEX idx_position_requests_status ON faculty_position_requests(status);
CREATE INDEX idx_position_requests_created_by ON faculty_position_requests(created_by);
CREATE INDEX idx_position_requests_department ON faculty_position_requests(home_department_id);
CREATE INDEX idx_position_requests_created_at ON faculty_position_requests(created_at);
CREATE INDEX idx_request_approvals_request_id ON request_approvals(request_id);
CREATE INDEX idx_request_approvals_step ON request_approvals(approval_step);
CREATE INDEX idx_hiring_plans_year ON hiring_plans(plan_year);
CREATE INDEX idx_hiring_plans_department ON hiring_plans(department_id);

-- Function to generate request numbers
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TEXT AS $$
DECLARE
    year_part TEXT;
    sequence_part TEXT;
    next_seq INTEGER;
BEGIN
    year_part := EXTRACT(YEAR FROM NOW())::TEXT;

    -- Get next sequence number for this year
    SELECT COALESCE(MAX(CAST(SUBSTRING(request_number FROM 6) AS INTEGER)), 0) + 1
    INTO next_seq
    FROM faculty_position_requests
    WHERE request_number LIKE year_part || '-%';

    sequence_part := LPAD(next_seq::TEXT, 4, '0');

    RETURN year_part || '-' || sequence_part;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate request numbers
CREATE OR REPLACE FUNCTION set_request_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.request_number IS NULL OR NEW.request_number = '' THEN
        NEW.request_number := generate_request_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_request_number
    BEFORE INSERT ON faculty_position_requests
    FOR EACH ROW
    EXECUTE FUNCTION set_request_number();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_position_requests_timestamp
    BEFORE UPDATE ON faculty_position_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER trigger_update_hiring_plans_timestamp
    BEFORE UPDATE ON hiring_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

-- Comments for documentation
COMMENT ON TABLE faculty_position_requests IS 'Main table storing faculty position search requests';
COMMENT ON TABLE request_approvals IS 'Tracks approval workflow steps for each request';
COMMENT ON TABLE hiring_plans IS 'Engineering Faculty Hiring Plan data';
COMMENT ON TABLE request_documents IS 'File uploads associated with position requests';
