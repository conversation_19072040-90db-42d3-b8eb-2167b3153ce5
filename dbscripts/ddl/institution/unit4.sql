-- Create the unit4 schema in the institution database
CREATE SCHEMA IF NOT EXISTS unit4;

-- Table to store research work order information
CREATE TABLE unit4.research_work_order (
    id SERIAL PRIMARY KEY,
    activity VARCHAR(50),
    act_name VARCHAR(255),
    anticipated_end_date TIMESTAMP WITH TIME ZONE,
    client VARCHAR(50),
    concur VARCHAR(5),
    end_date TIMESTAMP WITH TIME ZONE,
    fund VARCHAR(50),
    fund_name VARCHAR(255),
    grs VARCHAR(5),
    hr VARCHAR(5),
    org_unit VARCHAR(50),
    org_unit_name VARCHAR(255),
    parent_wo_name TEXT,
    parent_work_order VARCHAR(50),
    pi_eid VARCHAR(50),
    pi_name VARCHAR(255),
    pi_watiam VARCHAR(50),
    project VARCHAR(50),
    project_name TEXT,
    research_compliance VARCHAR(5),
    rfa_eid VARCHAR(50),
    rfa_name VARCHA<PERSON>(255),
    rfa_watiam VARCHAR(50),
    sa_eid VARCHAR(50),
    sa_name VARCHAR(255),
    sa_watiam VARCHAR(50),
    start_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(5),
    wo_manager_eid VARCHAR(50),
    wo_manager_name VARCHAR(255),
    wo_manager_watiam VARCHAR(50),
    work_order VARCHAR(50),
    work_order_name TEXT,
    work_request VARCHAR(50),
    activity_balance VARCHAR(50),
    work_order_balance VARCHAR(50),
    client_balance VARCHAR(50),
    future_budget NUMERIC(15, 2),
    net_funds NUMERIC(15, 2),
    import_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX idx_research_work_order_work_order ON unit4.research_work_order(work_order);
CREATE INDEX idx_research_work_order_org_unit ON unit4.research_work_order(org_unit);
CREATE INDEX idx_research_work_order_pi_name ON unit4.research_work_order(pi_name);
