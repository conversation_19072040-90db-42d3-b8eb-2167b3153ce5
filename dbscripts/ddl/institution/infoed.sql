-- Create the infoed schema in the institution database
CREATE SCHEMA IF NOT EXISTS infoed;

-- Table to store research funding information
CREATE TABLE infoed.research_funding (
    funding_id SERIAL PRIMARY KEY,
    award_year VARCHAR(100),
    breakout_sponsor_type VARCHAR(100),
    breakout_tri_agency VARCHAR(100),
    associated_department VARCHAR(100),
    faculty VARCHAR(100),
    investigator_role VARCHAR(100),
    original_sponsor VARCHAR(200),
    overall_tri_agency VARCHAR(100),
    project_number VARCHAR(100),
    project_title TEXT,
    researcher VARCHAR(100),
    sponsor_grouping VARCHAR(100),
    sponsor_name VARCHAR(200),
    sponsor_type VARCHAR(100),
    total_award NUMERIC(15, 2),
    work_order VARCHAR(100),
    prime_project_number VARCHAR(100),
    installment VARCHAR(100),
    instrument_type VARCHAR(100),
    number_of_prime_pts_with_awards NUMERIC(10, 2),
    number_of_researchers NUMERIC(10, 2),
    number_of_pis_only <PERSON>UM<PERSON><PERSON>(10, 2),
    first_pd NUMERIC(10, 2),
    faculty_id INTEGER,
    match_confidence NUMERIC(5, 2),
    import_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Create index on researcher name for faster lookups
CREATE INDEX idx_research_funding_researcher ON infoed.research_funding(researcher);

-- Create index on faculty_id for faster joins
CREATE INDEX idx_research_funding_faculty_id ON infoed.research_funding(faculty_id);
