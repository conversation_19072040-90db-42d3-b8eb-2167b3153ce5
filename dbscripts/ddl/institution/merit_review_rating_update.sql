-- Add is_submitted column to merit_review_rating table
ALTER TABLE uw.merit_review_rating
ADD COLUMN is_submitted BOOLEAN DEFAULT FALSE;

-- Add is_submitted column to merit_preliminary_rating table
ALTER TABLE uw.merit_preliminary_rating
ADD COLUMN is_submitted BOOLEAN DEFAULT FALSE;

-- Create index on merit_review_rating for faster lookups
CREATE INDEX idx_merit_review_rating_report_reviewer
ON uw.merit_review_rating(report_id, reviewer_id);

-- Create index on merit_preliminary_rating for faster lookups
CREATE INDEX idx_merit_preliminary_rating_report_unit_head
ON uw.merit_preliminary_rating(report_id, unit_head_id);

-- Update any existing ratings to have is_submitted = TRUE
UPDATE uw.merit_review_rating
SET is_submitted = TRUE
WHERE is_submitted IS NULL;

UPDATE uw.merit_preliminary_rating
SET is_submitted = TRUE
WHERE is_submitted IS NULL;

-- Add comments to explain the purpose of the columns
COMMENT ON COLUMN uw.merit_review_rating.is_submitted IS 'Indicates whether the rating has been submitted and can no longer be changed';
COMMENT ON COLUMN uw.merit_preliminary_rating.is_submitted IS 'Indicates whether the preliminary rating has been submitted and can no longer be changed';
