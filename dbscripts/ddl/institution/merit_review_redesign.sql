-- Merit Review Redesign
-- Drop existing merit_report table and related tables that reference it
DROP TABLE IF EXISTS merit_disagreement;
DROP TABLE IF EXISTS merit_final_rating;
DROP TABLE IF EXISTS merit_preliminary_rating;
DROP TABLE IF EXISTS merit_review_rating;
DROP TABLE IF EXISTS merit_report;

-- Main Merit Report Table
CREATE TABLE merit_report (
    id SERIAL PRIMARY KEY,
    faculty_id INTEGER NOT NULL,
    unit_id INTEGER NOT NULL,
    workflow_id INTEGER NOT NULL,
    report_type VARCHAR(10) NOT NULL, -- 'APR' or 'BPR'
    report_year INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- draft, in_progress, submitted, under_review, reviewed, approved
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    submit_dt TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (unit_id) REFERENCES unit(unit_id),
    FOREIGN KEY (workflow_id) REFERENCES merit_workflow_config(id)
);

-- Merit Report Teaching Section
CREATE TABLE merit_report_teaching (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    teaching_weight NUMERIC(5,2),
    research_weight NUMERIC(5,2),
    service_weight NUMERIC(5,2),
    previous_teaching_rating INTEGER,
    previous_research_rating INTEGER,
    previous_service_rating INTEGER,
    previous_teaching_weight NUMERIC(5,2),
    previous_research_weight NUMERIC(5,2),
    previous_service_weight NUMERIC(5,2),
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Teaching Courses
CREATE TABLE merit_report_teaching_courses (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    term_year VARCHAR(20) NOT NULL,
    course_number VARCHAR(20) NOT NULL,
    course_title VARCHAR(255) NOT NULL,
    scp_q1_q3 VARCHAR(50), -- Student Course Perception survey scores Q1-3
    scp_q1_q3_std_dev VARCHAR(50), -- Standard deviation
    scp_q4_q6 VARCHAR(50), -- Student Course Perception survey scores Q4-6
    scp_q4_q6_std_dev VARCHAR(50), -- Standard deviation
    num_students INTEGER,
    response_percentage NUMERIC(5,2),
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Student Supervision
CREATE TABLE merit_report_student_supervision (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    undergrad_supervised INTEGER DEFAULT 0,
    undergrad_cosupervised INTEGER DEFAULT 0,
    masters_supervised INTEGER DEFAULT 0,
    masters_cosupervised INTEGER DEFAULT 0,
    phd_supervised INTEGER DEFAULT 0,
    phd_cosupervised INTEGER DEFAULT 0,
    pdf_supervised INTEGER DEFAULT 0,
    pdf_cosupervised INTEGER DEFAULT 0,
    visitors_supervised INTEGER DEFAULT 0,
    visitors_cosupervised INTEGER DEFAULT 0,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Teaching Summary
CREATE TABLE merit_report_teaching_summary (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    accomplishments TEXT,
    goals TEXT,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Research Section
CREATE TABLE merit_report_research (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    year1 INTEGER,
    year2 INTEGER,
    year3 INTEGER,
    ref_journal_papers_year1 INTEGER DEFAULT 0,
    ref_journal_papers_year2 INTEGER DEFAULT 0,
    ref_journal_papers_year3 INTEGER DEFAULT 0,
    ref_conf_papers_year1 INTEGER DEFAULT 0,
    ref_conf_papers_year2 INTEGER DEFAULT 0,
    ref_conf_papers_year3 INTEGER DEFAULT 0,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Research Publications
CREATE TABLE merit_report_research_publications (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    publication_type VARCHAR(50) NOT NULL, -- journal, conference, book, chapter, etc.
    title TEXT NOT NULL,
    authors TEXT NOT NULL,
    venue TEXT NOT NULL,
    year INTEGER NOT NULL,
    doi VARCHAR(255),
    citation_count INTEGER,
    is_highlighted BOOLEAN DEFAULT FALSE,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Research Grants
CREATE TABLE merit_report_research_grants (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    grant_type VARCHAR(50) NOT NULL, -- research, equipment
    pi_name VARCHAR(255) NOT NULL,
    collaborators TEXT,
    title TEXT NOT NULL,
    agency VARCHAR(255) NOT NULL,
    amount NUMERIC(15,2) NOT NULL,
    installment_year INTEGER,
    share_percentage NUMERIC(5,2),
    status VARCHAR(50) NOT NULL, -- awarded, submitted
    submission_date DATE,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Research Summary
CREATE TABLE merit_report_research_summary (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    accomplishments TEXT,
    goals TEXT,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Service Section
CREATE TABLE merit_report_service (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Service Activities
CREATE TABLE merit_report_service_activities (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    service_type VARCHAR(50) NOT NULL, -- departmental, faculty, university, external
    committee_name VARCHAR(255) NOT NULL,
    role VARCHAR(255),
    start_date DATE,
    end_date DATE,
    time_spent VARCHAR(100), -- e.g., "2 hours per week"
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Professional Registration
CREATE TABLE merit_report_professional_registration (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    designation VARCHAR(50) NOT NULL, -- e.g., P.Eng, OAA
    registration_year INTEGER,
    province VARCHAR(50),
    requirements TEXT,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Service Summary
CREATE TABLE merit_report_service_summary (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    accomplishments TEXT,
    goals TEXT,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Awards and Honors
CREATE TABLE merit_report_awards (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    award_name VARCHAR(255) NOT NULL,
    award_type VARCHAR(50) NOT NULL, -- teaching, research, service, other
    awarding_body VARCHAR(255),
    year INTEGER NOT NULL,
    description TEXT,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Report Additional Comments
CREATE TABLE merit_report_additional_comments (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    comments TEXT,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id) ON DELETE CASCADE
);

-- Merit Review Rating Table (updated to reference new merit_report)
CREATE TABLE merit_review_rating (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    reviewer_id INTEGER NOT NULL,
    teaching_rating REAL NOT NULL,
    research_rating REAL NOT NULL,
    service_rating REAL NOT NULL,
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id),
    FOREIGN KEY (reviewer_id) REFERENCES faculty(faculty_id)
);

-- Merit Preliminary Rating Table (updated to reference new merit_report)
CREATE TABLE merit_preliminary_rating (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    unit_head_id INTEGER NOT NULL,
    teaching_rating INTEGER NOT NULL,
    research_rating INTEGER NOT NULL,
    service_rating INTEGER NOT NULL,
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id),
    FOREIGN KEY (unit_head_id) REFERENCES faculty(faculty_id)
);

-- Merit Final Rating Table (updated to reference new merit_report)
CREATE TABLE merit_final_rating (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    teaching_rating INTEGER NOT NULL,
    research_rating INTEGER NOT NULL,
    service_rating INTEGER NOT NULL,
    comments TEXT,
    approved_by UUID NOT NULL,
    approved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'dean_approved', -- dean_approved, provost_approved, hr_processed
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id),
    FOREIGN KEY (approved_by) REFERENCES common.user(user_id)
);

-- Merit Disagreement Table (updated to reference new merit_report)
CREATE TABLE merit_disagreement (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    disagreement_text TEXT NOT NULL,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'submitted', -- submitted, under_review, resolved
    unit_head_recommendation TEXT,
    dean_decision TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id)
);

-- Create indexes for better performance
CREATE INDEX idx_merit_report_faculty_id ON merit_report(faculty_id);
CREATE INDEX idx_merit_report_unit_id ON merit_report(unit_id);
CREATE INDEX idx_merit_report_workflow_id ON merit_report(workflow_id);
CREATE INDEX idx_merit_report_teaching_report_id ON merit_report_teaching(report_id);
CREATE INDEX idx_merit_report_teaching_courses_report_id ON merit_report_teaching_courses(report_id);
CREATE INDEX idx_merit_report_student_supervision_report_id ON merit_report_student_supervision(report_id);
CREATE INDEX idx_merit_report_teaching_summary_report_id ON merit_report_teaching_summary(report_id);
CREATE INDEX idx_merit_report_research_report_id ON merit_report_research(report_id);
CREATE INDEX idx_merit_report_research_publications_report_id ON merit_report_research_publications(report_id);
CREATE INDEX idx_merit_report_research_grants_report_id ON merit_report_research_grants(report_id);
CREATE INDEX idx_merit_report_research_summary_report_id ON merit_report_research_summary(report_id);
CREATE INDEX idx_merit_report_service_report_id ON merit_report_service(report_id);
CREATE INDEX idx_merit_report_service_activities_report_id ON merit_report_service_activities(report_id);
CREATE INDEX idx_merit_report_professional_registration_report_id ON merit_report_professional_registration(report_id);
CREATE INDEX idx_merit_report_service_summary_report_id ON merit_report_service_summary(report_id);
CREATE INDEX idx_merit_report_awards_report_id ON merit_report_awards(report_id);
CREATE INDEX idx_merit_report_additional_comments_report_id ON merit_report_additional_comments(report_id);
CREATE INDEX idx_merit_review_rating_report_id ON merit_review_rating(report_id);
CREATE INDEX idx_merit_review_rating_reviewer_id ON merit_review_rating(reviewer_id);
CREATE INDEX idx_merit_preliminary_rating_report_id ON merit_preliminary_rating(report_id);
CREATE INDEX idx_merit_final_rating_report_id ON merit_final_rating(report_id);
CREATE INDEX idx_merit_disagreement_report_id ON merit_disagreement(report_id);
