-- Merit Report Supervision Table
-- This table has the same structure as quest.supervision_data but is linked to a specific merit report

CREATE TABLE uw.merit_report_supervision (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    supervision_id INTEGER, -- Reference to the original supervision_id in quest.supervision_data
    term INTEGER,
    faculty TEXT,
    department TEXT,
    student_id TEXT,
    student_name TEXT,
    academic_plan TEXT,
    supervisor_last_name TEXT,
    supervisor_first_name TEXT,
    citizenship TEXT,
    email_address TEXT,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES uw.merit_report(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_merit_report_supervision_report_id ON uw.merit_report_supervision(report_id);
CREATE INDEX idx_merit_report_supervision_term ON uw.merit_report_supervision(term);
CREATE INDEX idx_merit_report_supervision_student_id ON uw.merit_report_supervision(student_id);
