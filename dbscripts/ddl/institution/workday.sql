-- Create the workday schema in the institution database
CREATE SCHEMA IF NOT EXISTS workday;

-- Table to store position details from Workday
CREATE TABLE IF NOT EXISTS workday.position_details (
    id SERIAL PRIMARY KEY,
    position_name TEXT,
    reference_id INTEGER,
    worker_type TEXT,
    employee_type TEXT,
    time_type TEXT,
    staffing_status TEXT,
    available_for_hire TEXT,
    worker TEXT,
    contract_end_date DATE,
    business_title TEXT,
    job_profile TEXT,
    frozen TEXT,
    freeze_date DATE,
    freeze_reason TEXT,
    previous_incumbent TEXT,
    position_vacate_date DATE,
    fte NUMERIC(5,2),
    job_family TEXT,
    job_family_groups TEXT,
    cost_center TEXT,
    class_indicator TEXT,
    manager TEXT,
    supervisory_organization TEXT,
    level_03 TEXT,
    level_04 TEXT,
    level_05 TEXT,
    level_06 TEXT,
    level_07 TEXT,
    import_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for commonly queried fields
CREATE INDEX IF NOT EXISTS idx_workday_reference_id ON workday.position_details(reference_id);
CREATE INDEX IF NOT EXISTS idx_workday_worker ON workday.position_details(worker);
CREATE INDEX IF NOT EXISTS idx_workday_job_family ON workday.position_details(job_family);
CREATE INDEX IF NOT EXISTS idx_workday_level_04 ON workday.position_details(level_04);

-- Comments for documentation
COMMENT ON SCHEMA workday IS 'Schema for storing data imported from Workday';
COMMENT ON TABLE workday.position_details IS 'Position details imported from Workday';
COMMENT ON COLUMN workday.position_details.position_name IS 'Full position name including reference ID and worker name';
COMMENT ON COLUMN workday.position_details.reference_id IS 'Unique reference ID for the position';
COMMENT ON COLUMN workday.position_details.worker_type IS 'Type of worker (Employee, Contingent Worker, etc.)';
COMMENT ON COLUMN workday.position_details.employee_type IS 'Employee type (Regular, Temporary, etc.)';
COMMENT ON COLUMN workday.position_details.time_type IS 'Time type (Full time, Part time, etc.)';
COMMENT ON COLUMN workday.position_details.staffing_status IS 'Current staffing status (Filled, Vacant, etc.)';
COMMENT ON COLUMN workday.position_details.available_for_hire IS 'Whether the position is available for hire';
COMMENT ON COLUMN workday.position_details.worker IS 'Name of the worker currently in the position';
COMMENT ON COLUMN workday.position_details.contract_end_date IS 'End date of the contract if applicable';
COMMENT ON COLUMN workday.position_details.business_title IS 'Business title of the position';
COMMENT ON COLUMN workday.position_details.job_profile IS 'Job profile including code';
COMMENT ON COLUMN workday.position_details.frozen IS 'Whether the position is frozen';
COMMENT ON COLUMN workday.position_details.freeze_date IS 'Date when the position was frozen';
COMMENT ON COLUMN workday.position_details.freeze_reason IS 'Reason for freezing the position';
COMMENT ON COLUMN workday.position_details.previous_incumbent IS 'Name of the previous incumbent';
COMMENT ON COLUMN workday.position_details.position_vacate_date IS 'Date when the position was vacated';
COMMENT ON COLUMN workday.position_details.fte IS 'Full-time equivalent value';
COMMENT ON COLUMN workday.position_details.job_family IS 'Job family (Staff Family, Faculty, etc.)';
COMMENT ON COLUMN workday.position_details.job_family_groups IS 'Job family groups';
COMMENT ON COLUMN workday.position_details.cost_center IS 'Cost center including code and description';
COMMENT ON COLUMN workday.position_details.class_indicator IS 'Class indicator code and description';
COMMENT ON COLUMN workday.position_details.manager IS 'Name of the manager';
COMMENT ON COLUMN workday.position_details.supervisory_organization IS 'Supervisory organization name';
COMMENT ON COLUMN workday.position_details.level_03 IS 'Organization level 3 from the top';
COMMENT ON COLUMN workday.position_details.level_04 IS 'Organization level 4 from the top';
COMMENT ON COLUMN workday.position_details.level_05 IS 'Organization level 5 from the top';
COMMENT ON COLUMN workday.position_details.level_06 IS 'Organization level 6 from the top';
COMMENT ON COLUMN workday.position_details.level_07 IS 'Organization level 7 from the top';
COMMENT ON COLUMN workday.position_details.import_date IS 'Date and time when the record was imported';
