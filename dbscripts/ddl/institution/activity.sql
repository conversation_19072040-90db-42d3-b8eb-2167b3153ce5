-- Additional Enum Types
CREATE TYPE permanent_temporary AS ENUM ('Permanent', 'Temporary');
CREATE TYPE paid_unpaid AS ENUM ('Paid', 'Unpaid');
CREATE TYPE external_service_type AS ENUM ('Non-profit board', 'Professional service', 'For-profit board', 'Other');
CREATE TYPE service_role AS ENUM ('Board member', 'Board Chair', 'Other');
CREATE TYPE faculty_role AS ENUM ('Committee Chair', 'Committee Member', 'Mentor', 'Supervisor');
CREATE TYPE supervision_type AS ENUM ('Thesis', 'Dissertation', 'Senior Project', 'Capstone');
CREATE TYPE student_classification AS ENUM ('Undergraduate Y1', 'Undergraduate Y2', 'Undergraduate Y3', 
    'Undergraduate Y4', 'Undergraduate Y5', 'Master, Professional', 'Master, Research', 'PhD', 'Non-degree');
CREATE TYPE supervision_level AS ENUM ('Undergraduate', 'Graduate, Research', 'Graduate, Professional');

-- TRS Weight Distribution Table
CREATE TABLE trs_weight_distribution (
    trs_weight_distribution_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    effective_date DATE NOT NULL,
    date_of_last_update DATE,
    teaching INTEGER NOT NULL,
    research INTEGER NOT NULL,
    service INTEGER NOT NULL,
    other INTEGER NOT NULL,
    total INTEGER,
    permanent_or_temporary permanent_temporary NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Supervision Load Table
CREATE TABLE supervision_load (
    supervision_load_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    undergraduate INTEGER NOT NULL,
    masters INTEGER NOT NULL,
    doctoral INTEGER NOT NULL,
    postdoctoral_fellows INTEGER,
    research_associates INTEGER,
    visitors INTEGER,
    other INTEGER NOT NULL,
    total INTEGER,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Community Service Table
CREATE TABLE community_service (
    community_service_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    role VARCHAR(255) NOT NULL,
    organization VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Consulting Table
CREATE TABLE consulting (
    consulting_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    client_name VARCHAR(255) NOT NULL,
    estimated_hours_involved REAL,
    paid_or_unpaid paid_unpaid NOT NULL,
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- External Service Table
CREATE TABLE external_service (
    external_service_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    organization VARCHAR(255),
    description TEXT,
    service_type external_service_type NOT NULL,
    role service_role,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Honors and Awards Table
CREATE TABLE honor_and_award (
    honor_and_award_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    start_date_of_review_period DATE NOT NULL,
    end_date_of_review_period DATE,
    honor_award_title VARCHAR(255) NOT NULL,
    year_conferred INTEGER NOT NULL,
    conferring_organization VARCHAR(255),
    further_details TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Institutional Committee Table
CREATE TABLE institutional_committee (
    institutional_committee_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    date_of_last_update DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    unit_id INTEGER NOT NULL,
    committee_id INTEGER NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (unit_id) REFERENCES unit(unit_id),
    FOREIGN KEY (committee_id) REFERENCES committee(committee_id)
);

-- Mentorship/Supervision Table
CREATE TABLE mentorship_supervision (
    mentorship_supervision_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    faculty_role faculty_role NOT NULL,
    type supervision_type NOT NULL,
    student_name VARCHAR(255) NOT NULL,
    student_classification student_classification NOT NULL,
    project_title VARCHAR(255) NOT NULL,
    expected_graduation_month_year VARCHAR(255) NOT NULL,
    supervision_level supervision_level NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Non-Credit Instruction Table
CREATE TABLE non_credit_instruction (
    non_credit_instruction_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    begin_date DATE,
    title VARCHAR(255) NOT NULL,
    audience VARCHAR(255) NOT NULL,
    location_instruction_delivered VARCHAR(255) NOT NULL,
    sponsoring_organization VARCHAR(255) NOT NULL,
    sponsoring_organization_location VARCHAR(255) NOT NULL,
    number_of_participants VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Other Institutional Service Table
CREATE TABLE other_institutional_service (
    other_institutional_service_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    date_of_last_update DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    title VARCHAR(255) NOT NULL,
    service_unit_id INTEGER,
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (service_unit_id) REFERENCES unit(unit_id)
);

-- Other Professional Activity Table
CREATE TABLE other_professional_activity (
    other_professional_activity_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    date_of_last_update DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    cpe_hours INTEGER,
    city VARCHAR(255),
    province_or_state VARCHAR(255),
    country VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Other Service Table
CREATE TABLE other_service (
    other_service_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    date_of_last_update DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    organization VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Professional Development Table
CREATE TABLE professional_development (
    professional_development_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    cpe_hours REAL,
    city VARCHAR(255),
    province_or_state VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Professional Membership Table
CREATE TABLE professional_membership (
    professional_membership_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    date_of_last_update DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    organization_name VARCHAR(255) NOT NULL,
    month_year_started VARCHAR(255) NOT NULL,
    month_year_ended VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Professional Service Table
CREATE TABLE professional_service (
    professional_service_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    date_of_last_update DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    title VARCHAR(255) NOT NULL,
    organization VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Activity Custom Section Table (simplified due to dynamic nature)
CREATE TABLE activity_custom_section (
    activity_custom_section_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    date_of_last_update DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);