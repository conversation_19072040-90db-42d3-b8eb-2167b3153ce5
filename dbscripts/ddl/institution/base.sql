-- Enum Types
CREATE TYPE employment_status AS ENUM ('Full Time', 'Part Time', 'Inactive', 'Staff');
CREATE TYPE allow_login AS ENUM ('Yes', 'No', '1', '0', 'y', 'n', 'yes', 'no', 'true', 'false');
CREATE TYPE gender_at_birth AS ENUM ('Male', 'Female', 'X', 'Not declared');
CREATE TYPE term_type AS ENUM ('Winter', 'Spring', 'Fall', 'Ongoing');
CREATE TYPE career_path AS ENUM ('Tenure Stream', 'Teaching Stream', 'Research Faculty', 'Adjunct Faculty',
    'Special Faculty', 'Non-Regular Faculty', 'Staff');
CREATE TYPE employment_type AS ENUM ('Definite-Term', 'Ongoing', 'Temporary');
CREATE TYPE course_level AS ENUM ('Graduate', 'Undergraduate', 'Non-degree');
CREATE TYPE position_category AS ENUM ('Administrative Appointment', 'Joint Appointment',
    'Cross Appointment', 'Temporary Assignment', 'Other');
CREATE TYPE access_rights AS ENUM ('Full Admin Rights', 'Report Rights', 'Limited Rights');
CREATE TYPE login_method AS ENUM ('Managed by your school', 'Managed by Intelicampus');

-- Unit Table
CREATE TABLE unit (
    unit_id SERIAL PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    short_name VARCHAR(255) NOT NULL,
    is_affiliated_institution BOOLEAN,
    abbreviation VARCHAR(255) NOT NULL UNIQUE,
    parent_unit_id INTEGER,
    level_number INTEGER NOT NULL,
    external_id VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (parent_unit_id) REFERENCES unit(unit_id)
);

-- Faculty Table
CREATE TABLE faculty (
    faculty_id SERIAL PRIMARY KEY,
    intelicampus_id VARCHAR(255) NOT NULL,
    intelicampus_id2 VARCHAR(255) NOT NULL,
    employee_id VARCHAR(255) NOT NULL,
    sso_id VARCHAR(255) NOT NULL,
    allow_login allow_login NOT NULL,
    faculty_unique_id VARCHAR(255) NOT NULL,
    login_name VARCHAR(255) NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    preferred_name VARCHAR(255),
    last_name VARCHAR(255) NOT NULL,
    middle_initial VARCHAR(255),
    suffix VARCHAR(255),
    work_email VARCHAR(255) NOT NULL,
    honorific VARCHAR(255),
    primary_unit_id INTEGER NOT NULL,
    primary_unit_percentage NUMERIC NOT NULL,
    primary_unit_sub_group VARCHAR(255),
    employment_status employment_status NOT NULL,
    date_started DATE NOT NULL,
    rank_name VARCHAR(255) NOT NULL,
    tenure_status VARCHAR(255) NOT NULL,
    gender_at_birth gender_at_birth,
    declared_gender VARCHAR(255),
    country_of_origin VARCHAR(255),
    race_ethnicity VARCHAR(255),
    languages TEXT,
    url VARCHAR(255),
    office_building VARCHAR(255),
    office_number VARCHAR(255),
    work_phone VARCHAR(255),
    department_phone VARCHAR(255),
    home_phone VARCHAR(255),
    cell_phone VARCHAR(255),
    street_1 VARCHAR(255),
    street_2 VARCHAR(255),
    city VARCHAR(255),
    province_state VARCHAR(255),
    postal_zip_code VARCHAR(255),
    fax_number VARCHAR(255),
    pager VARCHAR(255),
    personal_city VARCHAR(255),
    personal_province_state VARCHAR(255),
    personal_street_1 VARCHAR(255),
    personal_street_2 VARCHAR(255),
    personal_postal_zip VARCHAR(255),
    personal_email VARCHAR(255) NOT NULL,
    emergency_contact VARCHAR(255),
    emergency_contact_phone VARCHAR(255),
    country_of_contact VARCHAR(255),
    job_family VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (primary_unit_id) REFERENCES unit(unit_id)
);

-- Faculty Classification Table
CREATE TABLE faculty_classification (
    faculty_classification_id SERIAL PRIMARY KEY,
    start_term term_type NOT NULL,
    start_year INTEGER NOT NULL,
    ending_term term_type NOT NULL,
    ending_year VARCHAR(255) NOT NULL,
    faculty_id INTEGER NOT NULL,
    position_number VARCHAR(255) NOT NULL,
    employment_status employment_status NOT NULL,
    career_path career_path NOT NULL,
    employment_type employment_type NOT NULL,
    faculty_rank VARCHAR(255) NOT NULL,
    tenure_status VARCHAR(255) NOT NULL,
    custom_classification VARCHAR(255),
    accreditation_classification VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Current Position Table
CREATE TABLE current_position (
    current_position_id SERIAL PRIMARY KEY,
    intelicampus_id VARCHAR(255) NOT NULL,
    position_number VARCHAR(255) NOT NULL,
    position_title VARCHAR(255) NOT NULL,
    home_unit_id INTEGER NOT NULL,
    home_unit_percentage NUMERIC NOT NULL,
    date_started DATE,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (home_unit_id) REFERENCES unit(unit_id)
);


-- position_number
CREATE TABLE position (
    position_id VARCHAR(255) PRIMARY KEY,
    position_title VARCHAR(255) NOT NULL,
    position_description VARCHAR(255) NULL,
    is_deleted BOOLEAN DEFAULT FALSE
);



-- Course Subject Code Table
CREATE TABLE course_subject_code (
    course_subject_code_id SERIAL PRIMARY KEY,
    subject_code VARCHAR(255) NOT NULL UNIQUE,
    course_name VARCHAR(255) NOT NULL,
    home_unit_id INTEGER NOT NULL,
    second_unit_id INTEGER,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (home_unit_id) REFERENCES unit(unit_id),
    FOREIGN KEY (second_unit_id) REFERENCES unit(unit_id)
);

-- Course Table
CREATE TABLE course (
    course_id SERIAL PRIMARY KEY,
    subject_code_id INTEGER NOT NULL,
    course_number VARCHAR(255) NOT NULL,
    effective_date DATE NOT NULL,
    date_of_creation DATE,
    date_of_import DATE,
    date_of_last_update DATE NOT NULL,
    course_title VARCHAR(255),
    previous_course_number VARCHAR(255),
    course_designation VARCHAR(255),
    credit_hours NUMERIC,
    lecture_hours INTEGER,
    lab_hours INTEGER,
    capstone_course INTEGER,
    course_level course_level,
    description TEXT,
    cip_code VARCHAR(255),
    extra_hours INTEGER,
    extra_hours_label VARCHAR(255),
    weight NUMERIC,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (subject_code_id) REFERENCES course_subject_code(course_subject_code_id)
);

-- Course Taught Table
CREATE TABLE course_taught (
    course_taught_id SERIAL PRIMARY KEY,
    term_code VARCHAR(255) NOT NULL,
    year INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    course_designation VARCHAR(255),
    section_number VARCHAR(255) NOT NULL,
    reference_held_with VARCHAR(255),
    reference_cross_listed VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    course_title VARCHAR(255),
    faculty_name VARCHAR(255),
    actual_enrollment INTEGER NOT NULL,
    maximum_enrollment INTEGER,
    graduate_enrollment INTEGER,
    upperclassmen_enrollment INTEGER,
    underclassmen_enrollment INTEGER,
    location VARCHAR(255),
    days VARCHAR(255),
    time VARCHAR(255),
    credit_hours NUMERIC,
    instruction_mode VARCHAR(255) NOT NULL,
    lab_hours NUMERIC,
    lecture_hours NUMERIC,
    extra_hours INTEGER,
    extra_hours_label VARCHAR(255),
    teaching_load INTEGER,
    weight INTEGER,
    custom_activity_classification VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (course_id) REFERENCES course(course_id),
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Secondary Unit Assignment Table
CREATE TABLE secondary_unit_assignment (
    secondary_unit_assignment_id SERIAL PRIMARY KEY,
    faculty_id INTEGER NOT NULL,
    second_unit_id INTEGER NOT NULL,
    second_unit_sub_group VARCHAR(255),
    second_unit_percentage NUMERIC NOT NULL,
    position_title VARCHAR(255) NOT NULL,
    position_category position_category NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (second_unit_id) REFERENCES unit(unit_id)
);

-- Support Account Table
CREATE TABLE support_account (
    support_account_id SERIAL PRIMARY KEY,
    uid VARCHAR(255) NOT NULL,
    faculty_id INTEGER NOT NULL,
    login_name VARCHAR(255) NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    work_email VARCHAR(255) NOT NULL,
    primary_unit_id INTEGER NOT NULL,
    type_of_rights access_rights NOT NULL,
    login_method login_method,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (primary_unit_id) REFERENCES unit(unit_id)
);

-- Committee Table
CREATE TABLE committee (
    committee_id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(255) NOT NULL,
    primary_unit_id INTEGER NOT NULL,
    effective_date DATE NOT NULL,
    previous_name VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (primary_unit_id) REFERENCES unit(unit_id)
);

-- Scholarly Outlet Table
CREATE TABLE scholarly_outlet (
    scholarly_outlet_id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    outlet_type VARCHAR(255),
    abbreviation VARCHAR(255),
    publisher VARCHAR(255),
    issn VARCHAR(255),
    eissn VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Scholarly Outlet Metric Table
CREATE TABLE scholarly_outlet_metric (
    scholarly_outlet_metric_id SERIAL PRIMARY KEY,
    scholarly_outlet_id INTEGER NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    metric_value VARCHAR(255) NOT NULL,
    metric_year INTEGER NOT NULL,
    outlet_type VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (scholarly_outlet_id) REFERENCES scholarly_outlet(scholarly_outlet_id)
);

-- Institution Role Table
CREATE TABLE institution_role (
    role_id SERIAL PRIMARY KEY,
    role_name VARCHAR(255) NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Faculty Role Table
CREATE TABLE faculty_institution_role (
    faculty_institution_role_id SERIAL PRIMARY KEY,
    faculty_id INTEGER NOT NULL,
    institution_role_id INTEGER NOT NULL,
    unit_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (institution_role_id) REFERENCES institution_role(role_id)
);

-- Unit Head Table
CREATE TABLE unit_head (
    unit_head_id SERIAL PRIMARY KEY,
    unit_id INTEGER NOT NULL,
    faculty_id INTEGER NOT NULL,
    start_date DATE NOT NULL DEFAULT CURRENT_DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (unit_id) REFERENCES unit(unit_id),
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    CONSTRAINT unique_active_unit_head UNIQUE (unit_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

