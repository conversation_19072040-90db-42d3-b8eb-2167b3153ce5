-- Changelog Table for tracking changes made by system_admin in EngRecords pages
CREATE TABLE changelog (
    changelog_id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    page_url VARCHAR(255) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(255) NOT NULL,
    sql_query TEXT NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES common.user(user_id)
);

-- Index for faster queries
CREATE INDEX idx_changelog_user_id ON changelog(user_id);
CREATE INDEX idx_changelog_table_name ON changelog(table_name);
CREATE INDEX idx_changelog_timestamp ON changelog(timestamp);
