-- Enum Types
CREATE TYPE contributor_role AS ENUM ('Author', 'Editor');
CREATE TYPE status_type AS ENUM ('In Progress', 'Submitted', 'Revise & Resubmit', 'Accepted', 'In Press', 'Completed/Published', 'Work Discontinued');

-- Contributor File Table (for Option 1)
CREATE TABLE contributor (
    contributor_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL,
    sor_id VARCHAR(255),
    faculty_id INTEGER, -- Nullable for external contributors
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    middle_initial VARCHAR(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    contributor_type contributor_role,
    percent_effort DOUBLE PRECISION,
    sort_order INTEGER,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Abstract Table
CREATE TABLE abstract (
    abstract_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    journal_title VARCHAR(255),
    series_title VARCHAR(255),
    month_season VARCHAR(255),
    year INTEGER,
    publisher VARCHAR(255),
    publisher_location VARCHAR(255),
    volume VARCHAR(255),
    issue_number_edition VARCHAR(255),
    page_numbers VARCHAR(255),
    issn VARCHAR(255),
    doi VARCHAR(255),
    keywords TEXT,
    pmid VARCHAR(255),
    pmcid VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Artistic and Professional Performances Table
CREATE TABLE artistic_performance (
    artistic_performance_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    work_title VARCHAR(255) NOT NULL,
    outlet_venue VARCHAR(255),
    sponsor VARCHAR(255),
    location VARCHAR(255),
    date_date_range VARCHAR(255),
    year INTEGER,
    co_contributors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Artistic and Professional Production Table
CREATE TABLE artistic_production (
    artistic_production_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    work_title VARCHAR(255) NOT NULL,
    outlet_venue VARCHAR(255),
    sponsor VARCHAR(255),
    location VARCHAR(255),
    date_date_range VARCHAR(255) NOT NULL,
    year INTEGER NOT NULL,
    co_contributors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Book Chapter Table
CREATE TABLE book_chapter (
    book_chapter_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    chapter_title VARCHAR(255) NOT NULL,
    book_title VARCHAR(255),
    series_title VARCHAR(255),
    year INTEGER,
    date_published VARCHAR(255),
    publisher VARCHAR(255),
    publisher_location VARCHAR(255),
    edition VARCHAR(255),
    page_number VARCHAR(255),
    issn VARCHAR(255),
    doi VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Book Table
CREATE TABLE book (
    book_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    series_title VARCHAR(255),
    year INTEGER,
    date_publication VARCHAR(255),
    publisher VARCHAR(255),
    publisher_city_state VARCHAR(255),
    volume VARCHAR(255),
    edition VARCHAR(255),
    number_of_pages VARCHAR(255),
    isbn VARCHAR(255),
    doi VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Case Study Table
CREATE TABLE case_study (
    case_study_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    year INTEGER,
    case_source_publisher VARCHAR(255),
    pages VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Conference Proceedings Table
CREATE TABLE conference_proceeding (
    conference_proceeding_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title_of_paper VARCHAR(255) NOT NULL,
    title_of_published_proceedings VARCHAR(255),
    title_of_conference VARCHAR(255),
    conference_location VARCHAR(255),
    month_season VARCHAR(255),
    year INTEGER,
    publisher VARCHAR(255),
    publisher_location VARCHAR(255),
    volume VARCHAR(255),
    issue_number_edition VARCHAR(255),
    page_numbers VARCHAR(255),
    doi VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Dataset Table
CREATE TABLE dataset (
    dataset_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    publisher_sponsor VARCHAR(255),
    date VARCHAR(255),
    location VARCHAR(255),
    repository VARCHAR(255),
    keywords TEXT,
    doi VARCHAR(255),
    source VARCHAR(255),
    last_update VARCHAR(255),
    co_contributors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Digital and Electronic Media, Social Media, Blog, Podcast Table
CREATE TABLE digital_media (
    digital_media_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    year INTEGER,
    pages VARCHAR(255),
    publisher_platform VARCHAR(255),
    location VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Exhibition Table
CREATE TABLE exhibition (
    exhibition_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    work_title VARCHAR(255) NOT NULL,
    outlet_venue VARCHAR(255),
    sponsor VARCHAR(255),
    location VARCHAR(255),
    date_date_range VARCHAR(255) NOT NULL,
    year INTEGER NOT NULL,
    co_contributors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Journal Article Table
CREATE TABLE journal_article (
    journal_article_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    journal_title VARCHAR(255),
    series_title VARCHAR(255),
    month_season VARCHAR(255),
    year INTEGER,
    publisher VARCHAR(255),
    publisher_location VARCHAR(255),
    volume VARCHAR(255),
    issue_number_edition VARCHAR(255),
    page_numbers VARCHAR(255),
    issn VARCHAR(255),
    doi VARCHAR(255),
    pmid VARCHAR(255),
    pmcid VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Other Scholarly Work Table
CREATE TABLE other_scholarly_work (
    other_scholarly_work_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    journal_book_volume_title VARCHAR(255),
    series_title VARCHAR(255),
    month VARCHAR(255),
    year INTEGER,
    publisher VARCHAR(255),
    location_of_journal_publisher VARCHAR(255),
    volume VARCHAR(255),
    issue_number_edition VARCHAR(255),
    page_numbers VARCHAR(255),
    isbn_issn_case_number VARCHAR(255),
    co_contributors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Other Teaching Materials Table
CREATE TABLE other_teaching_material (
    other_teaching_material_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    year INTEGER,
    organization VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Patent and Intellectual Property Table
CREATE TABLE patent (
    patent_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    year INTEGER,
    patent_copyright_number_id VARCHAR(255),
    patent_type VARCHAR(255),
    patent_nationality VARCHAR(255),
    patent_cooperation_treaty_nations TEXT,
    co_contributors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Poster Presentation Table
CREATE TABLE poster_presentation (
    poster_presentation_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title_of_presentation VARCHAR(255) NOT NULL,
    conference_meeting_name VARCHAR(255),
    location_of_conference_meeting VARCHAR(255),
    month_season VARCHAR(255) NOT NULL,
    year INTEGER NOT NULL,
    sponsoring_organization VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Presentation Table
CREATE TABLE presentation (
    presentation_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title_of_presentation VARCHAR(255) NOT NULL,
    conference_meeting_name VARCHAR(255),
    location_of_conference_meeting VARCHAR(255),
    month_season VARCHAR(255),
    year INTEGER,
    sponsoring_organization VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Review Table
CREATE TABLE review (
    review_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    outlet VARCHAR(255),
    month_season VARCHAR(255),
    year INTEGER,
    publisher VARCHAR(255),
    publisher_location VARCHAR(255),
    volume VARCHAR(255),
    issue_number_edition VARCHAR(255),
    page_numbers VARCHAR(255),
    co_authors TEXT, -- For Option 2
    co_editors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Technology Table
CREATE TABLE technology (
    technology_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_percent_contribution DOUBLE PRECISION,
    faculty_role contributor_role,
    status status_type NOT NULL,
    status_date DATE NOT NULL,
    title VARCHAR(255) NOT NULL,
    outlet VARCHAR(255),
    year INTEGER,
    co_contributors TEXT, -- For Option 2
    url VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);