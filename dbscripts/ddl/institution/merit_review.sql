-- Merit Review Workflow Tables

-- Merit Workflow Config Table
CREATE TABLE merit_workflow_config (
    id SERIAL PRIMARY KEY,
    unit_id INTEGER NOT NULL,
    start_dt DATE NOT NULL,
    end_dt DATE NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- draft, active, completed, cancelled
    description VARCHAR(255),
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (unit_id) REFERENCES unit(unit_id),
    FOREIGN KEY (created_by) REFERENCES common.user(user_id)
);

-- Merit Review Committee Table
CREATE TABLE merit_review_committee (
    id SERIAL PRIMARY KEY,
    unit_id INTEGER NOT NULL,
    committee_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    faculty_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (unit_id) REFERENCES unit(unit_id),
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Merit Report Table
CREATE TABLE merit_report (
    id SERIAL PRIMARY KEY,
    faculty_id INTEGER NOT NULL,
    unit_id INTEGER NOT NULL,
    report_doc TEXT NOT NULL,
    create_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_dt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- draft, submitted, under_review, reviewed, approved
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (unit_id) REFERENCES unit(unit_id)
);

-- Merit Review Rating Table
CREATE TABLE merit_review_rating (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    reviewer_id INTEGER NOT NULL,
    teaching_rating INTEGER NOT NULL,
    research_rating INTEGER NOT NULL,
    service_rating INTEGER NOT NULL,
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id),
    FOREIGN KEY (reviewer_id) REFERENCES faculty(faculty_id)
);

-- Merit Conflict of Interest Table
CREATE TABLE merit_conflict_of_interest (
    id SERIAL PRIMARY KEY,
    committee_member_id INTEGER NOT NULL,
    faculty_id INTEGER NOT NULL,
    reason TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (committee_member_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Merit Preliminary Rating Table
CREATE TABLE merit_preliminary_rating (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    unit_head_id INTEGER NOT NULL,
    teaching_rating INTEGER NOT NULL,
    research_rating INTEGER NOT NULL,
    service_rating INTEGER NOT NULL,
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id),
    FOREIGN KEY (unit_head_id) REFERENCES faculty(faculty_id)
);

-- Merit Final Rating Table
CREATE TABLE merit_final_rating (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    teaching_rating INTEGER NOT NULL,
    research_rating INTEGER NOT NULL,
    service_rating INTEGER NOT NULL,
    comments TEXT,
    approved_by UUID NOT NULL,
    approved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'dean_approved', -- dean_approved, provost_approved, hr_processed
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id),
    FOREIGN KEY (approved_by) REFERENCES common.user(user_id)
);

-- Merit Disagreement Table
CREATE TABLE merit_disagreement (
    id SERIAL PRIMARY KEY,
    report_id INTEGER NOT NULL,
    disagreement_text TEXT NOT NULL,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'submitted', -- submitted, under_review, resolved
    unit_head_recommendation TEXT,
    dean_decision TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES merit_report(id)
);

-- Create indexes for better performance
CREATE INDEX idx_merit_workflow_config_unit_id ON merit_workflow_config(unit_id);
CREATE INDEX idx_merit_review_committee_unit_id ON merit_review_committee(unit_id);
CREATE INDEX idx_merit_review_committee_faculty_id ON merit_review_committee(faculty_id);
CREATE INDEX idx_merit_report_faculty_id ON merit_report(faculty_id);
CREATE INDEX idx_merit_report_unit_id ON merit_report(unit_id);
CREATE INDEX idx_merit_review_rating_report_id ON merit_review_rating(report_id);
CREATE INDEX idx_merit_review_rating_reviewer_id ON merit_review_rating(reviewer_id);
CREATE INDEX idx_merit_conflict_of_interest_committee_member_id ON merit_conflict_of_interest(committee_member_id);
CREATE INDEX idx_merit_conflict_of_interest_faculty_id ON merit_conflict_of_interest(faculty_id);
CREATE INDEX idx_merit_preliminary_rating_report_id ON merit_preliminary_rating(report_id);
CREATE INDEX idx_merit_final_rating_report_id ON merit_final_rating(report_id);
CREATE INDEX idx_merit_disagreement_report_id ON merit_disagreement(report_id);
