-- Enum Type for Access Levels
CREATE TYPE access_level AS ENUM (
    'No Access Granted',
    'Allow Access Only',
    'Allow Access & Allow to Grant Access'
);

-- Permission Categories Table
CREATE TABLE permission_category (
    permission_category_id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL UNIQUE, -- e.g., 'Reports', 'Administration', 'Communications', 'Setup'
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Permissions Table
CREATE TABLE permission (
    permission_id SERIAL PRIMARY KEY,
    permission_category_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL, -- e.g., 'Profile Report', 'Initiate Faculty Input Workflow'
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (permission_category_id) REFERENCES permission_category(permission_category_id),
    CONSTRAINT unique_permission_per_category UNIQUE (permission_category_id, name)
);

-- Unit Permissions Table
CREATE TABLE unit_permission (
    unit_permission_id SERIAL PRIMARY KEY,
    unit_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    access_level access_level NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (unit_id) REFERENCES unit(unit_id),
    FOREIGN KEY (permission_id) REFERENCES permission(permission_id),
    CONSTRAINT unique_unit_permission UNIQUE (unit_id, permission_id)
);


-- Insert Permission Categories
INSERT INTO permission_category (name, description) VALUES
    ('Reports', 'Access to various reporting functionalities'),
    ('Administration', 'Administrative tasks and settings'),
    ('Communications', 'Communication tools for faculty and administrators'),
    ('Setup', 'Configuration and setup options for the system');

-- Insert Permissions (example subset)
INSERT INTO permission (permission_category_id, name, description) VALUES
    ((SELECT permission_category_id FROM permission_category WHERE name = 'Reports'), 'Profile Report', 'Personal information, work experience, degrees, licenses, memberships, honors, interests, biographies, and more'),
    ((SELECT permission_category_id FROM permission_category WHERE name = 'Reports'), 'Scholarly Activities', 'Research, publications, and creative productions'),
    ((SELECT permission_category_id FROM permission_category WHERE name = 'Administration'), 'Initiate Faculty Input Workflow', 'Initiate formal faculty activity input workflow'),
    ((SELECT permission_category_id FROM permission_category WHERE name = 'Communications'), 'Email Faculty', 'Send an e-mail message to selected faculty and administrators'),
    ((SELECT permission_category_id FROM permission_category WHERE name = 'Setup'), 'Organizational Structure', 'Set up the structure of academic units and subunits');

-- Insert Unit Permissions (example for a University unit)
INSERT INTO unit_permission (unit_id, permission_id, access_level) VALUES
    (1, (SELECT permission_id FROM permission WHERE name = 'Profile Report'), 'Allow Access & Allow to Grant Access'),
    (1, (SELECT permission_id FROM permission WHERE name = 'Scholarly Activities'), 'Allow Access & Allow to Grant Access'),
    (1, (SELECT permission_id FROM permission WHERE name = 'Initiate Faculty Input Workflow'), 'Allow Access & Allow to Grant Access'),
    (1, (SELECT permission_id FROM permission WHERE name = 'Email Faculty'), 'Allow Access & Allow to Grant Access'),
    (1, (SELECT permission_id FROM permission WHERE name = 'Organizational Structure'), 'Allow Access & Allow to Grant Access');
    