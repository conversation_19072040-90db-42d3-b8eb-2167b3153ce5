-- Enum Types
CREATE TYPE training_type AS ENUM ('Post-Doctorate', 'Residency', 'Internship', 'Fellowship', 'Post-Baccalaureate');
CREATE TYPE degree_level AS ENUM ('Doctoral', 'Masters', 'Bachelors', 'Associates', 'Diploma', 'Certificate', 'Other');
CREATE TYPE military_branch AS ENUM ('Air Force', 'Air National Guard', 'Army', 'Coast Guard', 'Marine Corps', 'National Guard', 'Navy');
CREATE TYPE military_status AS ENUM ('Inactive', 'Active Duty', 'Reserves', 'Retired', 'Inactive Reserves');
CREATE TYPE licensure_type AS ENUM ('Medical License', 'DEA', 'Other');
CREATE TYPE licensure_status AS ENUM ('Active', 'Expired');
CREATE TYPE cert_status AS ENUM ('Applied', 'Approved', 'Cancelled', 'Withdrawn');

-- Additional Training Table
CREATE TABLE additional_training (
    additional_training_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    institution VARCHAR(255) NOT NULL,
    specialty VARCHAR(255) NOT NULL,
    type training_type NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Administrative Appointments Table
CREATE TABLE administrative_appointment (
    administrative_appointment_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_record_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    position_title VARCHAR(255) NOT NULL,
    service_unit_id INTEGER,
    start_date DATE NOT NULL,
    end_date DATE,
    duties TEXT,
    teaching_release INTEGER,
    weight_teaching_percent NUMERIC NOT NULL,
    weight_research_percent NUMERIC NOT NULL,
    weight_service_percent NUMERIC NOT NULL,
    internal_research_fund NUMERIC,
    other_incentives TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (service_unit_id) REFERENCES unit(unit_id)
);

-- Biography Table
CREATE TABLE biography (
    biography_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    date_of_last_update DATE NOT NULL,
    faculty_id INTEGER NOT NULL,
    biography TEXT NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Degrees Table
CREATE TABLE degree (
    degree_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_record_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    level_of_degree degree_level NOT NULL,
    full_official_degree_name VARCHAR(255) NOT NULL,
    degree_abbreviation VARCHAR(255) NOT NULL,
    year_conferred_or_in_progress VARCHAR(255) NOT NULL, -- Supports "YYYY" or "In Progress"
    highest_degree_earned yes_no NOT NULL,
    terminal_degree yes_no,
    discipline VARCHAR(255),
    granting_institution VARCHAR(255),
    city VARCHAR(255),
    province_or_state VARCHAR(255),
    country VARCHAR(255),
    title_of_dissertation_or_thesis TEXT,
    cip_code VARCHAR(6),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Hospital and Clinical Appointments Table
CREATE TABLE hospital_clinical_appointment (
    hospital_clinical_appointment_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    project_title VARCHAR(255) NOT NULL,
    hospital_clinic VARCHAR(255) NOT NULL,
    start_date VARCHAR(255) NOT NULL, -- Using VARCHAR due to "mon, year" format
    end_date VARCHAR(255) NOT NULL,   -- Using VARCHAR due to "mon, year" format
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Interests Table
CREATE TABLE interest (
    interest_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    date_of_last_update DATE NOT NULL,
    faculty_id INTEGER NOT NULL,
    teaching_interests TEXT NOT NULL,
    research_interests TEXT NOT NULL,
    service_interests TEXT NOT NULL,
    clinical_interests TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Military Experience Table
CREATE TABLE military_experience (
    military_experience_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    branch_of_military military_branch NOT NULL,
    military_status military_status NOT NULL,
    start_month_year VARCHAR(255) NOT NULL, -- Using VARCHAR due to "January 2020" format
    end_month_year VARCHAR(255) NOT NULL,   -- Using VARCHAR due to "January 2020" format
    military_career_narrative TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Professional Licensure (Medical and Health Schools) Table
CREATE TABLE professional_licensure_medical (
    professional_licensure_medical_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    type_of_licensure licensure_type NOT NULL,
    state_or_province VARCHAR(255) NOT NULL,
    country VARCHAR(255),
    license_number VARCHAR(255),
    status licensure_status NOT NULL,
    year_conferred INTEGER NOT NULL,
    expiration_date DATE NOT NULL,
    npi_number VARCHAR(255),
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Professional Licensures & Certifications Table
CREATE TABLE professional_licensure_certification (
    professional_licensure_certification_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    license_certificate_name VARCHAR(255) NOT NULL,
    month_year_conferred VARCHAR(255) NOT NULL, -- Using VARCHAR due to "January 2020" format
    month_year_expires VARCHAR(255),            -- Using VARCHAR due to "January 2020" format
    issuing_authority VARCHAR(255),
    province_or_state_of_authority VARCHAR(255),
    country VARCHAR(255),
    number VARCHAR(255),
    type VARCHAR(255),
    date_of_last_update DATE,
    current_status cert_status,
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Rank and Promotion History Table
CREATE TABLE rank_promotion_history (
    rank_promotion_history_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    faculty_rank VARCHAR(255) NOT NULL,
    tenure_permanence_status VARCHAR(255) NOT NULL,
    home_unit_id INTEGER,
    institution VARCHAR(255) NOT NULL,
    effective_date_of_rank DATE NOT NULL,
    end_date_of_rank DATE,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    FOREIGN KEY (home_unit_id) REFERENCES unit(unit_id)
);

-- Work Experience Table
CREATE TABLE work_experience (
    work_experience_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    sor_id VARCHAR(255),
    faculty_id INTEGER NOT NULL,
    position_title VARCHAR(255) NOT NULL,
    organization VARCHAR(255) NOT NULL,
    city VARCHAR(255),
    province_state VARCHAR(255),
    country VARCHAR(255),
    start_year INTEGER NOT NULL,
    end_year INTEGER NOT NULL,
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Profile Custom Section Table (simplified due to dynamic nature)
CREATE TABLE profile_custom_section (
    profile_custom_section_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);