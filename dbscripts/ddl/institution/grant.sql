-- Enum Types
CREATE TYPE contributor_type AS ENUM ('PI', 'Co-PI', 'Co-Investigator', 'Program Coordinator', 'Other', 'Multiple PI');
CREATE TYPE proposal_status AS ENUM ('In Preparation - Not Submitted', 'Submitted for Review', 'Funded - In Progress', 
    'Completed', 'Submitted - Not Funded', 'Work Discontinued', 'Withdrawn');
CREATE TYPE award_status AS ENUM ('In Preparation - Not Submitted', 'Submitted for Review', 'Funded - In Progress', 
    'Completed', 'Submitted - Not Funded', 'Work Discontinued', 'Withdrawn');
CREATE TYPE yes_no AS ENUM ('Yes', 'No');
CREATE TYPE internal_external AS ENUM ('Internal', 'External');
CREATE TYPE funding_source_category AS ENUM ('Corporate', 'Federal', 'Foundation', 'Institutional', 
    'Not for Profit', 'Province / State', 'Other');
CREATE TYPE grant_type AS ENUM ('Research', 'Fee-for-Service', 'Training', 'Program');

-- Currency Enum (limited to key currencies for brevity; full list can be added as needed)
CREATE TYPE currency AS ENUM ('CAD', 'USD', 'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AUD', 
    'AWG', 'AZN', 'BAM', 'BBD', 'BDT', 'BGN', 'BHD', 'BIF', 'BMD', 'BND', 'BOB', 'BRL', 'BSD', 
    'BTN', 'BWP', 'BYR', 'BZD', 'CDF', 'CHF', 'CLP', 'CNY', 'COP', 'CRC', 'CUC', 'CUP', 'CVE', 
    'CZK', 'DJF', 'DKK', 'DOP', 'DZD', 'EGP', 'ERN', 'ETB', 'EUR', 'FJD', 'FKP', 'GBP', 'GEL', 
    'GGP', 'GHS', 'GIP', 'GMD', 'GNF', 'GTQ', 'GYD', 'HKD', 'HNL', 'HRK', 'HTG', 'HUF', 'IDR', 
    'ILS', 'IMP', 'INR', 'IQD', 'IRR', 'ISK', 'JEP', 'JMD', 'JOD', 'JPY', 'KES', 'KGS', 'KHR', 
    'KMF', 'KPW', 'KRW', 'KWD', 'KYD', 'KZT', 'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'LTL', 'LVL', 
    'LYD', 'MAD', 'MDL', 'MGA', 'MKD', 'MMK', 'MNT', 'MOP', 'MRO', 'MUR', 'MVR', 'MWK', 'MXN', 
    'MYR', 'MZN', 'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'NZD', 'OMR', 'PAB', 'PEN', 'PGK', 'PHP', 
    'PKR', 'PLN', 'PRB', 'PYG', 'QAR', 'RON', 'RSD', 'RUB', 'RWF', 'SAR', 'SBD', 'SCR', 'SDG', 
    'SEK', 'SGD', 'SHP', 'SLL', 'SOS', 'SRD', 'SSP', 'STD', 'SVC', 'SYP', 'SZL', 'THB', 'TJS', 
    'TMT', 'TND', 'TOP', 'TRY', 'TTD', 'TWD', 'TZS', 'UAH', 'UGX', 'UYU', 'UZS', 'VEF', 'VND', 
    'VUV', 'WST', 'XAF', 'XCD', 'XOF', 'XPF', 'YER', 'ZAR', 'ZMW', 'ZWL');

-- Proposal Table
CREATE TABLE proposal (
    proposal_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    contributor_type contributor_type,
    percent_effort DOUBLE PRECISION,
    status proposal_status NOT NULL,
    status_date DATE,
    title VARCHAR(255) NOT NULL,
    funding_agency_sponsor VARCHAR(255),
    proposal_id_external VARCHAR(255) NOT NULL, -- Renamed to avoid conflict with primary key
    abstract TEXT,
    url VARCHAR(255),
    description TEXT,
    external_collaborators TEXT, -- For Option 1
    award_date DATE,
    proposed_start_date DATE NOT NULL,
    proposed_end_date DATE,
    period_start_date DATE,
    period_end_date DATE,
    has_indirect_funding yes_no,
    indirect_cost_rate DOUBLE PRECISION,
    currency currency DEFAULT 'CAD',
    period_total_funding DOUBLE PRECISION,
    period_total_direct_funding DOUBLE PRECISION,
    proposal_total_funding DOUBLE PRECISION,
    proposal_total_direct_funding DOUBLE PRECISION,
    approval_for_pre_award_spending yes_no,
    internal_external internal_external,
    funding_source_category funding_source_category,
    type_of_grant grant_type,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Award Table
CREATE TABLE award (
    award_id SERIAL PRIMARY KEY,
    record_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER NOT NULL,
    contributor_type contributor_type,
    percent_effort DOUBLE PRECISION,
    status award_status NOT NULL,
    status_date DATE,
    title VARCHAR(255) NOT NULL,
    funding_agency_sponsor VARCHAR(255),
    award_id_external VARCHAR(255) NOT NULL, -- Renamed to avoid conflict with primary key
    proposal_id_external VARCHAR(255), -- Links to proposal if applicable
    abstract TEXT,
    url VARCHAR(255),
    description TEXT,
    external_collaborators TEXT, -- For Option 1
    award_date DATE,
    award_start_date DATE NOT NULL,
    award_end_date DATE,
    period_start_date DATE,
    period_end_date DATE,
    has_indirect_funding yes_no,
    indirect_cost_rate DOUBLE PRECISION,
    currency currency DEFAULT 'USD',
    period_total_funding DOUBLE PRECISION,
    period_total_direct_funding DOUBLE PRECISION,
    award_total_funding DOUBLE PRECISION,
    award_total_direct_funding DOUBLE PRECISION,
    approval_for_pre_award_spending yes_no,
    internal_external internal_external,
    funding_source_category funding_source_category,
    type_of_grant grant_type,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id)
);

-- Collaborator Table (for Option 2)
CREATE TABLE grant_collaborator (
    grant_collaborator_id SERIAL PRIMARY KEY,
    award_id INTEGER,
    proposal_id INTEGER,
    faculty_id INTEGER, -- Nullable for external collaborators
    first_name VARCHAR(255),
    middle_initial VARCHAR(255),
    last_name VARCHAR(255),
    home_institution VARCHAR(255), -- Required for external collaborators
    email VARCHAR(255),
    contributor_type contributor_type,
    percent_effort DOUBLE PRECISION,
    sort_order INTEGER,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (award_id) REFERENCES award(award_id),
    FOREIGN KEY (proposal_id) REFERENCES proposal(proposal_id),
    FOREIGN KEY (faculty_id) REFERENCES faculty(faculty_id),
    CONSTRAINT check_award_or_proposal CHECK (
        (award_id IS NOT NULL AND proposal_id IS NULL) OR 
        (award_id IS NULL AND proposal_id IS NOT NULL)
    )
);