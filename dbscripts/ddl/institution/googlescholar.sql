-- Create the googlescholar schema in the institution database
CREATE SCHEMA IF NOT EXISTS googlescholar;

-- Table to store faculty search information
CREATE TABLE googlescholar.faculty_search (
    search_id SERIAL PRIMARY KEY,
    faculty_id INTEGER NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    search_query VARCHAR(255) NOT NULL,
    scholar_id VARCHAR(255),
    search_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'pending', -- pending, completed, failed
    error_message TEXT,
    FOREIGN KEY (faculty_id) REFERENCES uw.faculty(faculty_id)
);

-- Table to store author profiles
CREATE TABLE googlescholar.author_profile (
    author_id SERIAL PRIMARY KEY,
    scholar_id VARCHAR(255) NOT NULL UNIQUE,
    faculty_id INTEGER,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    affiliation VARCHAR(255),
    email_domain VARCHAR(255),
    areas_of_interest TEXT,
    homepage_url VARCHAR(255),
    citations_all INTEGER,
    citations_since_2019 INTEGER,
    h_index_all INTEGER,
    h_index_since_2019 INTEGER,
    i10_index_all INTEGER,
    i10_index_since_2019 INTEGER,
    profile_url VARCHAR(255) NOT NULL,
    profile_image_url VARCHAR(255),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (faculty_id) REFERENCES uw.faculty(faculty_id)
);

-- Table to store publications
CREATE TABLE googlescholar.publication (
    publication_id SERIAL PRIMARY KEY,
    scholar_id VARCHAR(255) NOT NULL,
    citation_id VARCHAR(255),
    title TEXT NOT NULL,
    authors TEXT NOT NULL,
    venue TEXT,
    year INTEGER,
    citations INTEGER DEFAULT 0,
    publication_url TEXT,
    scholar_url TEXT,
    pdf_url TEXT,
    abstract TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (scholar_id) REFERENCES googlescholar.author_profile(scholar_id)
);

-- Table to store co-authors
CREATE TABLE googlescholar.co_author (
    co_author_id SERIAL PRIMARY KEY,
    author_id INTEGER NOT NULL,
    co_author_name VARCHAR(255) NOT NULL,
    co_author_scholar_id VARCHAR(255),
    co_author_affiliation VARCHAR(255),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (author_id) REFERENCES googlescholar.author_profile(author_id)
);

-- Table to store citation history
CREATE TABLE googlescholar.citation_history (
    citation_id SERIAL PRIMARY KEY,
    author_id INTEGER NOT NULL,
    year INTEGER NOT NULL,
    citations INTEGER NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(author_id, year),
    FOREIGN KEY (author_id) REFERENCES googlescholar.author_profile(author_id)
);

-- Table to store scraping logs
CREATE TABLE googlescholar.scrape_log (
    log_id SERIAL PRIMARY KEY,
    faculty_id INTEGER,
    scholar_id VARCHAR(255),
    log_type VARCHAR(50) NOT NULL, -- search, profile, publications, co-authors, citations
    status VARCHAR(50) NOT NULL, -- success, failed
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (faculty_id) REFERENCES uw.faculty(faculty_id)
);

-- Create indexes for better performance
CREATE INDEX idx_faculty_search_faculty_id ON googlescholar.faculty_search(faculty_id);
CREATE INDEX idx_faculty_search_status ON googlescholar.faculty_search(status);
CREATE INDEX idx_author_profile_faculty_id ON googlescholar.author_profile(faculty_id);
CREATE INDEX idx_author_profile_scholar_id ON googlescholar.author_profile(scholar_id);
CREATE INDEX idx_publication_scholar_id ON googlescholar.publication(scholar_id);
CREATE INDEX idx_publication_year ON googlescholar.publication(year);
CREATE INDEX idx_co_author_author_id ON googlescholar.co_author(author_id);
CREATE INDEX idx_citation_history_author_id ON googlescholar.citation_history(author_id);
CREATE INDEX idx_scrape_log_faculty_id ON googlescholar.scrape_log(faculty_id);
CREATE INDEX idx_scrape_log_created_at ON googlescholar.scrape_log(created_at);
