we have two schemas: common and uw
common is mainly for system level data for example user, password etc
uw is for waterloo data

main tables in uw
unit: 
defines institution/division/faculty/department name and relationship
id is same as in workday and is used as primary key for the table

position: 
defines position, 
id is same as in workday and is used as primary key for the table

faculty:
include both faculty and staff data

institution_role:
include  roles used in uw
