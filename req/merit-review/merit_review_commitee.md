when user with role department_admin login in and navigate to merit review committee, system will first find his home department by using uw.faculty.primary_unit_id, and then find all the regular faculty in this department by uw.faculty.primary_unit_id = login user's primary_unit_id, and then find all the committee members in this department. 
committee members are in table uw.merit_review_committee, and the regular faculty are in table uw.faculty with job_family = 'Regular Faculty'.
on the committee page, system will display all the committee members in this department, and all the regular faculty in this department. and user can add new committee member by select a regular faculty from the list, or remove a committee member by click the remove button.
when save committee members, system will save the committee members in table uw.merit_review_committee, the committee_name is the department name + Merit Review Committee.
