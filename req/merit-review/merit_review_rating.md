for user that is in department_merit_committee, show a list of all merit reports that are in 'submitted' status in this user's department, and the faculty_id is not the same as the login user's faculty_id.
the user can enter rating for each category (teaching, research, service) and comments for each report.
rating will be saved in table uw.merit_review_ratings, user can enter or update the rating before a deadline, they can also submit the rating before the deadline, once submit, they can no longer change the rating.
for the rating page, show a list of all reports that match the criteria above, each row contains:
faculty name, unit name, report document (link to s3), rating status (not started, in progress, submitted), date created, date updated.

merit committee member can only view the ratings they have entered, they can't view ratings entered by other committee members.
department_admin and faculty_admin can view all ratings

Unit Heads (department_approver or their delegate department_admin) enters preliminary ratings;
preliminary ratings are entered in table uw.merit_preliminary_rating, once submitted, they can no longer change the rating. 
faculty_admin can view all preliminary ratings, but can not edit
faculty_approver can view and edit all preliminary ratings
 
