when user click 'Create Merit Report', use data in perceptions.course_evaluations to populate uw.merit_report_teaching_courses, logic as follows:
1. use login user facultySsoId to match userid in perceptions.course_evaluations
2. use logic in @term_def.md to convert term_id to term_year
3. use course_id as course_number
4. use average of q1_avg, q2_avg, q3_avg as scp_q1_q3
5. use average of q4_avg, q5_avg, q6_avg as scp_q4_q6
6. use class_size as num_students
7. get average of q1_responses, q2_responses, q3_responses, q4_responses, q5_responses, q6_responses as average_responses, then average_responses / class_size * 100 as response_percentage, keep 2 decimal places
8. use average of q1_std, q2_std, q3_std as scp_q1_q3_std_dev
9. use average of q4_std, q5_std, q6_std as scp_q4_q6_std_dev
10. 




