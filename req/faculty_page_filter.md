for this page dashboard/engrecords/faculty, add a filter bar so user can filter by engrecords.fac_group or fac_org_unit
there are two dropdowns, one for engrecords.fac_group, one for engrecords.fac_org_unit
when user select a dropdown item, the page will add a filter label on the top right corner of the page, and the filter label will have a close button, when user click the close button, the filter will be removed
user can select multiple labels, for the items in the same dropdown, the filter will be OR, for the items in different dropdown, the filter will be AND
the defaut filter is engrecords.fac_group = 'Faculty' so there will be one filter label on the top right corner of the page by default
pay attention to the pagination, when user select a filter, the page will refresh and show the first page of the filtered results, and the pagination will be based on the filtered results