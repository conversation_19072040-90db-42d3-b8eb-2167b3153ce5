when user click 'Create Merit Report', use data in infoed.research_funding to populate uw.merit_report_research_grants, logic as follows:
1. use login user facultyId to match faculty_id in infoed.research_funding
2. use award_year as installment_year
3. use total_award as amount
4. use sponsor_name as agency
5. use project_title as title
6. use researcher as pi_name
7. use 100 as share_percentage
8. use 'awarded' as status
10. use sponser_type as grant_type

and on the 'merit report submission', research -> grants, display the populated data as:
PI(Collaborators),	Title & Agency,	Instalment Amount,	Instalment Year,	Your sharein %
