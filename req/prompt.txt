similar to this file, create a new file that also calls openai api using gpt-40-mini model, the input is a very long text so we need split it into chunks and send to the model, in the beginning contains the table of contents of the document, 
use the structure in the table of contents to create a json response that has the same structure, and then for the 



similar to this file, create a new file that also calls openai api using gpt-40-mini model, the input is a very long text so we need split it into chunks and send to the model, 
extract list of publications and for each publication extract authors, title, journal, editors, year, pages. 
      Return the result as a JSON object. If a field is not found, leave it as an empty string.
also extract list of activites  and for each activity extract date, venue, attendee, publication, summary.
      Return the result as a JSON object. If a field is not found, leave it as an empty string.
      make sure any date is in the format YYYY-MM-DD. 





create database schema scripts from the files for postgresql, pay attention to these:
cover every sheet in the excel file
each table should have a primary key named as tablename_id
each table should have a soft delete column added
create enum types where applicable
for string type if length is not specified then use VARCHAR(255)
for foreign key use the primary key on the other table
when unit_abbreviation are used as reference for foreign key, use unit_id instead
do not use plural form for table name




for these table used for authentication and authorization I put them in a schema called common, common schema holds all the common or system level tables 
our clients are institutions like universiteis, our design is every institution will have it's own schema, but they will have the exactly same DDL
any change to one institution schema need to be applied to all institutions
for start, we will have:
uw for university of waterloo
ic for intelicampus, this is a public space for any registered user that does not have or use institution SSO
in the future we may have uot for university of toronto, yu for york university etc.
now with this database schema design, and the nextjs related code I attach here, please help me to  implement detailed login  and authorization functions,
including:
when users login using SSO, system will check if they already registered, if not system will register them and insert related record to corresponding tables
if users are using institutional SSO, then they will be marked as the corresponding institutional user, we can add another table define the realtionship between user and institution



for these table used for authentication and authorization I put them in a schema called common, common schema holds all the common or system level tables 
there is another schema uw, stands for university of waterloo, which contains specific tables for faculty information management
now with this database schema design, and the nextjs related code I attach here, please help me to  implement detailed login  and authorization functions,
including:
when users login using SSO, system will check if they already registered, if not system will register them and insert related record to corresponding tables
new registered users have the default role of professor, who can only see and upload his own files and records
what's a suggested way to create system admin user? system admin can assign roles to users to become institutional admin 