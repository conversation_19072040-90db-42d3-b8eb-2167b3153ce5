import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { sql } from '@/app/lib/db';
import { GET, POST, DELETE } from '@/app/api/merit-review/committee/department/route';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/app/lib/db');

describe('Merit Review Committee Department API', () => {
  const mockSession = {
    user: {
      id: 'user-123',
      email: '<EMAIL>',
      roles: ['department_admin'],
    },
  };

  const mockFaculty = {
    faculty_id: 1,
    primary_unit_id: 100,
  };

  const mockUnit = {
    full_name: 'Test Department',
  };

  const mockRegularFaculty = [
    {
      faculty_id: 1,
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      full_name: '<PERSON>',
      work_email: '<EMAIL>',
    },
    {
      faculty_id: 2,
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      full_name: '<PERSON>',
      work_email: '<EMAIL>',
    },
  ];

  const mockCommitteeMembers = [
    {
      id: 1,
      faculty_id: 1,
      faculty_name: '<PERSON>',
      created_at: '2023-01-01T00:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (getServerSession as jest.Mock).mockResolvedValue(mockSession);
    (sql as jest.Mock).mockImplementation((query: string, ...params: any[]) => {
      // Mock different SQL queries based on the query string
      if (query.includes('SELECT faculty_id, primary_unit_id')) {
        return [mockFaculty];
      } else if (query.includes('SELECT full_name')) {
        return [mockUnit];
      } else if (query.includes('job_family = \'Regular Faculty\'')) {
        return mockRegularFaculty;
      } else if (query.includes('merit_review_committee')) {
        return mockCommitteeMembers;
      } else if (query.includes('INSERT INTO')) {
        return [{ id: 2, unit_id: 100, faculty_id: 2 }];
      } else if (query.includes('UPDATE')) {
        return [];
      }
      return [];
    });
  });

  describe('GET', () => {
    it('should return committee data for department admin', async () => {
      const req = new NextRequest('http://localhost/api/merit-review/committee/department');
      const res = await GET(req);
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toHaveProperty('unitId', 100);
      expect(data).toHaveProperty('unitName', 'Test Department');
      expect(data).toHaveProperty('regularFaculty');
      expect(data).toHaveProperty('committeeMembers');
    });

    it('should return 401 if not authenticated', async () => {
      (getServerSession as jest.Mock).mockResolvedValue(null);
      const req = new NextRequest('http://localhost/api/merit-review/committee/department');
      const res = await GET(req);

      expect(res.status).toBe(401);
    });

    it('should return 403 if not department admin', async () => {
      (getServerSession as jest.Mock).mockResolvedValue({
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['regular_user'],
        },
      });
      const req = new NextRequest('http://localhost/api/merit-review/committee/department');
      const res = await GET(req);

      expect(res.status).toBe(403);
    });
  });

  describe('POST', () => {
    it('should add a new committee member', async () => {
      const req = new NextRequest('http://localhost/api/merit-review/committee/department', {
        method: 'POST',
        body: JSON.stringify({
          unit_id: 100,
          faculty_id: 2,
        }),
      });
      const res = await POST(req);
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toHaveProperty('id', 2);
      expect(data).toHaveProperty('unit_id', 100);
      expect(data).toHaveProperty('faculty_id', 2);
    });

    it('should return 400 if missing required fields', async () => {
      const req = new NextRequest('http://localhost/api/merit-review/committee/department', {
        method: 'POST',
        body: JSON.stringify({}),
      });
      const res = await POST(req);

      expect(res.status).toBe(400);
    });
  });

  describe('DELETE', () => {
    it('should remove a committee member', async () => {
      const url = new URL('http://localhost/api/merit-review/committee/department');
      url.searchParams.append('id', '1');
      const req = new NextRequest(url, {
        method: 'DELETE',
      });
      const res = await DELETE(req);
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(data).toHaveProperty('message', 'Committee member removed successfully');
    });

    it('should return 400 if missing committee member ID', async () => {
      const req = new NextRequest('http://localhost/api/merit-review/committee/department', {
        method: 'DELETE',
      });
      const res = await DELETE(req);

      expect(res.status).toBe(400);
    });
  });
});
