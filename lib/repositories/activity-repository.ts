import { BaseRepository } from './base-repository';
import { sql } from '@/app/lib/db';
import { ActivityForm } from '@/app/lib/definitions';

export class ActivityRepository extends BaseRepository<ActivityForm> {
  constructor() {
    super('activities', 'uw');
  }

  /**
   * Fetch activities with pagination and filtering
   */
  async getFilteredActivities(query: string, page: number, pageSize: number = 10): Promise<ActivityForm[]> {
    try {
      const offset = (page - 1) * pageSize;

      const activities = await sql`
        SELECT *
        FROM uw.activities
        WHERE summary ILIKE ${'%' + query + '%'}
        ORDER BY date DESC
        LIMIT ${pageSize}
        OFFSET ${offset}
      `;

      return activities as unknown as ActivityForm[];
    } catch (error) {
      console.error('Error in ActivityRepository.getFilteredActivities:', error);
      throw new Error('Failed to fetch filtered activities');
    }
  }

  /**
   * Count total pages for pagination
   */
  async countPages(query: string, pageSize: number = 10): Promise<number> {
    try {
      const countResult = await sql`
        SELECT COUNT(*)
        FROM uw.activities
        WHERE summary ILIKE ${'%' + query + '%'}
      `;

      const totalCount = parseInt(countResult[0].count, 10);
      return Math.ceil(totalCount / pageSize);
    } catch (error) {
      console.error('Error in ActivityRepository.countPages:', error);
      throw new Error('Failed to count activity pages');
    }
  }

  /**
   * Get latest activities
   */
  async getLatestActivities(limit: number = 5): Promise<ActivityForm[]> {
    try {
      const activities = await sql`
        SELECT *
        FROM uw.activities
        ORDER BY date DESC
        LIMIT ${limit}
      `;

      return activities as unknown as ActivityForm[];
    } catch (error) {
      console.error('Error in ActivityRepository.getLatestActivities:', error);
      throw new Error('Failed to fetch latest activities');
    }
  }
}
