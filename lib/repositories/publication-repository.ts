import { BaseRepository } from './base-repository';
import { sql } from '@/app/lib/db';
import { Publications } from '@/app/lib/definitions';

export class PublicationRepository extends BaseRepository<Publications> {
  constructor() {
    super('publications', 'uw');
  }

  /**
   * Fetch publications with pagination and filtering
   */
  async getFilteredPublications(query: string, page: number, pageSize: number = 10): Promise<Publications[]> {
    try {
      const offset = (page - 1) * pageSize;

      const publications = await sql`
        SELECT *
        FROM uw.publications
        WHERE title ILIKE ${'%' + query + '%'}
        ORDER BY created_at DESC
        LIMIT ${pageSize}
        OFFSET ${offset}
      `;

      return publications as unknown as Publications[];
    } catch (error) {
      console.error('Error in PublicationRepository.getFilteredPublications:', error);
      throw new Error('Failed to fetch filtered publications');
    }
  }

  /**
   * Count total pages for pagination
   */
  async countPages(query: string, pageSize: number = 10): Promise<number> {
    try {
      const countResult = await sql`
        SELECT COUNT(*)
        FROM uw.publications
        WHERE title ILIKE ${'%' + query + '%'}
      `;

      const totalCount = parseInt(countResult[0].count, 10);
      return Math.ceil(totalCount / pageSize);
    } catch (error) {
      console.error('Error in PublicationRepository.countPages:', error);
      throw new Error('Failed to count publication pages');
    }
  }

  /**
   * Get latest publications
   */
  async getLatestPublications(limit: number = 5): Promise<Publications[]> {
    try {
      const publications = await sql`
        SELECT *
        FROM uw.publications
        ORDER BY created_at DESC
        LIMIT ${limit}
      `;

      return publications as unknown as Publications[];
    } catch (error) {
      console.error('Error in PublicationRepository.getLatestPublications:', error);
      throw new Error('Failed to fetch latest publications');
    }
  }
}
