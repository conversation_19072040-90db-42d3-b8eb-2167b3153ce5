import { sql } from '@/app/lib/db';

/**
 * Base repository class that provides common database operations
 */
export abstract class BaseRepository<T> {
  protected tableName: string;
  protected schema: string;

  constructor(tableName: string, schema: string = 'public') {
    this.tableName = tableName;
    this.schema = schema;
  }

  /**
   * Get a record by ID
   */
  async getById(id: string | number): Promise<T | null> {
    try {
      const result = await sql`
        SELECT * FROM ${sql(this.schema)}.${sql(this.tableName)}
        WHERE id = ${id}
        LIMIT 1
      `;

      return result.length > 0 ? (result[0] as unknown as T) : null;
    } catch (error) {
      console.error(`Error in ${this.tableName}.getById:`, error);
      throw new Error(`Failed to fetch ${this.tableName} by ID`);
    }
  }

  /**
   * Get all records
   */
  async getAll(): Promise<T[]> {
    try {
      const result = await sql`
        SELECT * FROM ${sql(this.schema)}.${sql(this.tableName)}
        ORDER BY id
      `;

      return result as unknown as T[];
    } catch (error) {
      console.error(`Error in ${this.tableName}.getAll:`, error);
      throw new Error(`Failed to fetch all ${this.tableName}`);
    }
  }

  /**
   * Create a new record
   */
  async create(data: Partial<T>): Promise<T> {
    try {
      const keys = Object.keys(data);
      const values = Object.values(data);

      // Dynamically build the SQL query
      const query = `
        INSERT INTO ${this.schema}.${this.tableName}
        (${keys.join(', ')})
        VALUES (${keys.map((_, i) => `$${i + 1}`).join(', ')})
        RETURNING *
      `;

      const result = await sql.unsafe(query, ...(values as any[]));
      return result[0] as unknown as T;
    } catch (error) {
      console.error(`Error in ${this.tableName}.create:`, error);
      throw new Error(`Failed to create ${this.tableName}`);
    }
  }

  /**
   * Update a record
   */
  async update(id: string | number, data: Partial<T>): Promise<T> {
    try {
      const entries = Object.entries(data);
      const setClause = entries
        .map(([key], index) => `${key} = $${index + 2}`)
        .join(', ');

      const query = `
        UPDATE ${this.schema}.${this.tableName}
        SET ${setClause}
        WHERE id = $1
        RETURNING *
      `;

      const values = [id, ...entries.map(([_, value]) => value)];
      const result = await sql.unsafe(query, ...(values as any[]));

      return result[0] as unknown as T;
    } catch (error) {
      console.error(`Error in ${this.tableName}.update:`, error);
      throw new Error(`Failed to update ${this.tableName}`);
    }
  }

  /**
   * Delete a record
   */
  async delete(id: string | number): Promise<boolean> {
    try {
      const result = await sql`
        DELETE FROM ${sql(this.schema)}.${sql(this.tableName)}
        WHERE id = ${id}
        RETURNING id
      `;

      return result.length > 0;
    } catch (error) {
      console.error(`Error in ${this.tableName}.delete:`, error);
      throw new Error(`Failed to delete ${this.tableName}`);
    }
  }

  /**
   * Count records with optional filter
   */
  async count(whereClause?: string, params?: any[]): Promise<number> {
    try {
      let query = `SELECT COUNT(*) FROM ${this.schema}.${this.tableName}`;

      if (whereClause) {
        query += ` WHERE ${whereClause}`;
      }

      const result = await sql.unsafe(query, ...(params as any[] || []));
      return parseInt(result[0].count, 10);
    } catch (error) {
      console.error(`Error in ${this.tableName}.count:`, error);
      throw new Error(`Failed to count ${this.tableName}`);
    }
  }
}
