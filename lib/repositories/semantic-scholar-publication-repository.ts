import { sql } from '@/app/lib/db';

export interface SemanticScholarPublication {
  publication_id: number;
  scholar_id: string;
  title: string;
  authors: string;
  venue: string | null;
  year: number | null;
  citations: number | null;
  publication_url: string | null;
  scholar_url: string | null;
  pdf_url: string | null;
  abstract: string | null;
  last_updated: Date;
}

export interface FacultyPublication extends SemanticScholarPublication {
  faculty_id: number;
  faculty_name: string;
  work_email: string;
}

export interface FacultySummary {
  faculty_id: number;
  faculty_name: string;
  work_email: string;
  department: string | null;
}

export class SemanticScholarPublicationRepository {
  /**
   * Get publications for a specific faculty member by email
   * @param email The faculty member's email
   * @param sortBy Field to sort by: 'year' (default) or 'citations'
   */
  async getPublicationsByEmail(email: string, sortBy: 'year' | 'citations' = 'year'): Promise<SemanticScholarPublication[]> {
    try {
      // First check if the user is a faculty member
      const facultyCheck = await sql`
        SELECT faculty_id FROM uw.faculty
        WHERE work_email = ${email} AND is_deleted = FALSE
      `;

      if (facultyCheck.length === 0) {
        console.log(`No faculty found with email: ${email}`);
        return [];
      }

      const facultyId = facultyCheck[0].faculty_id;

      // Check if the faculty has an author profile
      const authorCheck = await sql`
        SELECT author_id FROM semanticscholar.author_profile
        WHERE faculty_id = ${facultyId}
      `;

      if (authorCheck.length === 0) {
        console.log(`No author profile found for faculty ID: ${facultyId}`);
        return [];
      }

      // Get publications with dynamic sorting
      let publications;

      if (sortBy === 'citations') {
        publications = await sql`
          SELECT
            p.publication_id,
            p.scholar_id,
            p.title,
            p.authors,
            p.venue,
            p.year,
            p.citations,
            p.publication_url,
            p.scholar_url,
            p.pdf_url,
            p.abstract,
            p.last_updated
          FROM semanticscholar.publication p
          JOIN semanticscholar.author_profile ap ON p.scholar_id = ap.scholar_id
          WHERE ap.faculty_id = ${facultyId}
          ORDER BY CASE WHEN p.citations IS NULL THEN 0 ELSE 1 END DESC, p.citations DESC, CASE WHEN p.year IS NULL THEN 0 ELSE 1 END DESC, p.year DESC
        `;
      } else {
        // Default sort by year
        publications = await sql`
          SELECT
            p.publication_id,
            p.scholar_id,
            p.title,
            p.authors,
            p.venue,
            p.year,
            p.citations,
            p.publication_url,
            p.scholar_url,
            p.pdf_url,
            p.abstract,
            p.last_updated
          FROM semanticscholar.publication p
          JOIN semanticscholar.author_profile ap ON p.scholar_id = ap.scholar_id
          WHERE ap.faculty_id = ${facultyId}
          ORDER BY CASE WHEN p.year IS NULL THEN 0 ELSE 1 END DESC, p.year DESC, p.citations DESC
        `;
      }

      // Convert the raw database rows to our interface type
      return publications.map(p => ({
        publication_id: p.publication_id,
        scholar_id: p.scholar_id,
        title: p.title,
        authors: p.authors,
        venue: p.venue,
        year: p.year,
        citations: p.citations,
        publication_url: p.publication_url,
        scholar_url: p.scholar_url,
        pdf_url: p.pdf_url,
        abstract: p.abstract,
        last_updated: p.last_updated
      }));
    } catch (error) {
      console.error('Error in SemanticScholarPublicationRepository.getPublicationsByEmail:', error);
      throw new Error('Failed to fetch publications by email');
    }
  }

  /**
   * Get all faculty publications
   */
  async getAllFacultyPublications(page: number = 1, pageSize: number = 20): Promise<FacultyPublication[]> {
    try {
      const offset = (page - 1) * pageSize;

      // First, get all faculty members from the main database
      const facultyMembers = await sql`
        SELECT faculty_id, first_name, last_name, work_email
        FROM uw.faculty
        WHERE is_deleted = FALSE
      `;

      // Then, get publications from the ingest database
      const publications = await sql`
        SELECT p.*, ap.faculty_id
        FROM semanticscholar.publication p
        JOIN semanticscholar.author_profile ap ON p.scholar_id = ap.scholar_id
        ORDER BY p.last_updated DESC, p.year DESC
      `;

      // Manually join the data
      const facultyMap = new Map();
      facultyMembers.forEach(faculty => {
        facultyMap.set(faculty.faculty_id, {
          faculty_id: faculty.faculty_id,
          faculty_name: `${faculty.first_name} ${faculty.last_name}`,
          work_email: faculty.work_email
        });
      });

      // Combine publication data with faculty data
      const combinedPublications = publications.map(pub => {
        const faculty = facultyMap.get(pub.faculty_id);
        if (!faculty) return null;

        return {
          ...pub,
          faculty_id: faculty.faculty_id,
          faculty_name: faculty.faculty_name,
          work_email: faculty.work_email
        };
      }).filter(Boolean) as FacultyPublication[];

      // Apply pagination manually
      const paginatedResults = combinedPublications.slice(offset, offset + pageSize);

      return paginatedResults;
    } catch (error) {
      console.error('Error in SemanticScholarPublicationRepository.getAllFacultyPublications:', error);
      throw new Error('Failed to fetch all faculty publications');
    }
  }

  /**
   * Get faculty members list
   */
  async getFacultyList(page: number = 1, pageSize: number = 20): Promise<{ faculty: FacultySummary[], totalCount: number }> {
    try {
      // Get all faculty members from the main database
      const facultyMembers = await sql`
        SELECT f.faculty_id, f.first_name, f.last_name, f.work_email, u.full_name as department
        FROM uw.faculty f
        LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
        WHERE f.is_deleted = FALSE
        ORDER BY f.last_name, f.first_name
      `;

      // Map faculty members to FacultySummary objects
      const facultyList: FacultySummary[] = facultyMembers.map(faculty => ({
        faculty_id: faculty.faculty_id,
        faculty_name: `${faculty.first_name} ${faculty.last_name}`,
        work_email: faculty.work_email,
        department: faculty.department
      }));

      // Apply pagination
      const totalCount = facultyList.length;
      const startIndex = (page - 1) * pageSize;
      const paginatedFaculty = facultyList.slice(startIndex, startIndex + pageSize);

      return {
        faculty: paginatedFaculty,
        totalCount
      };
    } catch (error) {
      console.error('Error in SemanticScholarPublicationRepository.getFacultyList:', error);
      throw new Error('Failed to fetch faculty list');
    }
  }

  /**
   * Get publications for a specific faculty member by ID
   * @param facultyId The faculty member's ID
   * @param sortBy Field to sort by: 'year' (default) or 'citations'
   */
  async getPublicationsByFacultyId(facultyId: number, sortBy: 'year' | 'citations' = 'year'): Promise<SemanticScholarPublication[]> {
    try {
      // Check if the faculty has an author profile
      const authorCheck = await sql`
        SELECT author_id FROM semanticscholar.author_profile
        WHERE faculty_id = ${facultyId}
      `;

      if (authorCheck.length === 0) {
        console.log(`No author profile found for faculty ID: ${facultyId}`);
        return [];
      }

      // Get publications with dynamic sorting
      let publications;

      if (sortBy === 'citations') {
        publications = await sql`
          SELECT
            p.publication_id,
            p.scholar_id,
            p.title,
            p.authors,
            p.venue,
            p.year,
            p.citations,
            p.publication_url,
            p.scholar_url,
            p.pdf_url,
            p.abstract,
            p.last_updated
          FROM semanticscholar.publication p
          JOIN semanticscholar.author_profile ap ON p.scholar_id = ap.scholar_id
          WHERE ap.faculty_id = ${facultyId}
          ORDER BY CASE WHEN p.citations IS NULL THEN 0 ELSE 1 END DESC, p.citations DESC, CASE WHEN p.year IS NULL THEN 0 ELSE 1 END DESC, p.year DESC
        `;
      } else {
        // Default sort by year
        publications = await sql`
          SELECT
            p.publication_id,
            p.scholar_id,
            p.title,
            p.authors,
            p.venue,
            p.year,
            p.citations,
            p.publication_url,
            p.scholar_url,
            p.pdf_url,
            p.abstract,
            p.last_updated
          FROM semanticscholar.publication p
          JOIN semanticscholar.author_profile ap ON p.scholar_id = ap.scholar_id
          WHERE ap.faculty_id = ${facultyId}
          ORDER BY CASE WHEN p.year IS NULL THEN 0 ELSE 1 END DESC, p.year DESC, p.citations DESC
        `;
      }

      // Convert the raw database rows to our interface type
      return publications.map(p => ({
        publication_id: p.publication_id,
        scholar_id: p.scholar_id,
        title: p.title,
        authors: p.authors,
        venue: p.venue,
        year: p.year,
        citations: p.citations,
        publication_url: p.publication_url,
        scholar_url: p.scholar_url,
        pdf_url: p.pdf_url,
        abstract: p.abstract,
        last_updated: p.last_updated
      }));
    } catch (error) {
      console.error('Error in SemanticScholarPublicationRepository.getPublicationsByFacultyId:', error);
      throw new Error('Failed to fetch publications by faculty ID');
    }
  }

  /**
   * Get faculty details by ID
   */
  async getFacultyDetailsById(facultyId: number): Promise<{ faculty_name: string; department: string | null; work_email: string } | null> {
    try {
      const facultyResult = await sql`
        SELECT f.first_name, f.last_name, f.work_email, u.full_name as department
        FROM uw.faculty f
        LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
        WHERE f.faculty_id = ${facultyId} AND f.is_deleted = FALSE
      `;

      if (facultyResult.length === 0) {
        return null;
      }

      const faculty = facultyResult[0];
      return {
        faculty_name: `${faculty.first_name} ${faculty.last_name}`,
        department: faculty.department,
        work_email: faculty.work_email
      };
    } catch (error) {
      console.error('Error in SemanticScholarPublicationRepository.getFacultyDetailsById:', error);
      throw new Error('Failed to fetch faculty details');
    }
  }

  /**
   * Get total count of faculty publications
   */
  async getTotalPublicationsCount(): Promise<number> {
    try {
      // First, get all faculty IDs from the main database
      const facultyMembers = await sql`
        SELECT faculty_id
        FROM uw.faculty
        WHERE is_deleted = FALSE
      `;

      // Create an array of faculty IDs
      const facultyIds = facultyMembers.map(f => f.faculty_id);

      // If there are no faculty members, return 0
      if (facultyIds.length === 0) {
        return 0;
      }

      // Count publications for faculty members in the ingest database
      // We'll need to do this in batches to avoid query parameter limits
      const batchSize = 100;
      let totalCount = 0;

      for (let i = 0; i < facultyIds.length; i += batchSize) {
        const batchIds = facultyIds.slice(i, i + batchSize);

        // Convert to a comma-separated string for the IN clause
        const facultyIdsStr = batchIds.join(',');

        // Use a raw query with string interpolation for the IN clause
        // This is safe because we're generating the IDs from our own database query
        const result = await sql.unsafe(`
          SELECT COUNT(*) as total
          FROM semanticscholar.publication p
          JOIN semanticscholar.author_profile ap ON p.scholar_id = ap.scholar_id
          WHERE ap.faculty_id IN (${facultyIdsStr})
        `);

        totalCount += parseInt(result[0].total);
      }

      return totalCount;
    } catch (error) {
      console.error('Error in SemanticScholarPublicationRepository.getTotalPublicationsCount:', error);
      throw new Error('Failed to fetch total publications count');
    }
  }

  /**
   * Get publications count for a specific faculty member by email
   */
  async getPublicationsCountByEmail(email: string): Promise<number> {
    try {
      // First check if the user is a faculty member
      const facultyCheck = await sql`
        SELECT faculty_id FROM uw.faculty
        WHERE work_email = ${email} AND is_deleted = FALSE
      `;

      if (facultyCheck.length === 0) {
        console.log(`No faculty found with email: ${email}`);
        return 0;
      }

      const facultyId = facultyCheck[0].faculty_id;

      // Check if the faculty has an author profile
      const authorCheck = await sql`
        SELECT author_id FROM semanticscholar.author_profile
        WHERE faculty_id = ${facultyId}
      `;

      if (authorCheck.length === 0) {
        console.log(`No author profile found for faculty ID: ${facultyId}`);
        return 0;
      }

      // Get count
      const result = await sql`
        SELECT COUNT(*) as total
        FROM semanticscholar.publication p
        JOIN semanticscholar.author_profile ap ON p.scholar_id = ap.scholar_id
        WHERE ap.faculty_id = ${facultyId}
      `;

      return parseInt(result[0].total);
    } catch (error) {
      console.error('Error in SemanticScholarPublicationRepository.getPublicationsCountByEmail:', error);
      throw new Error('Failed to fetch publications count by email');
    }
  }
}
