/**
 * Accessibility testing utilities for development and testing
 */

import { colorContrast } from './accessibility';

/**
 * Run accessibility checks on the current page
 */
export async function runAccessibilityChecks(): Promise<AccessibilityReport> {
  const report: AccessibilityReport = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    issues: [],
    warnings: [],
    passed: [],
  };

  // Check for missing alt text
  checkAltText(report);
  
  // Check for proper heading hierarchy
  checkHeadingHierarchy(report);
  
  // Check for form labels
  checkFormLabels(report);
  
  // Check for color contrast (basic check)
  checkBasicColorContrast(report);
  
  // Check for keyboard accessibility
  checkKeyboardAccessibility(report);
  
  // Check for ARIA attributes
  checkAriaAttributes(report);

  return report;
}

interface AccessibilityReport {
  timestamp: string;
  url: string;
  issues: AccessibilityIssue[];
  warnings: AccessibilityIssue[];
  passed: AccessibilityCheck[];
}

interface AccessibilityIssue {
  type: 'error' | 'warning';
  rule: string;
  message: string;
  element?: string;
  selector?: string;
}

interface AccessibilityCheck {
  rule: string;
  message: string;
  count?: number;
}

function checkAltText(report: AccessibilityReport) {
  const images = document.querySelectorAll('img');
  let missingAlt = 0;
  let decorativeImages = 0;
  let properAlt = 0;

  images.forEach((img, index) => {
    const alt = img.getAttribute('alt');
    const ariaHidden = img.getAttribute('aria-hidden');
    
    if (ariaHidden === 'true') {
      decorativeImages++;
    } else if (alt === null) {
      missingAlt++;
      report.issues.push({
        type: 'error',
        rule: 'img-alt',
        message: 'Image missing alt attribute',
        selector: `img:nth-of-type(${index + 1})`,
      });
    } else if (alt === '') {
      decorativeImages++;
    } else {
      properAlt++;
    }
  });

  if (missingAlt === 0) {
    report.passed.push({
      rule: 'img-alt',
      message: `All ${images.length} images have proper alt attributes`,
      count: images.length,
    });
  }
}

function checkHeadingHierarchy(report: AccessibilityReport) {
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let previousLevel = 0;
  let hasH1 = false;
  let multipleH1 = false;
  let h1Count = 0;

  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    
    if (level === 1) {
      h1Count++;
      hasH1 = true;
      if (h1Count > 1) {
        multipleH1 = true;
      }
    }

    if (previousLevel > 0 && level > previousLevel + 1) {
      report.issues.push({
        type: 'error',
        rule: 'heading-hierarchy',
        message: `Heading level skipped from h${previousLevel} to h${level}`,
        selector: `${heading.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
      });
    }

    previousLevel = level;
  });

  if (!hasH1) {
    report.issues.push({
      type: 'error',
      rule: 'page-has-heading-one',
      message: 'Page should have exactly one h1 element',
    });
  }

  if (multipleH1) {
    report.warnings.push({
      type: 'warning',
      rule: 'page-has-heading-one',
      message: 'Page has multiple h1 elements',
    });
  }

  if (hasH1 && !multipleH1) {
    report.passed.push({
      rule: 'page-has-heading-one',
      message: 'Page has proper h1 structure',
    });
  }
}

function checkFormLabels(report: AccessibilityReport) {
  const inputs = document.querySelectorAll('input, select, textarea');
  let unlabeledInputs = 0;

  inputs.forEach((input, index) => {
    const id = input.getAttribute('id');
    const ariaLabel = input.getAttribute('aria-label');
    const ariaLabelledBy = input.getAttribute('aria-labelledby');
    const type = input.getAttribute('type');
    
    // Skip hidden inputs
    if (type === 'hidden') return;

    let hasLabel = false;

    if (id) {
      const label = document.querySelector(`label[for="${id}"]`);
      if (label) hasLabel = true;
    }

    if (ariaLabel || ariaLabelledBy) {
      hasLabel = true;
    }

    if (!hasLabel) {
      unlabeledInputs++;
      report.issues.push({
        type: 'error',
        rule: 'label',
        message: 'Form element missing accessible label',
        selector: `${input.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
      });
    }
  });

  if (unlabeledInputs === 0 && inputs.length > 0) {
    report.passed.push({
      rule: 'label',
      message: `All ${inputs.length} form elements have proper labels`,
      count: inputs.length,
    });
  }
}

function checkBasicColorContrast(report: AccessibilityReport) {
  // This is a basic check - for comprehensive testing, use axe-core
  const textElements = document.querySelectorAll('p, span, div, a, button, h1, h2, h3, h4, h5, h6');
  let contrastIssues = 0;

  textElements.forEach((element) => {
    const styles = window.getComputedStyle(element);
    const color = styles.color;
    const backgroundColor = styles.backgroundColor;
    
    // Skip if no background color or transparent
    if (!backgroundColor || backgroundColor === 'rgba(0, 0, 0, 0)' || backgroundColor === 'transparent') {
      return;
    }

    // Basic check for common problematic combinations
    if (color === 'rgb(128, 128, 128)' && backgroundColor === 'rgb(255, 255, 255)') {
      contrastIssues++;
      report.warnings.push({
        type: 'warning',
        rule: 'color-contrast',
        message: 'Potential color contrast issue detected',
        element: element.tagName.toLowerCase(),
      });
    }
  });

  if (contrastIssues === 0) {
    report.passed.push({
      rule: 'color-contrast',
      message: 'No obvious color contrast issues detected',
    });
  }
}

function checkKeyboardAccessibility(report: AccessibilityReport) {
  const interactiveElements = document.querySelectorAll('a, button, input, select, textarea, [tabindex]');
  let keyboardIssues = 0;

  interactiveElements.forEach((element) => {
    const tabIndex = element.getAttribute('tabindex');
    
    // Check for positive tabindex (anti-pattern)
    if (tabIndex && parseInt(tabIndex) > 0) {
      keyboardIssues++;
      report.warnings.push({
        type: 'warning',
        rule: 'tabindex',
        message: 'Positive tabindex detected - can disrupt natural tab order',
        element: element.tagName.toLowerCase(),
      });
    }
  });

  if (keyboardIssues === 0) {
    report.passed.push({
      rule: 'tabindex',
      message: 'No problematic tabindex values detected',
    });
  }
}

function checkAriaAttributes(report: AccessibilityReport) {
  const elementsWithAria = document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]');
  let ariaIssues = 0;

  elementsWithAria.forEach((element) => {
    const ariaLabelledBy = element.getAttribute('aria-labelledby');
    const ariaDescribedBy = element.getAttribute('aria-describedby');

    // Check if referenced elements exist
    if (ariaLabelledBy) {
      const referencedElement = document.getElementById(ariaLabelledBy);
      if (!referencedElement) {
        ariaIssues++;
        report.issues.push({
          type: 'error',
          rule: 'aria-valid-attr-value',
          message: `aria-labelledby references non-existent element: ${ariaLabelledBy}`,
          element: element.tagName.toLowerCase(),
        });
      }
    }

    if (ariaDescribedBy) {
      const referencedElement = document.getElementById(ariaDescribedBy);
      if (!referencedElement) {
        ariaIssues++;
        report.issues.push({
          type: 'error',
          rule: 'aria-valid-attr-value',
          message: `aria-describedby references non-existent element: ${ariaDescribedBy}`,
          element: element.tagName.toLowerCase(),
        });
      }
    }
  });

  if (ariaIssues === 0 && elementsWithAria.length > 0) {
    report.passed.push({
      rule: 'aria-valid-attr-value',
      message: `All ${elementsWithAria.length} ARIA attributes have valid references`,
      count: elementsWithAria.length,
    });
  }
}

/**
 * Log accessibility report to console in development
 */
export function logAccessibilityReport(report: AccessibilityReport) {
  if (process.env.NODE_ENV !== 'development') return;

  console.group('🔍 Accessibility Report');
  console.log('URL:', report.url);
  console.log('Timestamp:', report.timestamp);
  
  if (report.issues.length > 0) {
    console.group('❌ Issues');
    report.issues.forEach(issue => {
      console.error(`${issue.rule}: ${issue.message}`, issue.selector || issue.element || '');
    });
    console.groupEnd();
  }

  if (report.warnings.length > 0) {
    console.group('⚠️ Warnings');
    report.warnings.forEach(warning => {
      console.warn(`${warning.rule}: ${warning.message}`, warning.selector || warning.element || '');
    });
    console.groupEnd();
  }

  if (report.passed.length > 0) {
    console.group('✅ Passed');
    report.passed.forEach(check => {
      console.log(`${check.rule}: ${check.message}`);
    });
    console.groupEnd();
  }

  console.groupEnd();
}

/**
 * Run accessibility checks on page load in development
 */
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.addEventListener('load', async () => {
    // Wait a bit for dynamic content to load
    setTimeout(async () => {
      const report = await runAccessibilityChecks();
      logAccessibilityReport(report);
    }, 1000);
  });
}
