'use client';

import { create } from 'zustand';
import { Node, Edge } from 'reactflow';
import { NodeType } from '@/components/workflow/CustomNode';

export type NodeStatus = 'success' | 'failed' | 'in_progress' | 'pending';

export type WorkflowNode = Node & {
  data: {
    label: string;
    type: NodeType;
    status: NodeStatus;
    description?: string;
    config?: Record<string, any>;
  };
};

interface WorkflowState {
  nodes: WorkflowNode[];
  edges: Edge[];
  selectedNode: string | null;
  addNode: (node: WorkflowNode) => void;
  updateNode: (nodeId: string, data: Partial<WorkflowNode['data']>) => void;
  deleteNode: (nodeId: string) => void;
  addEdge: (edge: Edge) => void;
  deleteEdge: (edgeId: string) => void;
  setSelectedNode: (nodeId: string | null) => void;
  initializeSampleWorkflow: () => void;
  setNodes: (nodes: WorkflowNode[]) => void;
}

export const useWorkflowStore = create<WorkflowState>((set) => ({
  nodes: [],
  edges: [],
  selectedNode: null,
  setNodes: (nodes) => set({ nodes }),
  addNode: (node) => set((state) => ({ nodes: [...state.nodes, node] })),
  updateNode: (nodeId, data) =>
    set((state) => ({
      nodes: state.nodes.map((node) =>
        node.id === nodeId ? { ...node, data: { ...node.data, ...data } } : node
      ),
    })),
  deleteNode: (nodeId) =>
    set((state) => ({
      nodes: state.nodes.filter((node) => node.id !== nodeId),
      edges: state.edges.filter(
        (edge) => edge.source !== nodeId && edge.target !== nodeId
      ),
    })),
  addEdge: (edge) => set((state) => ({ edges: [...state.edges, edge] })),
  deleteEdge: (edgeId) =>
    set((state) => ({
      edges: state.edges.filter((edge) => edge.id !== edgeId),
    })),
  setSelectedNode: (nodeId) => set({ selectedNode: nodeId }),
  initializeSampleWorkflow: () => {
    const nodes: WorkflowNode[] = [
      {
        id: '1',
        type: 'custom',
        position: { x: 50, y: 50 },
        data: {
          label: 'Process Announcement',
          type: 'start',
          status: 'success',
          description: 'Faculty admin announces process and timeline'
        }
      },
      {
        id: '2',
        type: 'custom',
        position: { x: 250, y: 50 },
        data: {
          label: 'Faculty Preparation',
          type: 'process',
          status: 'in_progress',
          description: 'Faculty members prepare submissions according to guidelines'
        }
      },
      {
        id: '3',
        type: 'custom',
        position: { x: 450, y: 50 },
        data: {
          label: 'Committee Review',
          type: 'review',
          status: 'pending',
          description: 'Merit reports reviewed by Department Committee'
        }
      },
      {
        id: '4',
        type: 'custom',
        position: { x: 650, y: 50 },
        data: {
          label: 'Submit Ratings',
          type: 'submission',
          status: 'failed',
          description: 'Preliminary ratings submitted to Dean\'s Office staff'
        }
      },
      {
        id: '5',
        type: 'custom',
        position: { x: 850, y: 50 },
        data: {
          label: 'Chairs Review',
          type: 'review',
          status: 'success',
          description: 'Chairs & Directors meet with Dean to review ratings'
        }
      },
      {
        id: '6',
        type: 'custom',
        position: { x: 1050, y: 50 },
        data: {
          label: 'Summary Report',
          type: 'document',
          status: 'pending',
          description: 'One-page summary report required for each faculty member'
        }
      },
      {
        id: '7',
        type: 'custom',
        position: { x: 1250, y: 50 },
        data: {
          label: 'Dean-Provost Review',
          type: 'review',
          status: 'in_progress',
          description: 'Dean meets with Provost to finalize ratings'
        }
      },
      {
        id: '8',
        type: 'custom',
        position: { x: 1450, y: 50 },
        data: {
          label: 'Finalization Notice',
          type: 'notification',
          status: 'success',
          description: 'Dean\'s Office advises units of finalized ratings'
        }
      },
      {
        id: '9',
        type: 'custom',
        position: { x: 1650, y: 0 },
        data: {
          label: 'Change Requests',
          type: 'request',
          status: 'failed',
          description: 'Requests for rating changes submitted to Dean'
        }
      },
      {
        id: '10',
        type: 'custom',
        position: { x: 1650, y: 100 },
        data: {
          label: 'HR Processing',
          type: 'end',
          status: 'pending',
          description: 'Finalized ratings reported to HR for merit pay processing'
        }
      }
    ];

    const edges: Edge[] = [
      { id: 'e1-2', source: '1', target: '2', type: 'smoothstep' },
      { id: 'e2-3', source: '2', target: '3', type: 'smoothstep' },
      { id: 'e3-4', source: '3', target: '4', type: 'smoothstep' },
      { id: 'e4-5', source: '4', target: '5', type: 'smoothstep' },
      { id: 'e5-6', source: '5', target: '6', type: 'smoothstep' },
      { id: 'e6-7', source: '6', target: '7', type: 'smoothstep' },
      { id: 'e7-8', source: '7', target: '8', type: 'smoothstep' },
      { id: 'e8-9', source: '8', target: '9', type: 'smoothstep' },
      { id: 'e8-10', source: '8', target: '10', type: 'smoothstep' },
      { id: 'e9-10', source: '9', target: '10', type: 'smoothstep' }
    ];

    set({ nodes, edges });
  }
})); 