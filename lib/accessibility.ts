/**
 * Accessibility utilities for WCAG 2.0 AA compliance
 */

/**
 * Generate a unique ID for form elements and their labels
 */
export function generateId(prefix: string = 'element'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Check if an element should be announced to screen readers
 */
export function shouldAnnounce(element: HTMLElement): boolean {
  return !element.hasAttribute('aria-hidden') && 
         !element.closest('[aria-hidden="true"]');
}

/**
 * Create accessible error message attributes
 */
export function createErrorAttributes(fieldId: string, hasError: boolean) {
  const errorId = `${fieldId}-error`;
  return {
    'aria-invalid': hasError ? 'true' : 'false',
    'aria-describedby': hasError ? errorId : undefined,
    errorId,
  };
}

/**
 * Create accessible loading state attributes
 */
export function createLoadingAttributes(isLoading: boolean, loadingText: string = 'Loading') {
  return {
    'aria-busy': isLoading ? 'true' : 'false',
    'aria-live': 'polite' as const,
    'aria-label': isLoading ? loadingText : undefined,
  };
}

/**
 * Create accessible button attributes for toggle states
 */
export function createToggleAttributes(isExpanded: boolean, controlsId?: string) {
  return {
    'aria-expanded': isExpanded ? 'true' : 'false',
    'aria-controls': controlsId,
  };
}

/**
 * Create accessible table attributes
 */
export function createTableAttributes(caption: string, ariaLabel?: string) {
  return {
    role: 'table' as const,
    'aria-label': ariaLabel,
    caption,
  };
}

/**
 * Create accessible navigation attributes
 */
export function createNavAttributes(label: string, current?: boolean) {
  return {
    role: 'navigation' as const,
    'aria-label': label,
    'aria-current': current ? 'page' : undefined,
  };
}

/**
 * Announce message to screen readers
 */
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

/**
 * Focus management utilities
 */
export const focusManagement = {
  /**
   * Set focus to element and scroll into view if needed
   */
  setFocus(element: HTMLElement | null, scrollIntoView: boolean = true) {
    if (!element) return;
    
    element.focus();
    if (scrollIntoView) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  },

  /**
   * Focus first focusable element in container
   */
  focusFirst(container: HTMLElement) {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length > 0) {
      this.setFocus(focusableElements[0]);
    }
  },

  /**
   * Get all focusable elements in container
   */
  getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[];
  },

  /**
   * Trap focus within container (for modals, dropdowns, etc.)
   */
  trapFocus(container: HTMLElement, event: KeyboardEvent) {
    if (event.key !== 'Tab') return;

    const focusableElements = this.getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      // Shift + Tab
      if (document.activeElement === firstElement) {
        event.preventDefault();
        this.setFocus(lastElement);
      }
    } else {
      // Tab
      if (document.activeElement === lastElement) {
        event.preventDefault();
        this.setFocus(firstElement);
      }
    }
  }
};

/**
 * Color contrast utilities
 */
export const colorContrast = {
  /**
   * Calculate relative luminance of a color
   */
  getLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },

  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
    const lum1 = this.getLuminance(...color1);
    const lum2 = this.getLuminance(...color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },

  /**
   * Check if color combination meets WCAG AA standards
   */
  meetsWCAGAA(foreground: [number, number, number], background: [number, number, number], isLargeText: boolean = false): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    return isLargeText ? ratio >= 3 : ratio >= 4.5;
  }
};

/**
 * Keyboard navigation utilities
 */
export const keyboardNavigation = {
  /**
   * Handle arrow key navigation in lists
   */
  handleArrowNavigation(event: KeyboardEvent, items: HTMLElement[], currentIndex: number): number {
    let newIndex = currentIndex;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        newIndex = (currentIndex + 1) % items.length;
        break;
      case 'ArrowUp':
        event.preventDefault();
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
        break;
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        newIndex = items.length - 1;
        break;
    }

    if (newIndex !== currentIndex) {
      focusManagement.setFocus(items[newIndex]);
    }

    return newIndex;
  }
};
