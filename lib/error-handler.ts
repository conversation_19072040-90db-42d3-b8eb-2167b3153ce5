/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  statusCode: number;
  
  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.name = 'ApiError';
  }
}

/**
 * Handles errors in API routes
 */
export function handleApiError(error: unknown) {
  console.error('API Error:', error);
  
  if (error instanceof ApiError) {
    return {
      error: error.message,
      status: error.statusCode
    };
  }
  
  if (error instanceof Error) {
    return {
      error: error.message,
      status: 500
    };
  }
  
  return {
    error: 'An unknown error occurred',
    status: 500
  };
}

/**
 * Creates a not found error
 */
export function notFound(message: string = 'Resource not found') {
  return new ApiError(message, 404);
}

/**
 * Creates an unauthorized error
 */
export function unauthorized(message: string = 'Unauthorized') {
  return new ApiError(message, 401);
}

/**
 * Creates a forbidden error
 */
export function forbidden(message: string = 'Forbidden') {
  return new ApiError(message, 403);
}

/**
 * Creates a bad request error
 */
export function badRequest(message: string = 'Bad request') {
  return new ApiError(message, 400);
}

/**
 * Creates a server error
 */
export function serverError(message: string = 'Internal server error') {
  return new ApiError(message, 500);
}
