# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
/raw_data
/dbdump
/dataimport/google_scholar_results
/tmp
# dependencies
/node_modules
/scripts/node_modules
/scripts/screenshots
/scripts/semantic_scholar_results
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.env.temp

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.cursor/*

TASKS.md
.aider*
