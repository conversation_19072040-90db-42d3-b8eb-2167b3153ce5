import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

// Define role-based access control for routes
// Order matters - more specific routes should come first
const routePermissions: Record<string, string[]> = {
  // Merit Review sub-routes
  '/dashboard/merit-review/submission/v2/admin': ['faculty_admin', 'department_admin', 'system_admin', 'regular_user'],
  '/dashboard/merit-review/submission/v2': ['faculty_admin', 'department_admin', 'department_support', 'department_approver', 'faculty_approver', 'division_approver', 'regular_user', 'system_admin'],
  '/dashboard/merit-review/submission': ['faculty_admin', 'department_admin', 'department_support', 'department_approver', 'faculty_approver', 'division_approver', 'regular_user', 'system_admin'],
  '/dashboard/merit-review/submissions': ['faculty_admin', 'department_admin', 'system_admin'],
  '/dashboard/merit-review/course-evaluations': ['faculty_admin', 'department_admin', 'department_support', 'department_approver', 'faculty_approver', 'division_approver', 'regular_user', 'system_admin'],
  '/dashboard/merit-review/committee': ['faculty_admin', 'department_admin', 'department_support', 'department_approver', 'faculty_approver', 'division_approver', 'regular_user', 'system_admin'],
  '/dashboard/merit-review/committee/ratings': ['faculty_admin', 'department_admin', 'department_support', 'department_approver', 'faculty_approver', 'division_approver', 'regular_user', 'system_admin'],
  '/dashboard/merit-review/committee/manage': ['faculty_admin', 'department_admin', 'department_support', 'department_approver', 'faculty_approver', 'division_approver', 'system_admin'],
  '/dashboard/merit-review/preliminary-ratings': ['faculty_admin', 'department_admin', 'department_approver', 'faculty_approver', 'system_admin'],
  '/dashboard/merit-review/configuration': ['faculty_admin', 'department_admin', 'system_admin'],
  '/dashboard/merit-review/configuration/create': ['faculty_admin', 'department_admin', 'system_admin'],
  '/dashboard/merit-review/configuration/edit': ['faculty_admin', 'department_admin', 'system_admin'],
  '/dashboard/merit-review/configuration/view': ['faculty_admin', 'department_admin', 'system_admin'],
  '/dashboard/merit-review/configuration/list': ['faculty_admin', 'department_admin', 'system_admin'],

  // Main routes
  '/dashboard/merit-review': ['faculty_admin', 'department_admin', 'department_support', 'department_approver', 'faculty_approver', 'division_approver', 'regular_user', 'system_admin'],
  '/dashboard/sys_admin': ['system_admin', 'faculty_admin'],
  '/dashboard/profile': ['institutional_admin', 'system_admin', 'regular_user', 'faculty_admin', 'department_admin'],
  '/dashboard/publications': ['institutional_admin', 'system_admin', 'faculty_admin', 'department_admin', 'regular_user'],
  '/dashboard/activities': ['institutional_admin', 'system_admin', 'faculty_admin', 'department_admin', 'regular_user'],
  '/dashboard/activity/publications': ['institutional_admin', 'system_admin', 'regular_user', 'faculty_admin', 'department_admin'],
  '/dashboard/upload': ['institutional_admin', 'system_admin', 'regular_user', 'faculty_admin', 'department_admin'],
};

/**
 * Middleware function to handle authentication and authorization
 */
export async function middleware(req: NextRequest) {
  const pathname = req.nextUrl.pathname;

  // Skip middleware for public routes
  if (pathname === '/login' || pathname === '/' || pathname.startsWith('/_next') || pathname.startsWith('/api/auth')) {
    return NextResponse.next();
  }

  // Get the JWT token from the request
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

  // If no token is found, redirect to login
  if (!token) {
    const url = new URL('/login', req.url);
    url.searchParams.set('callbackUrl', encodeURI(pathname));
    return NextResponse.redirect(url);
  }

  const roles = (token.roles as string[]) || [];

  // Check permissions for specific routes
  for (const [route, allowedRoles] of Object.entries(routePermissions)) {
    if (pathname.startsWith(route)) {
      const hasPermission = allowedRoles.some(role => roles.includes(role));

      if (!hasPermission) {
        return NextResponse.redirect(new URL('/dashboard', req.url));
      }

      break;
    }
  }

  // Check if the path starts with /dashboard/engrecords
  if (pathname.startsWith('/dashboard/engrecords')) {
    // If no token or user is not a system admin, institution admin, or faculty admin, redirect to dashboard
    if (!token || (!token.roles?.includes('system_admin') && !token.roles?.includes('institution_admin') && !token.roles?.includes('faculty_admin'))) {
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (API routes for authentication)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',
  ],
};